"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Wordlist = exports.version = exports.wordlists = exports.utils = exports.logger = exports.errors = exports.constants = exports.FixedNumber = exports.BigNumber = exports.ContractFactory = exports.Contract = exports.BaseContract = exports.providers = exports.getDefaultProvider = exports.VoidSigner = exports.Wallet = exports.Signer = void 0;
var contracts_1 = require("@ethersproject/contracts");
Object.defineProperty(exports, "BaseContract", { enumerable: true, get: function () { return contracts_1.BaseContract; } });
Object.defineProperty(exports, "Contract", { enumerable: true, get: function () { return contracts_1.Contract; } });
Object.defineProperty(exports, "ContractFactory", { enumerable: true, get: function () { return contracts_1.ContractFactory; } });
var bignumber_1 = require("@ethersproject/bignumber");
Object.defineProperty(exports, "BigNumber", { enumerable: true, get: function () { return bignumber_1.BigNumber; } });
Object.defineProperty(exports, "FixedNumber", { enumerable: true, get: function () { return bignumber_1.FixedNumber; } });
var abstract_signer_1 = require("@ethersproject/abstract-signer");
Object.defineProperty(exports, "Signer", { enumerable: true, get: function () { return abstract_signer_1.Signer; } });
Object.defineProperty(exports, "VoidSigner", { enumerable: true, get: function () { return abstract_signer_1.VoidSigner; } });
var wallet_1 = require("@ethersproject/wallet");
Object.defineProperty(exports, "Wallet", { enumerable: true, get: function () { return wallet_1.Wallet; } });
var constants = __importStar(require("@ethersproject/constants"));
exports.constants = constants;
var providers = __importStar(require("@ethersproject/providers"));
exports.providers = providers;
var providers_1 = require("@ethersproject/providers");
Object.defineProperty(exports, "getDefaultProvider", { enumerable: true, get: function () { return providers_1.getDefaultProvider; } });
var wordlists_1 = require("@ethersproject/wordlists");
Object.defineProperty(exports, "Wordlist", { enumerable: true, get: function () { return wordlists_1.Wordlist; } });
Object.defineProperty(exports, "wordlists", { enumerable: true, get: function () { return wordlists_1.wordlists; } });
var utils = __importStar(require("./utils"));
exports.utils = utils;
var logger_1 = require("@ethersproject/logger");
Object.defineProperty(exports, "errors", { enumerable: true, get: function () { return logger_1.ErrorCode; } });
////////////////////////
// Compile-Time Constants
// This is generated by "npm run dist"
var _version_1 = require("./_version");
Object.defineProperty(exports, "version", { enumerable: true, get: function () { return _version_1.version; } });
var logger = new logger_1.Logger(_version_1.version);
exports.logger = logger;
//# sourceMappingURL=ethers.js.map