{"version": 3, "file": "ethers.js", "sourceRoot": "", "sources": ["../src.ts/ethers.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAEnF,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAElE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,gCAAgC,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAE/C,OAAO,KAAK,SAAS,MAAM,0BAA0B,CAAC;AAEtD,OAAO,KAAK,SAAS,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAE9D,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAC,MAAM,0BAA0B,CAAC;AAE9D,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAEjC,OAAO,EAAE,SAAS,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAWpE,wBAAwB;AACxB,yBAAyB;AAEzB,sCAAsC;AACtC,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAErC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAuBnC,wBAAwB;AACxB,UAAU;AAEV,OAAO,EACH,MAAM,EAEN,MAAM,EACN,UAAU,EAEV,kBAAkB,EAClB,SAAS,EAET,YAAY,EACZ,QAAQ,EACR,eAAe,EAEf,SAAS,EACT,WAAW,EAEX,SAAS,EACT,MAAM,EAEN,MAAM,EAEN,KAAK,EAEL,SAAS;AAGT,wBAAwB;AACxB,yBAAyB;AAEzB,OAAO,EAiCP,QAAQ,EACX,CAAC"}