/**
 * @file A `<PERSON>ttice` must satisfy the following in addition to `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>` and `MeetS<PERSON><PERSON>ttice` laws:
 *
 * - Absorbtion law for meet: `a ∧ (a ∨ b) == a`
 * - Absorbtion law for join: `a ∨ (a ∧ b) == a`
 */
import { Join<PERSON><PERSON><PERSON>tti<PERSON> } from './JoinSemilattice';
import { MeetSemilattice } from './MeetSemilattice';
/**
 * @since 1.4.0
 */
export interface Lattice<A> extends JoinSemilattice<A>, MeetSemilattice<A> {
}
