/**
 * @file A join-semilattice (or upper semilattice) is a semilattice whose operation is called `join`, and which can be thought
 * of as a least upper bound.
 *
 * A `<PERSON>in<PERSON><PERSON><PERSON><PERSON><PERSON>` must satisfy the following laws:
 *
 * - Associativity: `a ∨ (b ∨ c) = (a ∨ b) ∨ c`
 * - Commutativity: `a ∨ b = b ∨ a`
 * - Idempotency:   `a ∨ a = a`
 *
 */
/**
 * @since 1.4.0
 */
export interface JoinSemilattice<A> {
    readonly join: (x: A, y: A) => A;
}
