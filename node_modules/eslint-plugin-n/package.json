{"name": "eslint-plugin-n", "version": "16.6.2", "description": "Additional ESLint's rules for Node.js", "engines": {"node": ">=16.0.0"}, "main": "lib/index.js", "files": ["lib/", "configs/"], "peerDependencies": {"eslint": ">=7.0.0"}, "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "builtins": "^5.0.1", "eslint-plugin-es-x": "^7.5.0", "get-tsconfig": "^4.7.0", "globals": "^13.24.0", "ignore": "^5.2.4", "is-builtin-module": "^3.2.1", "is-core-module": "^2.12.1", "minimatch": "^3.1.2", "resolve": "^1.22.2", "semver": "^7.5.3"}, "devDependencies": {"@eslint/js": "^8.43.0", "@types/eslint": "^8.44.6", "@typescript-eslint/parser": "^5.60.0", "esbuild": "^0.18.7", "eslint": "^8.56.0", "eslint-config-prettier": "^8.8.0", "eslint-doc-generator": "^1.6.1", "eslint-plugin-eslint-plugin": "^5.2.1", "eslint-plugin-n": "file:.", "fast-glob": "^3.2.12", "husky": "^8.0.3", "import-meta-resolve": "^3.0.0", "lint-staged": "^13.2.2", "markdownlint-cli": "^0.35.0", "mocha": "^10.2.0", "npm-run-all2": "^6.1.1", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.8.8", "punycode": "^2.3.0", "release-it": "^15.11.0", "rimraf": "^5.0.1", "typescript": "^5.1.3"}, "scripts": {"build": "node scripts/update", "clean": "rimraf .nyc_output coverage", "coverage": "opener ./coverage/lcov-report/index.html", "format": "prettier --loglevel warn --write \"**/*.{js,css,md}\"", "lint": "npm-run-all \"lint:*\"", "lint:docs": "markdownlint \"**/*.md\"", "lint:eslint-docs": "npm run update:eslint-docs -- --check", "lint:js": "eslint .", "new": "node scripts/new-rule", "postversion": "git push && git push --tags", "prepare": "npx husky install", "pretest": "npm run -s lint", "preversion": "npm test", "test": "nyc npm run -s test:_mocha", "test:_mocha": "_mocha \"tests/lib/**/*.js\" --reporter progress --timeout 4000", "test:ci": "nyc npm run -s test:_mocha", "update:eslint-docs": "eslint-doc-generator", "version": "npm run -s build && eslint lib/rules --fix && git add .", "watch": "npm run test:_mocha -- --watch --growl"}, "repository": {"type": "git", "url": "git+https://github.com/eslint-community/eslint-plugin-n.git"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "node", "nodejs", "ecmascript", "shebang", "file", "path", "import", "require"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/eslint-community/eslint-plugin-n/issues"}, "homepage": "https://github.com/eslint-community/eslint-plugin-n#readme", "funding": "https://github.com/sponsors/mysticatea", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "release-it": {"github": {"release": true}, "npm": {"skipChecks": true}}, "lint-staged": {"*.js": "eslint --cache --fix", "*.{json,js}": "prettier --write --ignore-path .es<PERSON><PERSON><PERSON>"}}