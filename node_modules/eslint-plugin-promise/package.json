{"name": "eslint-plugin-promise", "version": "6.6.0", "description": "Enforce best practices for JavaScript promises", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "promise", "promises"], "homepage": "https://github.com/eslint-community/eslint-plugin-promise", "bugs": "https://github.com/eslint-community/eslint-plugin-promise/issues", "repository": {"type": "git", "url": "https://github.com/eslint-community/eslint-plugin-promise"}, "license": "ISC", "author": "jden <<EMAIL>>", "contributors": ["<PERSON>", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://aadit.codes/)"], "scripts": {"format": "prettier --write . && eslint . --fix", "lint": "npm-run-all \"lint:*\"", "lint:eslint-docs": "npm run update:eslint-docs && git diff --exit-code", "lint:js": "eslint --report-unused-disable-directives .", "prepare": "husky install", "test": "jest --coverage", "update:eslint-docs": "eslint-doc-generator && npm run format"}, "lint-staged": {"{README.md,CONTRIBUTING.md}": ["doctoc --maxlevel 3 --notitle"], "*.js": ["prettier --write", "eslint --report-unused-disable-directives --fix"], "*.+(json|md)": ["prettier --write"]}, "prettier": {"proseWrap": "always", "semi": false, "singleQuote": true}, "jest": {"coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "collectCoverageFrom": ["rules/*.js", "rules/*/*.js", "!rules/lib/eslint-compat.js"], "testPathIgnorePatterns": ["__tests__/rule-tester.js"]}, "devDependencies": {"@typescript-eslint/parser": "^5.45.0", "doctoc": "^2.2.1", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-doc-generator": "^1.7.1", "eslint-plugin-eslint-plugin": "^4.4.1", "eslint-plugin-jest": "^28.6.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "globals": "^14.0.0", "husky": "^7.0.4", "jest": "^28.1.3", "lint-staged": "^15.2.7", "npm-run-all": "^4.1.5", "prettier": "^2.7.1", "typescript": "^4.9.3"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0 || ^9.0.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}