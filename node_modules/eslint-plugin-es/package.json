{"name": "eslint-plugin-es", "version": "3.0.1", "description": "ESLint plugin about ECMAScript syntactic features.", "engines": {"node": ">=8.10.0"}, "main": "lib/index.js", "files": ["lib"], "peerDependencies": {"eslint": ">=4.19.1"}, "dependencies": {"eslint-utils": "^2.0.0", "regexpp": "^3.0.0"}, "devDependencies": {"@mysticatea/eslint-plugin": "^11.0.0", "@vuepress/plugin-pwa": "^1.2.0", "acorn": "^7.1.0", "babel-eslint": "^10.0.1", "codecov": "^3.5.0", "eslint": "^6.2.2", "eslint4b": "^6.2.2", "espree": "^7.0.0", "globals": "^12.0.0", "mocha": "^6.2.0", "npm-run-all": "^4.1.5", "nyc": "^14.1.1", "opener": "^1.5.1", "rimraf": "^3.0.0", "semver": "^6.3.0", "vue-eslint-editor": "^0.2.0", "vuepress": "^1.2.0"}, "scripts": {"clean": "rimraf .nyc_output coverage", "codecov": "nyc report -r lcovonly && codecov -t ${CODECOV_TOKEN} --disable=gcov", "coverage": "nyc report -r lcov && opener ./coverage/lcov-report/index.html", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "lint": "eslint lib tests scripts", "new": "node scripts/new-rule", "pretest": "npm run -s lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha tests/**/*.js --reporter dot", "update": "run-s update:*", "update:url": "npm run -s lint -- --fix", "update:ruledocs": "node scripts/update-docs-rules", "update:index": "node scripts/update-index", "preversion": "npm test", "version": "run-s update:* docs:build && git add .", "postversion": "git push && git push --tags", "watch": "mocha tests/**/*.js --reporter progress --watch --growl"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/eslint-plugin-es.git"}, "keywords": ["eslint", "plugin", "eslintplugin"], "author": "<PERSON><PERSON> (https://github.com/mysticatea)", "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/eslint-plugin-es/issues"}, "homepage": "https://github.com/mysticatea/eslint-plugin-es#readme", "funding": "https://github.com/sponsors/mysticatea"}