{"name": "ethereum-cryptography", "version": "2.2.1", "description": "All the cryptographic primitives used in Ethereum", "repository": "https://github.com/ethereum/js-ethereum-cryptography", "license": "MIT", "main": "./index.js", "files": ["bip39/*.js", "bip39/*.d.ts", "bip39/wordlists/*.js", "bip39/wordlists/*.d.ts", "*.js", "*.d.ts", "esm"], "dependencies": {"@noble/curves": "1.4.2", "@noble/hashes": "1.4.0", "@scure/bip32": "1.4.0", "@scure/bip39": "1.3.0"}, "exports": {".": {"types": "./index.d.ts", "import": "./esm/index.js", "default": "./index.js"}, "./aes": {"types": "./aes.d.ts", "import": "./esm/aes.js", "default": "./aes.js"}, "./bip39": {"types": "./bip39/index.d.ts", "import": "./esm/bip39/index.js", "default": "./bip39/index.js"}, "./blake2b": {"types": "./blake2b.d.ts", "import": "./esm/blake2b.js", "default": "./blake2b.js"}, "./hdkey": {"types": "./hdkey.d.ts", "import": "./esm/hdkey.js", "default": "./hdkey.js"}, "./index": {"types": "./index.d.ts", "import": "./esm/index.js", "default": "./index.js"}, "./keccak": {"types": "./keccak.d.ts", "import": "./esm/keccak.js", "default": "./keccak.js"}, "./pbkdf2": {"types": "./pbkdf2.d.ts", "import": "./esm/pbkdf2.js", "default": "./pbkdf2.js"}, "./random": {"types": "./random.d.ts", "import": "./esm/random.js", "default": "./random.js"}, "./ripemd160": {"types": "./ripemd160.d.ts", "import": "./esm/ripemd160.js", "default": "./ripemd160.js"}, "./scrypt": {"types": "./scrypt.d.ts", "import": "./esm/scrypt.js", "default": "./scrypt.js"}, "./secp256k1-compat": {"types": "./secp256k1-compat.d.ts", "import": "./esm/secp256k1-compat.js", "default": "./secp256k1-compat.js"}, "./secp256k1": {"types": "./secp256k1.d.ts", "import": "./esm/secp256k1.js", "default": "./secp256k1.js"}, "./sha256": {"types": "./sha256.d.ts", "import": "./esm/sha256.js", "default": "./sha256.js"}, "./sha512": {"types": "./sha512.d.ts", "import": "./esm/sha512.js", "default": "./sha512.js"}, "./utils": {"types": "./utils.d.ts", "import": "./esm/utils.js", "default": "./utils.js"}, "./bip39/index": {"types": "./bip39/index.d.ts", "import": "./esm/bip39/index.js", "default": "./bip39/index.js"}, "./bip39/wordlists/czech": {"types": "./bip39/wordlists/czech.d.ts", "import": "./esm/bip39/wordlists/czech.js", "default": "./bip39/wordlists/czech.js"}, "./bip39/wordlists/english": {"types": "./bip39/wordlists/english.d.ts", "import": "./esm/bip39/wordlists/english.js", "default": "./bip39/wordlists/english.js"}, "./bip39/wordlists/french": {"types": "./bip39/wordlists/french.d.ts", "import": "./esm/bip39/wordlists/french.js", "default": "./bip39/wordlists/french.js"}, "./bip39/wordlists/italian": {"types": "./bip39/wordlists/italian.d.ts", "import": "./esm/bip39/wordlists/italian.js", "default": "./bip39/wordlists/italian.js"}, "./bip39/wordlists/japanese": {"types": "./bip39/wordlists/japanese.d.ts", "import": "./esm/bip39/wordlists/japanese.js", "default": "./bip39/wordlists/japanese.js"}, "./bip39/wordlists/korean": {"types": "./bip39/wordlists/korean.d.ts", "import": "./esm/bip39/wordlists/korean.js", "default": "./bip39/wordlists/korean.js"}, "./bip39/wordlists/portuguese": {"types": "./bip39/wordlists/portuguese.d.ts", "import": "./esm/bip39/wordlists/portuguese.js", "default": "./bip39/wordlists/portuguese.js"}, "./bip39/wordlists/simplified-chinese": {"types": "./bip39/wordlists/simplified-chinese.d.ts", "import": "./esm/bip39/wordlists/simplified-chinese.js", "default": "./bip39/wordlists/simplified-chinese.js"}, "./bip39/wordlists/spanish": {"types": "./bip39/wordlists/spanish.d.ts", "import": "./esm/bip39/wordlists/spanish.js", "default": "./bip39/wordlists/spanish.js"}, "./bip39/wordlists/traditional-chinese": {"types": "./bip39/wordlists/traditional-chinese.d.ts", "import": "./esm/bip39/wordlists/traditional-chinese.js", "default": "./bip39/wordlists/traditional-chinese.js"}, "./aes.js": {"types": "./aes.d.ts", "import": "./esm/aes.js", "default": "./aes.js"}, "./bip39.js": {"types": "./bip39/index.d.ts", "import": "./esm/bip39/index.js", "default": "./bip39/index.js"}, "./blake2b.js": {"types": "./blake2b.d.ts", "import": "./esm/blake2b.js", "default": "./blake2b.js"}, "./hdkey.js": {"types": "./hdkey.d.ts", "import": "./esm/hdkey.js", "default": "./hdkey.js"}, "./index.js": {"types": "./index.d.ts", "import": "./esm/index.js", "default": "./index.js"}, "./keccak.js": {"types": "./keccak.d.ts", "import": "./esm/keccak.js", "default": "./keccak.js"}, "./pbkdf2.js": {"types": "./pbkdf2.d.ts", "import": "./esm/pbkdf2.js", "default": "./pbkdf2.js"}, "./random.js": {"types": "./random.d.ts", "import": "./esm/random.js", "default": "./random.js"}, "./ripemd160.js": {"types": "./ripemd160.d.ts", "import": "./esm/ripemd160.js", "default": "./ripemd160.js"}, "./scrypt.js": {"types": "./scrypt.d.ts", "import": "./esm/scrypt.js", "default": "./scrypt.js"}, "./secp256k1-compat.js": {"types": "./secp256k1-compat.d.ts", "import": "./esm/secp256k1-compat.js", "default": "./secp256k1-compat.js"}, "./secp256k1.js": {"types": "./secp256k1.d.ts", "import": "./esm/secp256k1.js", "default": "./secp256k1.js"}, "./sha256.js": {"types": "./sha256.d.ts", "import": "./esm/sha256.js", "default": "./sha256.js"}, "./sha512.js": {"types": "./sha512.d.ts", "import": "./esm/sha512.js", "default": "./sha512.js"}, "./utils.js": {"types": "./utils.d.ts", "import": "./esm/utils.js", "default": "./utils.js"}, "./bip39/index.js": {"types": "./bip39/index.d.ts", "import": "./esm/bip39/index.js", "default": "./bip39/index.js"}, "./bip39/wordlists/czech.js": {"types": "./bip39/wordlists/czech.d.ts", "import": "./esm/bip39/wordlists/czech.js", "default": "./bip39/wordlists/czech.js"}, "./bip39/wordlists/english.js": {"types": "./bip39/wordlists/english.d.ts", "import": "./esm/bip39/wordlists/english.js", "default": "./bip39/wordlists/english.js"}, "./bip39/wordlists/french.js": {"types": "./bip39/wordlists/french.d.ts", "import": "./esm/bip39/wordlists/french.js", "default": "./bip39/wordlists/french.js"}, "./bip39/wordlists/italian.js": {"types": "./bip39/wordlists/italian.d.ts", "import": "./esm/bip39/wordlists/italian.js", "default": "./bip39/wordlists/italian.js"}, "./bip39/wordlists/japanese.js": {"types": "./bip39/wordlists/japanese.d.ts", "import": "./esm/bip39/wordlists/japanese.js", "default": "./bip39/wordlists/japanese.js"}, "./bip39/wordlists/korean.js": {"types": "./bip39/wordlists/korean.d.ts", "import": "./esm/bip39/wordlists/korean.js", "default": "./bip39/wordlists/korean.js"}, "./bip39/wordlists/simplified-chinese.js": {"types": "./bip39/wordlists/simplified-chinese.d.ts", "import": "./esm/bip39/wordlists/simplified-chinese.js", "default": "./bip39/wordlists/simplified-chinese.js"}, "./bip39/wordlists/spanish.js": {"types": "./bip39/wordlists/spanish.d.ts", "import": "./esm/bip39/wordlists/spanish.js", "default": "./bip39/wordlists/spanish.js"}, "./bip39/wordlists/traditional-chinese.js": {"types": "./bip39/wordlists/traditional-chinese.d.ts", "import": "./esm/bip39/wordlists/traditional-chinese.js", "default": "./bip39/wordlists/traditional-chinese.js"}}, "browser": {"crypto": false}, "sideEffects": false, "scripts": {"prepare": "npm run build", "build": "npm-run-all build:tsc", "build:tsc": "tsc --project tsconfig.prod.json && tsc --project tsconfig.prod.esm.json", "test": "npm-run-all test:node", "test:node": "cd test && npm install && cd .. && mocha", "clean": "rimraf test/test-builds bip39 '*.js' '*.js.map' '*.d.ts' '*.d.ts.map' 'src/**/*.js'", "lint": "eslint", "lint:fix": "eslint --fix", "browser-tests": "npm-run-all browser-tests:build browser-tests:test", "browser-tests:build": "bash -x ./scripts/build-browser-tests.sh", "browser-tests:test": "npm-run-all browser-tests:test-parcel browser-tests:test-browserify browser-tests:test-webpack browser-tests:test-rollup", "browser-tests:test-parcel": "karma start --single-run --browsers ChromeHeadless test/karma.parcel.conf.js", "browser-tests:test-browserify": "karma start --single-run --browsers ChromeHeadless test/karma.browserify.conf.js", "browser-tests:test-webpack": "karma start --single-run --browsers ChromeHeadless test/karma.webpack.conf.js", "browser-tests:test-rollup": "karma start --single-run --browsers ChromeHeadless test/karma.rollup.conf.js"}, "devDependencies": {"@rollup/plugin-commonjs": "22.0.1", "@rollup/plugin-node-resolve": "13.3.0", "@types/estree": "1.0.0", "@types/mocha": "9.1.1", "@types/node": "18.15.11", "@typescript-eslint/eslint-plugin": "5.30.6", "@typescript-eslint/parser": "5.30.6", "browserify": "17.0.0", "eslint": "8.38.0", "eslint-plugin-prettier": "4.2.1", "karma": "6.4.0", "karma-chrome-launcher": "3.1.1", "karma-mocha": "2.0.1", "karma-mocha-reporter": "2.2.5", "mocha": "10.0.0", "npm-run-all": "4.1.5", "parcel": "2.6.2", "prettier": "2.7.1", "rimraf": "~3.0.2", "rollup": "2.76.0", "ts-node": "10.9.1", "typescript": "5.5.2", "webpack": "5.76.0", "webpack-cli": "4.10.0"}, "keywords": ["ethereum", "cryptography", "digital signature", "hash", "encryption", "prng", "keccak", "scrypt", "pbkdf2", "sha-256", "ripemd-160", "blake2b", "aes", "advanced encryption standard", "secp256k1", "ecdsa", "bip32", "hierarchical deterministic keys", "hdwallet", "hdkeys"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "targets": {"parcel_tests": {"context": "browser"}}}