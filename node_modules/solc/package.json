{"name": "solc", "version": "0.8.26", "description": "Solidity compiler", "main": "index.js", "bin": {"solcjs": "solc.js"}, "scripts": {"build": "tsc", "postbuild": "node build/postbuild.js && cp README.md LICENSE dist/", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --fix --ext .js,.ts .", "updateBinary": "node build/clean.js && ts-node ./downloadCurrentVersion.ts && ts-node ./verifyVersion.ts", "prepack": "node build/pack-publish-block.js", "build:tarball": "npm run updateBinary && npm run build && BYPASS_SAFETY_CHECK=true npm pack ./dist", "publish:tarball": "tarball=$(npm run --silent tarballName) && ls \"$tarball\" && BYPASS_SAFETY_CHECK=true npm publish \"$tarball\"", "tarballName": "jq --raw-output '.name + \"-\" + .version + \".tgz\"' package.json", "copyTestFiles": "cp -r ./test/resources ./dist/test/", "pretest": "npm run lint && npm run build && npm run copyTestFiles", "test": "cd dist && tape ./test/index.js", "coverage": "nyc npm run test", "coveralls": "npm run coverage && coveralls <coverage/lcov.info"}, "repository": {"type": "git", "url": "git+https://github.com/ethereum/solc-js.git"}, "keywords": ["ethereum", "solidity", "compiler"], "engines": {"node": ">=10.0.0"}, "files": ["common/*.js", "bindings/*.js", "*.js"], "author": "chriseth", "license": "MIT", "bugs": {"url": "https://github.com/ethereum/solc-js/issues"}, "homepage": "https://github.com/ethereum/solc-js#readme", "dependencies": {"command-exists": "^1.2.8", "commander": "^8.1.0", "follow-redirects": "^1.12.1", "js-sha3": "0.8.0", "memorystream": "^0.3.1", "semver": "^5.5.0", "tmp": "0.0.33"}, "devDependencies": {"@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@types/tape": "^4.13.2", "@types/tmp": "^0.2.3", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "coveralls": "^3.0.0", "eslint": "^7.32.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.1", "nyc": "^15.1.0", "tape": "^4.11.0", "tape-spawn": "^1.4.2", "ts-node": "^10.4.0", "typescript": "^4.5.4"}, "nyc": {"exclude": ["soljson.js", "dist"]}}