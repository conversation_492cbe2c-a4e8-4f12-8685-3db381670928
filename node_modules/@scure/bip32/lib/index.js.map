{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../index.ts"], "names": [], "mappings": ";;;AACA,6CAA0C;AAC1C,uDAAoD;AACpD,iDAA8C;AAC9C,iDAA8C;AAC9C,mDAA6D;AAC7D,+CAAmG;AACnG,yCAAyC;AACzC,sCAA2D;AAG3D,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,EAAE,CAAC,IAAA,WAAI,EAAC,eAAM,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACjG,MAAM,WAAW,GAAG,IAAA,kBAAa,EAAC,eAAM,CAAC,CAAC;AAE1C,SAAS,aAAa,CAAC,KAAiB;IACtC,OAAO,MAAM,CAAC,KAAK,IAAA,kBAAU,EAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,aAAa,CAAC,GAAW;IAChC,OAAO,IAAA,kBAAU,EAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;AACxD,CAAC;AAED,MAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,cAAc,CAAC,CAAC;AAElD,MAAM,gBAAgB,GAAa,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;AAClE,QAAA,eAAe,GAAW,UAAU,CAAC;AAOlD,MAAM,OAAO,GAAG,CAAC,IAAgB,EAAE,EAAE,CAAC,IAAA,qBAAS,EAAC,IAAA,eAAM,EAAC,IAAI,CAAC,CAAC,CAAC;AAC9D,MAAM,OAAO,GAAG,CAAC,IAAgB,EAAE,EAAE,CAAC,IAAA,kBAAU,EAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC3E,MAAM,KAAK,GAAG,CAAC,CAAS,EAAE,EAAE;IAC1B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;QACxD,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,mCAAmC,CAAC,CAAC;KACzE;IACD,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAA,kBAAU,EAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACvC,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAYF,MAAa,KAAK;IAuFhB,YAAY,GAAa;QATT,UAAK,GAAW,CAAC,CAAC;QAClB,UAAK,GAAW,CAAC,CAAC;QAClB,cAAS,GAAsB,IAAI,CAAC;QACpC,sBAAiB,GAAW,CAAC,CAAC;QAO5C,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,IAAI,gBAAgB,CAAC;QACjD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC,iBAAiB,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,EAAE;gBACxC,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;aAC7E;SACF;QACD,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,UAAU,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SAClE;QACD,IAAI,GAAG,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBACjD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,OAAO;gBACV,OAAO,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACtF,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;SACvD;aAAM,IAAI,GAAG,CAAC,SAAS,EAAE;YACxB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAClE;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IArHD,IAAI,WAAW;QACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;SACtC;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;IACnC,CAAC;IACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;IAC7B,CAAC;IACD,IAAI,kBAAkB;QACpB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;SACnC;QACD,OAAO,WAAW,CAAC,MAAM,CACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAA,mBAAW,EAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAC9E,CAAC;IACJ,CAAC;IACD,IAAI,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;SAClC;QACD,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/E,CAAC;IAEM,MAAM,CAAC,cAAc,CAAC,IAAgB,EAAE,WAAqB,gBAAgB;QAClF,IAAA,eAAW,EAAC,IAAI,CAAC,CAAC;QAClB,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;YAClD,MAAM,IAAI,KAAK,CACb,4BAA4B,IAAI,CAAC,MAAM,4DAA4D,CACpG,CAAC;SACH;QACD,MAAM,CAAC,GAAG,IAAA,WAAI,EAAC,eAAM,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,IAAI,KAAK,CAAC;YACf,QAAQ;YACR,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YACtB,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;SAC3B,CAAC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,SAAiB,EAAE,WAAqB,gBAAgB;QAEpF,MAAM,SAAS,GAAe,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAG,IAAA,kBAAU,EAAC,SAAS,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC5C,MAAM,GAAG,GAAG;YACV,QAAQ;YACR,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;YACnB,iBAAiB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;YAC9C,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;YAClC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;SACnC,CAAC;QACF,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAChC,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,OAAO,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE;YACvD,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACrC;QACD,IAAI,MAAM,EAAE;YACV,OAAO,IAAI,KAAK,CAAC,EAAE,GAAG,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACxD;aAAM;YACL,OAAO,IAAI,KAAK,CAAC,EAAE,GAAG,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;SAC9C;IACH,CAAC;IAEM,MAAM,CAAC,QAAQ,CAAC,IAAuB;QAC5C,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IA4CM,MAAM,CAAC,IAAY;QACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACpD;QACD,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC;SACb;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEvD,IAAI,KAAK,GAAU,IAAI,CAAC;QACxB,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;YACrB,MAAM,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC;aAC9C;YACD,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,uBAAe,EAAE;gBACxD,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;aAClC;YAED,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAChB,GAAG,IAAI,uBAAe,CAAC;aACxB;YACD,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;SAChC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,WAAW,CAAC,KAAa;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QACD,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,KAAK,IAAI,uBAAe,EAAE;YAE5B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;YAC7B,IAAI,CAAC,IAAI,EAAE;gBACT,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;aACxD;YAED,IAAI,GAAG,IAAA,mBAAW,EAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SACrD;aAAM;YAEL,IAAI,GAAG,IAAA,mBAAW,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACvC;QACD,MAAM,CAAC,GAAG,IAAA,WAAI,EAAC,eAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC7C,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QACD,MAAM,GAAG,GAAa;YACpB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS;YACT,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC;YACrB,iBAAiB,EAAE,IAAI,CAAC,WAAW;YACnC,KAAK;SACN,CAAC;QACF,IAAI;YAEF,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAQ,GAAG,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;oBACxC,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;iBACtF;gBACD,GAAG,CAAC,UAAU,GAAG,KAAK,CAAC;aACxB;iBAAM;gBACL,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC;gBAEzF,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACjC,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;iBACzF;gBACD,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aACxC;YACD,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;SACvB;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;SACpC;IACH,CAAC;IAEM,IAAI,CAAC,IAAgB;QAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACvC;QACD,IAAA,eAAW,EAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAQ,EAAE;YACxC,SAAS,EAAE,IAAI;YACf,GAAG,EAAE,KAAK;SACX,CAAC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,IAAgB,EAAE,SAAqB;QACnD,IAAA,eAAW,EAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACtB,IAAA,eAAW,EAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;SACtC;QACD,IAAI,GAAG,CAAC;QACR,IAAI;YACF,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;SAC7C;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC;IAEM,eAAe;QACpB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACM,MAAM;QACX,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,kBAAkB;YAC9B,IAAI,EAAE,IAAI,CAAC,iBAAiB;SAC7B,CAAC;IACJ,CAAC;IAEO,SAAS,CAAC,OAAe,EAAE,GAAe;QAChD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACrC;QACD,IAAA,eAAW,EAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAErB,OAAO,IAAA,mBAAW,EAChB,KAAK,CAAC,OAAO,CAAC,EACd,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC5B,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAC7B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EACjB,IAAI,CAAC,SAAS,EACd,GAAG,CACJ,CAAC;IACJ,CAAC;CACF;AAhQD,sBAgQC"}