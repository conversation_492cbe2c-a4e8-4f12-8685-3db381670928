{"version": 3, "file": "bn254.js", "sourceRoot": "", "sources": ["../src/bn254.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CG;AACH,sEAAsE;AACtE,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAC7C,OAAO,EACL,GAAG,GAIJ,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAE9C,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAC5D,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrE,OAAO,EAAgB,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACtE,kBAAkB;AAClB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAEtB,MAAM,IAAI,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAC3C,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9B,MAAM,aAAa,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC;AAExC,+EAA+E;AAC/E,MAAM,EAAE,GAAG,KAAK,CACd,MAAM,CAAC,+EAA+E,CAAC,CACxF,CAAC;AACF,iDAAiD;AACjD,MAAM,IAAI,GAAG;IACX,EAAE,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC3F,EAAE,EAAE,MAAM,CAAC,6EAA6E,CAAC;CAC1F,CAAC;AAEF,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IAChD,KAAK,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC9F,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;IAChC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;IACtC,wDAAwD;IACxD,uCAAuC;IACvC,oBAAoB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAQ,EAAE;QACzC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;QAC5C,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;QAC5C,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxD,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxD,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxD,IAAI,EAAE,GAAG,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe;QACjD,OAAO;YACL,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC;gBACb,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,wBAAwB;gBAC1E,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,wBAAwB;gBAC1E,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC;aACjD,CAAC,EAAE,wBAAwB;YAC5B,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC;gBACb,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,uBAAuB;gBACzE,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,uBAAuB;gBACzE,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC;aACjD,CAAC;SACH,CAAC,CAAC,uBAAuB;IAC5B,CAAC;IACD,iBAAiB,CAAC,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;gBAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IACD,uCAAuC;IACvC,uCAAuC;IACvC,qBAAqB,EAAE,CAAC,GAAG,EAAE,EAAE;QAC7B,MAAM,SAAS,GAAG,CAAC,GAAS,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;QAChF,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACpD,MAAM,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;QACzB,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC,GAAG,CACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EACrD,IAAI,CAAC,GAAG,CACN,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EACxB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAClE,CACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,sBAAsB;AACtB,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAE7D;;;;;;EAME;AACF,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;IAChC,wDAAwD;IACxD,GAAG,EAAE,8BAA8B;IACnC,SAAS,EAAE,8BAA8B;IACzC,CAAC,EAAE,EAAE,CAAC,KAAK;IACX,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;IACN,MAAM,EAAE,KAAK;IACb,IAAI,EAAE,MAAM;CACJ,CAAC,CAAC;AAEZ,MAAM,CAAC,MAAM,eAAe,GAAqB,CAC/C,EAAO,EACP,EAAO,EACP,EAAO,EACP,EAAO,EACP,EAAO,EACP,QAAkC,EAClC,EAAE;IACF,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACtB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,CAAC,MAAM,KAAK,GAAe,GAAG,CAAC;IACnC,SAAS;IACT,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;IAClC,EAAE,EAAE;QACF,EAAE;QACF,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;QACZ,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;QACb,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,IAAI;QACV,CAAC,EAAE,GAAG;QACN,WAAW,EAAE,EAAE,GAAG,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,8BAA8B,EAAE;QAC1E,cAAc,EAAE,IAAI;QACpB,kBAAkB,EAAE,IAAI;QACxB,UAAU,EAAE,cAAc;QAC1B,SAAS,EAAE,cAAc;QACzB,OAAO,EAAE,cAAc;QACvB,cAAc,EAAE;YACd,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,cAAc;YAC1B,KAAK,EAAE,cAAc;SACtB;KACF;IACD,EAAE,EAAE;QACF,EAAE,EAAE,GAAG;QACP,2DAA2D;QAC3D,CAAC,EAAE,MAAM,CAAC,+EAA+E,CAAC;QAC1F,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC;YACnB,MAAM,CAAC,+EAA+E,CAAC;YACvF,MAAM,CAAC,+EAA+E,CAAC;SACxF,CAAC;QACF,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC;YACnB,MAAM,CAAC,8EAA8E,CAAC;YACtF,MAAM,CAAC,8EAA8E,CAAC;SACvF,CAAC;QACF,CAAC,EAAE,GAAG,CAAC,IAAI;QACX,CAAC,EAAE,IAAI;QACP,IAAI,EAAE,MAAM,CAAC,+EAA+E,CAAC;QAC7F,WAAW,EAAE,EAAE,GAAG,WAAW,EAAE;QAC/B,cAAc,EAAE,IAAI;QACpB,kBAAkB,EAAE,IAAI;QACxB,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,iBAAiB;QAC/F,UAAU,EAAE,cAAc;QAC1B,SAAS,EAAE,cAAc;QACzB,OAAO,EAAE,cAAc;QACvB,SAAS,EAAE;YACT,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,cAAc;YAC1B,KAAK,EAAE,cAAc;SACtB;KACF;IACD,MAAM,EAAE;QACN,WAAW,EAAE,IAAI,GAAG,GAAG,GAAG,GAAG;QAC7B,CAAC,EAAE,EAAE,CAAC,KAAK;QACX,SAAS,EAAE,KAAK;QAChB,SAAS,EAAE,UAAU;KACtB;IACD,WAAW;IACX,IAAI,EAAE,MAAM;IACZ,WAAW;IAEX,cAAc,EAAE,eAAe;CAChC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAY,WAAW,CAAC;IACpD,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,EAAE;IACF,CAAC,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC1F,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACb,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACb,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,GAAG,OAAO,CAAC,MAAM,CAAC;CACnB,CAAC,CAAC"}