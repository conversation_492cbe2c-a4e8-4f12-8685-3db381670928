{"version": 3, "file": "sha3.d.ts", "sourceRoot": "", "sources": ["src/sha3.ts"], "names": [], "mappings": "AAaA,OAAO,EAEL,IAAI,EAMJ,KAAK,KAAK,EACV,KAAK,OAAO,EACZ,KAAK,OAAO,EACZ,KAAK,KAAK,EACX,MAAM,YAAY,CAAC;AAgCpB,kFAAkF;AAClF,wBAAgB,OAAO,CAAC,CAAC,EAAE,WAAW,EAAE,MAAM,GAAE,MAAW,GAAG,IAAI,CAyCjE;AAED,8BAA8B;AAC9B,qBAAa,MAAO,SAAQ,IAAI,CAAC,MAAM,CAAE,YAAW,OAAO,CAAC,MAAM,CAAC;IACjE,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC;IAC5B,SAAS,CAAC,GAAG,SAAK;IAClB,SAAS,CAAC,MAAM,SAAK;IACrB,SAAS,CAAC,QAAQ,UAAS;IAC3B,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC;IAC/B,SAAS,CAAC,SAAS,UAAS;IAErB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IACzB,SAAS,CAAC,SAAS,UAAS;IAC5B,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;gBAIvB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,MAAM,EACjB,SAAS,UAAQ,EACjB,MAAM,GAAE,MAAW;IAiBrB,SAAS,CAAC,MAAM,IAAI,IAAI;IAOxB,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;IAYzB,SAAS,CAAC,MAAM,IAAI,IAAI;IAUxB,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAehD,OAAO,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAKpC,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU;IAI9B,UAAU,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAOvC,MAAM,IAAI,UAAU;IAGpB,OAAO,IAAI,IAAI;IAIf,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM;CAehC;AAKD,8BAA8B;AAC9B,eAAO,MAAM,QAAQ,EAAE,KAA+C,CAAC;AACvE,yDAAyD;AACzD,eAAO,MAAM,QAAQ,EAAE,KAA+C,CAAC;AACvE,8BAA8B;AAC9B,eAAO,MAAM,QAAQ,EAAE,KAA+C,CAAC;AACvE,8BAA8B;AAC9B,eAAO,MAAM,QAAQ,EAAE,KAA8C,CAAC;AAEtE,gCAAgC;AAChC,eAAO,MAAM,UAAU,EAAE,KAA+C,CAAC;AACzE,yDAAyD;AACzD,eAAO,MAAM,UAAU,EAAE,KAA+C,CAAC;AACzE,gCAAgC;AAChC,eAAO,MAAM,UAAU,EAAE,KAA+C,CAAC;AACzE,gCAAgC;AAChC,eAAO,MAAM,UAAU,EAAE,KAA8C,CAAC;AAExE,MAAM,MAAM,SAAS,GAAG;IAAE,KAAK,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAQ3C,0CAA0C;AAC1C,eAAO,MAAM,QAAQ,EAAE,OAAsD,CAAC;AAC9E,0CAA0C;AAC1C,eAAO,MAAM,QAAQ,EAAE,OAAsD,CAAC"}