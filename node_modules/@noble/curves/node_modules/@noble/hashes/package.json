{"name": "@noble/hashes", "version": "1.7.2", "description": "Audited & minimal 0-dependency JS implementation of SHA, RIPEMD, BLAKE, HMAC, HKDF, PBKDF & Scrypt", "files": ["/*.js", "/*.js.map", "/*.d.ts", "/*.d.ts.map", "esm", "src/*.ts"], "scripts": {"bench": "node benchmark/noble.js", "bench:compare": "MBENCH_DIMS='algorithm,buffer,library' node benchmark/hashes.js", "bench:compare-hkdf": "MBENCH_DIMS='algorithm,length,library' node benchmark/hkdf.js", "bench:compare-scrypt": "MBENCH_DIMS='iters,library' MBENCH_FILTER='async' node benchmark/scrypt.js", "bench:install": "cd benchmark; npm install; npm install .. --install-links", "build": "npm run build:clean; tsc && tsc -p tsconfig.cjs.json", "build:clean": "rm -f *.{js,d.ts,js.map,d.ts.map} esm/*.{js,js.map,d.ts.map}", "build:release": "npx jsbt esbuild test/build", "lint": "prettier --check 'src/**/*.{js,ts}' 'test/**/*.{js,ts}'", "format": "prettier --write 'src/**/*.{js,ts}' 'test/**/*.{js,ts}'", "test": "node --import ./test/esm-register.js test/index.js", "test:bun": "bun test/index.js", "test:deno": "deno --allow-env --allow-read --import-map=./test/import_map.json test/index.js", "test:dos": "node --import ./test/esm-register.js test/slow-dos.test.js", "test:big": "node --import ./test/esm-register.js test/slow-big.test.js", "test:kdf": "node --import ./test/esm-register.js test/slow-kdf.test.js"}, "author": "<PERSON> (https://paulmillr.com)", "homepage": "https://paulmillr.com/noble/", "repository": {"type": "git", "url": "git+https://github.com/paulmillr/noble-hashes.git"}, "license": "MIT", "devDependencies": {"@paulmillr/jsbt": "0.3.3", "fast-check": "3.0.0", "micro-bmark": "0.4.0", "micro-should": "0.5.1", "prettier": "3.5.2", "typescript": "5.8.2"}, "engines": {"node": "^14.21.3 || >=16"}, "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}, "./crypto": {"node": {"import": "./esm/cryptoNode.js", "default": "./cryptoNode.js"}, "import": "./esm/crypto.js", "default": "./crypto.js"}, "./_assert": {"import": "./esm/_assert.js", "require": "./_assert.js"}, "./_md": {"import": "./esm/_md.js", "require": "./_md.js"}, "./argon2": {"import": "./esm/argon2.js", "require": "./argon2.js"}, "./blake1": {"import": "./esm/blake1.js", "require": "./blake1.js"}, "./blake2b": {"import": "./esm/blake2b.js", "require": "./blake2b.js"}, "./blake2s": {"import": "./esm/blake2s.js", "require": "./blake2s.js"}, "./blake3": {"import": "./esm/blake3.js", "require": "./blake3.js"}, "./eskdf": {"import": "./esm/eskdf.js", "require": "./eskdf.js"}, "./hkdf": {"import": "./esm/hkdf.js", "require": "./hkdf.js"}, "./hmac": {"import": "./esm/hmac.js", "require": "./hmac.js"}, "./legacy": {"import": "./esm/legacy.js", "require": "./legacy.js"}, "./pbkdf2": {"import": "./esm/pbkdf2.js", "require": "./pbkdf2.js"}, "./ripemd160": {"import": "./esm/ripemd160.js", "require": "./ripemd160.js"}, "./scrypt": {"import": "./esm/scrypt.js", "require": "./scrypt.js"}, "./sha1": {"import": "./esm/sha1.js", "require": "./sha1.js"}, "./sha2": {"import": "./esm/sha2.js", "require": "./sha2.js"}, "./sha3-addons": {"import": "./esm/sha3-addons.js", "require": "./sha3-addons.js"}, "./sha3": {"import": "./esm/sha3.js", "require": "./sha3.js"}, "./sha256": {"import": "./esm/sha256.js", "require": "./sha256.js"}, "./sha512": {"import": "./esm/sha512.js", "require": "./sha512.js"}, "./utils": {"import": "./esm/utils.js", "require": "./utils.js"}}, "sideEffects": false, "browser": {"node:crypto": false, "./crypto": "./crypto.js"}, "keywords": ["sha", "sha2", "sha3", "sha256", "sha512", "keccak", "kangarootwelve", "ripemd160", "blake2", "blake3", "hmac", "hkdf", "pbkdf2", "scrypt", "kdf", "hash", "cryptography", "security", "noble"], "funding": "https://paulmillr.com/funding/"}