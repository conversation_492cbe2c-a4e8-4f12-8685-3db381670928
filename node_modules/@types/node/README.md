# Installation
> `npm install --save @types/node`

# Summary
This package contains type definitions for node (https://nodejs.org/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node.

### Additional Details
 * Last updated: Fri, 08 Aug 2025 16:38:49 GMT
 * Dependencies: [undici-types](https://npmjs.com/package/undici-types)

# Credits
These definitions were written by [Microsoft TypeScript](https://github.com/Microsoft), [<PERSON>](https://github.com/jkomyno), [<PERSON>](https://github.com/r3nya), [<PERSON>](https://github.com/btoueg), [<PERSON>](https://github.com/touffy), [<PERSON><PERSON><PERSON>](https://github.com/mohsen1), [<PERSON><PERSON>](https://github.com/galkin), [<PERSON>](https://github.com/eps1lon), [<PERSON><PERSON><PERSON>](https://github.com/WilcoBakker), [<PERSON><PERSON>](https://github.com/chyzwar), [<PERSON><PERSON><PERSON>](https://github.com/trivikr), [Junxiao Shi](https://github.com/yoursunny), [Ilia Baryshnikov](https://github.com/qwelias), [ExE Boss](https://github.com/ExE-Boss), [Piotr Błażejewicz](https://github.com/peterblazejewicz), [Anna Henningsen](https://github.com/addaleax), [Victor Perin](https://github.com/victorperin), [NodeJS Contributors](https://github.com/NodeJS), [Linus Unnebäck](https://github.com/LinusU), [wafuwafu13](https://github.com/wafuwafu13), [Matteo Collina](https://github.com/mcollina), [Dmitry Semigradsky](https://github.com/Semigradsky), [René](https://github.com/Renegade334), and [Yagiz Nizipli](https://github.com/anonrig).
