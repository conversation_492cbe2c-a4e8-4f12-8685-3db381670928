{"version": 3, "file": "memo.js", "sourceRoot": "", "sources": ["../src/memo.ts"], "names": [], "mappings": "AAAA,+DAA+D;AAC/D,uDAAuD;AACvD,sEAAsE;AACtE;;GAEG;AACH;IAME;QACE,IAAI,CAAC,WAAW,GAAG,OAAO,OAAO,KAAK,UAAU,CAAC;QACjD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACtD,CAAC;IAED;;;OAGG;IACI,sBAAO,GAAd,UAAe,GAAQ;QACrB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACxB,OAAO,IAAI,CAAC;aACb;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,OAAO,KAAK,CAAC;SACd;QACD,4DAA4D;QAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,KAAK,KAAK,GAAG,EAAE;gBACjB,OAAO,IAAI,CAAC;aACb;SACF;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,wBAAS,GAAhB,UAAiB,GAAQ;QACvB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACzB;aAAM;YACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACzB,MAAM;iBACP;aACF;SACF;IACH,CAAC;IACH,WAAC;AAAD,CAAC,AAlDD,IAkDC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/**\n * Memo class used for decycle json objects. Uses WeakSet if available otherwise array.\n */\nexport class Memo {\n  /** Determines if WeakSet is available */\n  private readonly _hasWeakSet: boolean;\n  /** Either WeakSet or Array */\n  private readonly _inner: any;\n\n  public constructor() {\n    this._hasWeakSet = typeof WeakSet === 'function';\n    this._inner = this._hasWeakSet ? new WeakSet() : [];\n  }\n\n  /**\n   * Sets obj to remember.\n   * @param obj Object to remember\n   */\n  public memoize(obj: any): boolean {\n    if (this._hasWeakSet) {\n      if (this._inner.has(obj)) {\n        return true;\n      }\n      this._inner.add(obj);\n      return false;\n    }\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < this._inner.length; i++) {\n      const value = this._inner[i];\n      if (value === obj) {\n        return true;\n      }\n    }\n    this._inner.push(obj);\n    return false;\n  }\n\n  /**\n   * Removes object from internal storage.\n   * @param obj Object to forget\n   */\n  public unmemoize(obj: any): void {\n    if (this._hasWeakSet) {\n      this._inner.delete(obj);\n    } else {\n      for (let i = 0; i < this._inner.length; i++) {\n        if (this._inner[i] === obj) {\n          this._inner.splice(i, 1);\n          break;\n        }\n      }\n    }\n  }\n}\n"]}