{"version": 3, "file": "stacktrace.js", "sourceRoot": "", "sources": ["../src/stacktrace.ts"], "names": [], "mappings": "AAAA,IAAM,mBAAmB,GAAG,aAAa,CAAC;AAE1C;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,EAAW;IACzC,IAAI;QACF,IAAI,CAAC,EAAE,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;YACnC,OAAO,mBAAmB,CAAC;SAC5B;QACD,OAAO,EAAE,CAAC,IAAI,IAAI,mBAAmB,CAAC;KACvC;IAAC,OAAO,CAAC,EAAE;QACV,4DAA4D;QAC5D,gEAAgE;QAChE,OAAO,mBAAmB,CAAC;KAC5B;AACH,CAAC", "sourcesContent": ["const defaultFunctionName = '<anonymous>';\n\n/**\n * Safely extract function name from itself\n */\nexport function getFunctionName(fn: unknown): string {\n  try {\n    if (!fn || typeof fn !== 'function') {\n      return defaultFunctionName;\n    }\n    return fn.name || defaultFunctionName;\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    return defaultFunctionName;\n  }\n}\n"]}