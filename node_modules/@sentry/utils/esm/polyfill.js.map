{"version": 3, "file": "polyfill.js", "sourceRoot": "", "sources": ["../src/polyfill.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,IAAM,cAAc,GACzB,MAAM,CAAC,cAAc,IAAI,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;AAE/F;;GAEG;AACH,wDAAwD;AACxD,SAAS,UAAU,CAAiC,GAAY,EAAE,KAAa;IAC7E,6CAA6C;IAC7C,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC;IACtB,OAAO,GAAuB,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,wDAAwD;AACxD,SAAS,eAAe,CAAiC,GAAY,EAAE,KAAa;IAClF,KAAK,IAAM,IAAI,IAAI,KAAK,EAAE;QACxB,iDAAiD;QACjD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC7B,8DAA8D;YAC9D,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;SACzB;KACF;IAED,OAAO,GAAuB,CAAC;AACjC,CAAC", "sourcesContent": ["export const setPrototypeOf =\n  Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? setProtoOf : mixinProperties);\n\n/**\n * setPrototypeOf polyfill using __proto__\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction setProtoOf<TTarget extends object, TProto>(obj: TTarget, proto: TProto): TTarget & TProto {\n  // @ts-ignore __proto__ does not exist on obj\n  obj.__proto__ = proto;\n  return obj as TTarget & TProto;\n}\n\n/**\n * setPrototypeOf polyfill using mixin\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction mixinProperties<TTarget extends object, TProto>(obj: TTarget, proto: TProto): TTarget & TProto {\n  for (const prop in proto) {\n    // eslint-disable-next-line no-prototype-builtins\n    if (!obj.hasOwnProperty(prop)) {\n      // @ts-ignore typescript complains about indexing so we remove\n      obj[prop] = proto[prop];\n    }\n  }\n\n  return obj as TTarget & TProto;\n}\n"]}