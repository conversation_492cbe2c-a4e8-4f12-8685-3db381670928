{"version": 3, "file": "path.js", "sourceRoot": "", "sources": ["../src/path.ts"], "names": [], "mappings": "AAAA,wEAAwE;AACxE,qGAAqG;AAErG,YAAY;AACZ,SAAS,cAAc,CAAC,KAAe,EAAE,cAAwB;IAC/D,2DAA2D;IAC3D,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1C,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACpB;aAAM,IAAI,IAAI,KAAK,IAAI,EAAE;YACxB,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnB,uCAAuC;YACvC,EAAE,EAAE,CAAC;SACN;aAAM,IAAI,EAAE,EAAE;YACb,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnB,uCAAuC;YACvC,EAAE,EAAE,CAAC;SACN;KACF;IAED,mEAAmE;IACnE,IAAI,cAAc,EAAE;QAClB,uCAAuC;QACvC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE;YACf,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACrB;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,iEAAiE;AACjE,sCAAsC;AACtC,IAAM,WAAW,GAAG,4DAA4D,CAAC;AACjF,YAAY;AACZ,SAAS,SAAS,CAAC,QAAgB;IACjC,IAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzC,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACrC,CAAC;AAED,+BAA+B;AAC/B,gBAAgB;AAChB,YAAY;AACZ,MAAM,UAAU,OAAO;IAAC,cAAiB;SAAjB,UAAiB,EAAjB,qBAAiB,EAAjB,IAAiB;QAAjB,yBAAiB;;IACvC,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAE7B,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE;QAC/D,IAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAEpC,qBAAqB;QACrB,IAAI,CAAC,IAAI,EAAE;YACT,SAAS;SACV;QAED,YAAY,GAAM,IAAI,SAAI,YAAc,CAAC;QACzC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;KAC3C;IAED,yEAAyE;IACzE,2EAA2E;IAE3E,qBAAqB;IACrB,YAAY,GAAG,cAAc,CAC3B,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,EAAH,CAAG,CAAC,EACxC,CAAC,gBAAgB,CAClB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,YAAY,IAAI,GAAG,CAAC;AAC7D,CAAC;AAED,YAAY;AACZ,SAAS,IAAI,CAAC,GAAa;IACzB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,OAAO,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QAClC,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE;YACrB,MAAM;SACP;KACF;IAED,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;IACzB,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE;YACnB,MAAM;SACP;KACF;IAED,IAAI,KAAK,GAAG,GAAG,EAAE;QACf,OAAO,EAAE,CAAC;KACX;IACD,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AAED,0BAA0B;AAC1B,gBAAgB;AAChB,YAAY;AACZ,MAAM,UAAU,QAAQ,CAAC,IAAY,EAAE,EAAU;IAC/C,sCAAsC;IACtC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/B,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3B,qCAAqC;IAErC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC,IAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAEpC,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IAC1D,IAAI,eAAe,GAAG,MAAM,CAAC;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;YAC/B,eAAe,GAAG,CAAC,CAAC;YACpB,MAAM;SACP;KACF;IAED,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,KAAK,IAAI,CAAC,GAAG,eAAe,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACxB;IAED,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;IAEjE,OAAO,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC;AAED,uBAAuB;AACvB,gBAAgB;AAChB,YAAY;AACZ,MAAM,UAAU,aAAa,CAAC,IAAY;IACxC,IAAM,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IACxC,IAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;IAE9C,qBAAqB;IACrB,IAAI,cAAc,GAAG,cAAc,CACjC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,EAAH,CAAG,CAAC,EAChC,CAAC,cAAc,CAChB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,EAAE;QACtC,cAAc,GAAG,GAAG,CAAC;KACtB;IACD,IAAI,cAAc,IAAI,aAAa,EAAE;QACnC,cAAc,IAAI,GAAG,CAAC;KACvB;IAED,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC;AACtD,CAAC;AAED,gBAAgB;AAChB,YAAY;AACZ,MAAM,UAAU,UAAU,CAAC,IAAY;IACrC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;AAChC,CAAC;AAED,gBAAgB;AAChB,YAAY;AACZ,MAAM,UAAU,IAAI;IAAC,cAAiB;SAAjB,UAAiB,EAAjB,qBAAiB,EAAjB,IAAiB;QAAjB,yBAAiB;;IACpC,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,CAAC;AAED,YAAY;AACZ,MAAM,UAAU,OAAO,CAAC,IAAY;IAClC,IAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAEpB,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE;QACjB,wBAAwB;QACxB,OAAO,GAAG,CAAC;KACZ;IAED,IAAI,GAAG,EAAE;QACP,yCAAyC;QACzC,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KACrC;IAED,OAAO,IAAI,GAAG,GAAG,CAAC;AACpB,CAAC;AAED,YAAY;AACZ,MAAM,UAAU,QAAQ,CAAC,IAAY,EAAE,GAAY;IACjD,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC5C,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;KACxC;IACD,OAAO,CAAC,CAAC;AACX,CAAC", "sourcesContent": ["// Slightly modified (no IE8 support, ES6) and transcribed to TypeScript\n// https://raw.githubusercontent.com/calvinmetcalf/rollup-plugin-node-builtins/master/src/es6/path.js\n\n/** JSDoc */\nfunction normalizeArray(parts: string[], allowAboveRoot?: boolean): string[] {\n  // if the path tries to go above the root, `up` ends up > 0\n  let up = 0;\n  for (let i = parts.length - 1; i >= 0; i--) {\n    const last = parts[i];\n    if (last === '.') {\n      parts.splice(i, 1);\n    } else if (last === '..') {\n      parts.splice(i, 1);\n      // eslint-disable-next-line no-plusplus\n      up++;\n    } else if (up) {\n      parts.splice(i, 1);\n      // eslint-disable-next-line no-plusplus\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (allowAboveRoot) {\n    // eslint-disable-next-line no-plusplus\n    for (; up--; up) {\n      parts.unshift('..');\n    }\n  }\n\n  return parts;\n}\n\n// Split a filename into [root, dir, basename, ext], unix version\n// 'root' is just a slash, or nothing.\nconst splitPathRe = /^(\\/?|)([\\s\\S]*?)((?:\\.{1,2}|[^/]+?|)(\\.[^./]*|))(?:[/]*)$/;\n/** JSDoc */\nfunction splitPath(filename: string): string[] {\n  const parts = splitPathRe.exec(filename);\n  return parts ? parts.slice(1) : [];\n}\n\n// path.resolve([from ...], to)\n// posix version\n/** JSDoc */\nexport function resolve(...args: string[]): string {\n  let resolvedPath = '';\n  let resolvedAbsolute = false;\n\n  for (let i = args.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n    const path = i >= 0 ? args[i] : '/';\n\n    // Skip empty entries\n    if (!path) {\n      continue;\n    }\n\n    resolvedPath = `${path}/${resolvedPath}`;\n    resolvedAbsolute = path.charAt(0) === '/';\n  }\n\n  // At this point the path should be resolved to a full absolute path, but\n  // handle relative paths to be safe (might happen when process.cwd() fails)\n\n  // Normalize the path\n  resolvedPath = normalizeArray(\n    resolvedPath.split('/').filter(p => !!p),\n    !resolvedAbsolute,\n  ).join('/');\n\n  return (resolvedAbsolute ? '/' : '') + resolvedPath || '.';\n}\n\n/** JSDoc */\nfunction trim(arr: string[]): string[] {\n  let start = 0;\n  for (; start < arr.length; start++) {\n    if (arr[start] !== '') {\n      break;\n    }\n  }\n\n  let end = arr.length - 1;\n  for (; end >= 0; end--) {\n    if (arr[end] !== '') {\n      break;\n    }\n  }\n\n  if (start > end) {\n    return [];\n  }\n  return arr.slice(start, end - start + 1);\n}\n\n// path.relative(from, to)\n// posix version\n/** JSDoc */\nexport function relative(from: string, to: string): string {\n  /* eslint-disable no-param-reassign */\n  from = resolve(from).substr(1);\n  to = resolve(to).substr(1);\n  /* eslint-enable no-param-reassign */\n\n  const fromParts = trim(from.split('/'));\n  const toParts = trim(to.split('/'));\n\n  const length = Math.min(fromParts.length, toParts.length);\n  let samePartsLength = length;\n  for (let i = 0; i < length; i++) {\n    if (fromParts[i] !== toParts[i]) {\n      samePartsLength = i;\n      break;\n    }\n  }\n\n  let outputParts = [];\n  for (let i = samePartsLength; i < fromParts.length; i++) {\n    outputParts.push('..');\n  }\n\n  outputParts = outputParts.concat(toParts.slice(samePartsLength));\n\n  return outputParts.join('/');\n}\n\n// path.normalize(path)\n// posix version\n/** JSDoc */\nexport function normalizePath(path: string): string {\n  const isPathAbsolute = isAbsolute(path);\n  const trailingSlash = path.substr(-1) === '/';\n\n  // Normalize the path\n  let normalizedPath = normalizeArray(\n    path.split('/').filter(p => !!p),\n    !isPathAbsolute,\n  ).join('/');\n\n  if (!normalizedPath && !isPathAbsolute) {\n    normalizedPath = '.';\n  }\n  if (normalizedPath && trailingSlash) {\n    normalizedPath += '/';\n  }\n\n  return (isPathAbsolute ? '/' : '') + normalizedPath;\n}\n\n// posix version\n/** JSDoc */\nexport function isAbsolute(path: string): boolean {\n  return path.charAt(0) === '/';\n}\n\n// posix version\n/** JSDoc */\nexport function join(...args: string[]): string {\n  return normalizePath(args.join('/'));\n}\n\n/** JSDoc */\nexport function dirname(path: string): string {\n  const result = splitPath(path);\n  const root = result[0];\n  let dir = result[1];\n\n  if (!root && !dir) {\n    // No dirname whatsoever\n    return '.';\n  }\n\n  if (dir) {\n    // It has a dirname, strip trailing slash\n    dir = dir.substr(0, dir.length - 1);\n  }\n\n  return root + dir;\n}\n\n/** JSDoc */\nexport function basename(path: string, ext?: string): string {\n  let f = splitPath(path)[2];\n  if (ext && f.substr(ext.length * -1) === ext) {\n    f = f.substr(0, f.length - ext.length);\n  }\n  return f;\n}\n"]}