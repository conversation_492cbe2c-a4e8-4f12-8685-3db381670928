{"version": 3, "file": "node.js", "sourceRoot": "", "sources": ["../src/node.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAChC,OAAO,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAErC;;;;GAIG;AACH,MAAM,UAAU,SAAS;IACvB,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,OAAO,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,kBAAkB,CAAC;AAC7G,CAAC;AAED;;;;GAIG;AACH,6EAA6E;AAC7E,MAAM,UAAU,cAAc,CAAC,GAAQ,EAAE,OAAe;IACtD,sEAAsE;IACtE,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC9B,CAAC;AAED,4EAA4E;AAC5E,IAAM,oBAAoB,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;AAE7F;;;;;;;GAOG;AACH,MAAM,UAAU,sBAAsB,CACpC,GAA2B,EAC3B,IAAqC;IAArC,qBAAA,EAAA,2BAAqC;IAErC,mDAAmD;IACnD,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;KAC9E;IAED,IAAM,WAAW,GAA2B,EAAE,CAAC;IAE/C,WAAW;IACX,+BAA+B;IAC/B,oBAAoB;IACpB,IAAM,OAAO,GAAG,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,MAAM,IAAI,EAAE,CAG/C,CAAC;IACF,UAAU;IACV,mCAAmC;IACnC,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,QAAQ;IACR,qDAAqD;IACrD,kBAAkB;IAClB,2BAA2B;IAC3B,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,WAAW,CAAC;IACrE,YAAY;IACZ,gBAAgB;IAChB,+BAA+B;IAC/B,IAAM,QAAQ,GACZ,GAAG,CAAC,QAAQ,KAAK,OAAO,IAAI,GAAG,CAAC,MAAM,IAAK,CAAC,GAAG,CAAC,MAAM,IAAI,EAAE,CAA6B,CAAC,SAAS;QACjG,CAAC,CAAC,OAAO;QACT,CAAC,CAAC,MAAM,CAAC;IACb,yCAAyC;IACzC,mCAAmC;IACnC,iBAAiB;IACjB,IAAM,WAAW,GAAG,CAAC,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG,IAAI,EAAE,CAAW,CAAC;IACjE,eAAe;IACf,IAAM,WAAW,GAAM,QAAQ,WAAM,IAAI,GAAG,WAAa,CAAC;IAE1D,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG;QACd,QAAQ,GAAG,EAAE;YACX,KAAK,SAAS;gBACZ,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;gBAC9B,MAAM;YACR,KAAK,QAAQ;gBACX,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;gBAC5B,MAAM;YACR,KAAK,KAAK;gBACR,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC;gBAC9B,MAAM;YACR,KAAK,SAAS;gBACZ,WAAW;gBACX,2CAA2C;gBAC3C,kEAAkE;gBAClE,sEAAsE;gBACtE,WAAW,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,IAAI,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;gBAClG,MAAM;YACR,KAAK,cAAc;gBACjB,gBAAgB;gBAChB,wBAAwB;gBACxB,4BAA4B;gBAC5B,sEAAsE;gBACtE,WAAW,CAAC,YAAY,GAAG,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC;gBAC/F,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,MAAM,EAAE;oBACzC,MAAM;iBACP;gBACD,aAAa;gBACb,iCAAiC;gBACjC,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE;oBAC1B,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;iBACxF;gBACD,MAAM;YACR;gBACE,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;oBACpC,WAAW,CAAC,GAAG,CAAC,GAAI,GAA8B,CAAC,GAAG,CAAC,CAAC;iBACzD;SACJ;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,WAAW,CAAC;AACrB,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { ExtractedNodeRequestData } from '@sentry/types';\n\nimport { isString } from './is';\nimport { normalize } from './object';\n\n/**\n * Checks whether we're in the Node.js or Browser environment\n *\n * @returns Answer to given question\n */\nexport function isNodeEnv(): boolean {\n  return Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]';\n}\n\n/**\n * Requires a module which is protected against bundler minification.\n *\n * @param request The module path to resolve\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function dynamicRequire(mod: any, request: string): any {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return mod.require(request);\n}\n\n/** Default request keys that'll be used to extract data from the request */\nconst DEFAULT_REQUEST_KEYS = ['cookies', 'data', 'headers', 'method', 'query_string', 'url'];\n\n/**\n * Normalizes data from the request object, accounting for framework differences.\n *\n * @param req The request object from which to extract data\n * @param keys An optional array of keys to include in the normalized data. Defaults to DEFAULT_REQUEST_KEYS if not\n * provided.\n * @returns An object containing normalized request data\n */\nexport function extractNodeRequestData(\n  req: { [key: string]: any },\n  keys: string[] = DEFAULT_REQUEST_KEYS,\n): ExtractedNodeRequestData {\n  // make sure we can safely use dynamicRequire below\n  if (!isNodeEnv()) {\n    throw new Error(\"Can't get node request data outside of a node environment\");\n  }\n\n  const requestData: { [key: string]: any } = {};\n\n  // headers:\n  //   node, express: req.headers\n  //   koa: req.header\n  const headers = (req.headers || req.header || {}) as {\n    host?: string;\n    cookie?: string;\n  };\n  // method:\n  //   node, express, koa: req.method\n  const method = req.method;\n  // host:\n  //   express: req.hostname in > 4 and req.host in < 4\n  //   koa: req.host\n  //   node: req.headers.host\n  const host = req.hostname || req.host || headers.host || '<no host>';\n  // protocol:\n  //   node: <n/a>\n  //   express, koa: req.protocol\n  const protocol =\n    req.protocol === 'https' || req.secure || ((req.socket || {}) as { encrypted?: boolean }).encrypted\n      ? 'https'\n      : 'http';\n  // url (including path and query string):\n  //   node, express: req.originalUrl\n  //   koa: req.url\n  const originalUrl = (req.originalUrl || req.url || '') as string;\n  // absolute url\n  const absoluteUrl = `${protocol}://${host}${originalUrl}`;\n\n  keys.forEach(key => {\n    switch (key) {\n      case 'headers':\n        requestData.headers = headers;\n        break;\n      case 'method':\n        requestData.method = method;\n        break;\n      case 'url':\n        requestData.url = absoluteUrl;\n        break;\n      case 'cookies':\n        // cookies:\n        //   node, express, koa: req.headers.cookie\n        //   vercel, sails.js, express (w/ cookie middleware): req.cookies\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        requestData.cookies = req.cookies || dynamicRequire(module, 'cookie').parse(headers.cookie || '');\n        break;\n      case 'query_string':\n        // query string:\n        //   node: req.url (raw)\n        //   express, koa: req.query\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        requestData.query_string = dynamicRequire(module, 'url').parse(originalUrl || '', false).query;\n        break;\n      case 'data':\n        if (method === 'GET' || method === 'HEAD') {\n          break;\n        }\n        // body data:\n        //   node, express, koa: req.body\n        if (req.body !== undefined) {\n          requestData.data = isString(req.body) ? req.body : JSON.stringify(normalize(req.body));\n        }\n        break;\n      default:\n        if ({}.hasOwnProperty.call(req, key)) {\n          requestData[key] = (req as { [key: string]: any })[key];\n        }\n    }\n  });\n\n  return requestData;\n}\n"]}