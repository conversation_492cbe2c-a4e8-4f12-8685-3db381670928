{"version": 3, "file": "supports.js", "sourceRoot": "", "sources": ["../src/supports.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,MAAM,QAAQ,CAAC;AAEzC;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB;IAChC,IAAI;QACF,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QACnB,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB;IAC9B,IAAI;QACF,qEAAqE;QACrE,2CAA2C;QAC3C,gDAAgD;QAChD,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,oBAAoB;IAClC,IAAI;QACF,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,aAAa;IAC3B,IAAI,CAAC,CAAC,OAAO,IAAI,eAAe,EAAU,CAAC,EAAE;QAC3C,OAAO,KAAK,CAAC;KACd;IAED,IAAI;QACF,IAAI,OAAO,EAAE,CAAC;QACd,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC;QAChB,IAAI,QAAQ,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AACD;;GAEG;AACH,wDAAwD;AACxD,SAAS,aAAa,CAAC,IAAc;IACnC,OAAO,IAAI,IAAI,kDAAkD,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC1F,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB;IACjC,IAAI,CAAC,aAAa,EAAE,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAM,MAAM,GAAG,eAAe,EAAU,CAAC;IAEzC,6BAA6B;IAC7B,6DAA6D;IAC7D,IAAI,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAC;KACb;IAED,iGAAiG;IACjG,4DAA4D;IAC5D,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,IAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC;IAC5B,mDAAmD;IACnD,IAAI,GAAG,IAAI,OAAQ,GAAG,CAAC,aAAyB,KAAK,UAAU,EAAE;QAC/D,IAAI;YACF,IAAM,OAAO,GAAG,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5C,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;YACtB,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE;gBACxD,6DAA6D;gBAC7D,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACrD;YACD,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC/B;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,iFAAiF,EAAE,GAAG,CAAC,CAAC;SACrG;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,yBAAyB;IACvC,OAAO,mBAAmB,IAAI,eAAe,EAAE,CAAC;AAClD,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,sBAAsB;IACpC,wHAAwH;IACxH,4CAA4C;IAC5C,2EAA2E;IAC3E,yDAAyD;IAEzD,IAAI,CAAC,aAAa,EAAE,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI;QACF,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,cAAc,EAAE,QAA0B;SAC3C,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,eAAe;IAC7B,4EAA4E;IAC5E,kFAAkF;IAClF,wEAAwE;IACxE,IAAM,MAAM,GAAG,eAAe,EAAU,CAAC;IACzC,+DAA+D;IAC/D,8DAA8D;IAC9D,IAAM,MAAM,GAAI,MAAc,CAAC,MAAM,CAAC;IACtC,IAAM,mBAAmB,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;IACvE,8DAA8D;IAC9D,IAAM,aAAa,GAAG,SAAS,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;IAEzG,OAAO,CAAC,mBAAmB,IAAI,aAAa,CAAC;AAC/C,CAAC", "sourcesContent": ["import { logger } from './logger';\nimport { getGlobalObject } from './misc';\n\n/**\n * Tells whether current environment supports ErrorEvent objects\n * {@link supportsErrorEvent}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsErrorEvent(): boolean {\n  try {\n    new ErrorEvent('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMError objects\n * {@link supportsDOMError}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsDOMError(): boolean {\n  try {\n    // Chrome: VM89:1 Uncaught TypeError: Failed to construct 'DOMError':\n    // 1 argument required, but only 0 present.\n    // @ts-ignore It really needs 1 argument, not 0.\n    new DOMError('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMException objects\n * {@link supportsDOMException}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsDOMException(): boolean {\n  try {\n    new DOMException('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports Fetch API\n * {@link supportsFetch}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsFetch(): boolean {\n  if (!('fetch' in getGlobalObject<Window>())) {\n    return false;\n  }\n\n  try {\n    new Headers();\n    new Request('');\n    new Response();\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n/**\n * isNativeFetch checks if the given function is a native implementation of fetch()\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isNativeFetch(func: Function): boolean {\n  return func && /^function fetch\\(\\)\\s+\\{\\s+\\[native code\\]\\s+\\}$/.test(func.toString());\n}\n\n/**\n * Tells whether current environment supports Fetch API natively\n * {@link supportsNativeFetch}.\n *\n * @returns true if `window.fetch` is natively implemented, false otherwise\n */\nexport function supportsNativeFetch(): boolean {\n  if (!supportsFetch()) {\n    return false;\n  }\n\n  const global = getGlobalObject<Window>();\n\n  // Fast path to avoid DOM I/O\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  if (isNativeFetch(global.fetch)) {\n    return true;\n  }\n\n  // window.fetch is implemented, but is polyfilled or already wrapped (e.g: by a chrome extension)\n  // so create a \"pure\" iframe to see if that has native fetch\n  let result = false;\n  const doc = global.document;\n  // eslint-disable-next-line deprecation/deprecation\n  if (doc && typeof (doc.createElement as unknown) === `function`) {\n    try {\n      const sandbox = doc.createElement('iframe');\n      sandbox.hidden = true;\n      doc.head.appendChild(sandbox);\n      if (sandbox.contentWindow && sandbox.contentWindow.fetch) {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        result = isNativeFetch(sandbox.contentWindow.fetch);\n      }\n      doc.head.removeChild(sandbox);\n    } catch (err) {\n      logger.warn('Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ', err);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Tells whether current environment supports ReportingObserver API\n * {@link supportsReportingObserver}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsReportingObserver(): boolean {\n  return 'ReportingObserver' in getGlobalObject();\n}\n\n/**\n * Tells whether current environment supports Referrer Policy API\n * {@link supportsReferrerPolicy}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsReferrerPolicy(): boolean {\n  // Despite all stars in the sky saying that Edge supports old draft syntax, aka 'never', 'always', 'origin' and 'default\n  // https://caniuse.com/#feat=referrer-policy\n  // It doesn't. And it throw exception instead of ignoring this parameter...\n  // REF: https://github.com/getsentry/raven-js/issues/1233\n\n  if (!supportsFetch()) {\n    return false;\n  }\n\n  try {\n    new Request('_', {\n      referrerPolicy: 'origin' as ReferrerPolicy,\n    });\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports History API\n * {@link supportsHistory}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsHistory(): boolean {\n  // NOTE: in Chrome App environment, touching history.pushState, *even inside\n  //       a try/catch block*, will cause Chrome to output an error to console.error\n  // borrowed from: https://github.com/angular/angular.js/pull/13945/files\n  const global = getGlobalObject<Window>();\n  /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const chrome = (global as any).chrome;\n  const isChromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n  /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n  const hasHistoryApi = 'history' in global && !!global.history.pushState && !!global.history.replaceState;\n\n  return !isChromePackagedApp && hasHistoryApi;\n}\n"]}