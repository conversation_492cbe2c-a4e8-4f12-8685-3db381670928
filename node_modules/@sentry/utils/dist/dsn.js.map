{"version": 3, "file": "dsn.js", "sourceRoot": "", "sources": ["../src/dsn.ts"], "names": [], "mappings": ";;AAEA,iCAAsC;AAEtC,8CAA8C;AAC9C,IAAM,SAAS,GAAG,gEAAgE,CAAC;AAEnF,oBAAoB;AACpB,IAAM,aAAa,GAAG,aAAa,CAAC;AAEpC,iEAAiE;AACjE;IAgBE,kCAAkC;IAClC,aAAmB,IAAa;QAC9B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACxB;aAAM;YACL,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAED;;;;;;;;OAQG;IACI,sBAAQ,GAAf,UAAgB,YAA6B;QAA7B,6BAAA,EAAA,oBAA6B;QACrC,IAAA,SAA4D,EAA1D,cAAI,EAAE,cAAI,EAAE,cAAI,EAAE,cAAI,EAAE,wBAAS,EAAE,sBAAQ,EAAE,cAAa,CAAC;QACnE,OAAO,CACF,QAAQ,WAAM,IAAI,IAAG,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,MAAI,IAAM,CAAC,CAAC,CAAC,EAAE,CAAE;aAChE,MAAI,IAAI,IAAG,IAAI,CAAC,CAAC,CAAC,MAAI,IAAM,CAAC,CAAC,CAAC,EAAE,WAAI,IAAI,CAAC,CAAC,CAAI,IAAI,MAAG,CAAC,CAAC,CAAC,IAAI,IAAG,SAAW,CAAA,CAC5E,CAAC;IACJ,CAAC;IAED,qCAAqC;IAC7B,yBAAW,GAAnB,UAAoB,GAAW;QAC7B,IAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,IAAI,mBAAW,CAAC,aAAa,CAAC,CAAC;SACtC;QAEK,IAAA,sCAAuE,EAAtE,gBAAQ,EAAE,YAAI,EAAE,UAAS,EAAT,8BAAS,EAAE,YAAI,EAAE,UAAS,EAAT,8BAAS,EAAE,gBAA0B,CAAC;QAC9E,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,SAAS,GAAG,QAAQ,CAAC;QAEzB,IAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAY,CAAC;SACnC;QAED,IAAI,SAAS,EAAE;YACb,IAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,YAAY,EAAE;gBAChB,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;aAC7B;SACF;QAED,IAAI,CAAC,eAAe,CAAC,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAE,SAAS,WAAA,EAAE,IAAI,MAAA,EAAE,QAAQ,EAAE,QAAuB,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;IACvG,CAAC;IAED,8CAA8C;IACtC,6BAAe,GAAvB,UAAwB,UAAyB;QAC/C,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC;QAClC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC;QAClC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC;QAClC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;IACxC,CAAC;IAED,8CAA8C;IACtC,uBAAS,GAAjB;QAAA,iBAkBC;QAjBC,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,UAAA,SAAS;YACzD,IAAI,CAAC,KAAI,CAAC,SAAgC,CAAC,EAAE;gBAC3C,MAAM,IAAI,mBAAW,CAAI,aAAa,UAAK,SAAS,aAAU,CAAC,CAAC;aACjE;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YAClC,MAAM,IAAI,mBAAW,CAAI,aAAa,4BAAuB,IAAI,CAAC,SAAW,CAAC,CAAC;SAChF;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YACzD,MAAM,IAAI,mBAAW,CAAI,aAAa,2BAAsB,IAAI,CAAC,QAAU,CAAC,CAAC;SAC9E;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE;YAC/C,MAAM,IAAI,mBAAW,CAAI,aAAa,uBAAkB,IAAI,CAAC,IAAM,CAAC,CAAC;SACtE;IACH,CAAC;IACH,UAAC;AAAD,CAAC,AAvGD,IAuGC;AAvGY,kBAAG", "sourcesContent": ["import { DsnC<PERSON><PERSON>, DsnLike, DsnProtocol } from '@sentry/types';\n\nimport { SentryError } from './error';\n\n/** Regular expression used to parse a Dsn. */\nconst DSN_REGEX = /^(?:(\\w+):)\\/\\/(?:(\\w+)(?::(\\w+))?@)([\\w.-]+)(?::(\\d+))?\\/(.+)/;\n\n/** Error message */\nconst ERROR_MESSAGE = 'Invalid Dsn';\n\n/** The Sentry Dsn, identifying a Sentry instance and project. */\nexport class Dsn implements DsnComponents {\n  /** Protocol used to connect to Sentry. */\n  public protocol!: DsnProtocol;\n  /** Public authorization key. */\n  public user!: string;\n  /** Private authorization key (deprecated, optional). */\n  public pass!: string;\n  /** Hostname of the Sentry instance. */\n  public host!: string;\n  /** Port of the Sentry instance. */\n  public port!: string;\n  /** Path */\n  public path!: string;\n  /** Project ID */\n  public projectId!: string;\n\n  /** Creates a new Dsn component */\n  public constructor(from: DsnLike) {\n    if (typeof from === 'string') {\n      this._fromString(from);\n    } else {\n      this._fromComponents(from);\n    }\n\n    this._validate();\n  }\n\n  /**\n   * Renders the string representation of this Dsn.\n   *\n   * By default, this will render the public representation without the password\n   * component. To get the deprecated private representation, set `withPassword`\n   * to true.\n   *\n   * @param withPassword When set to true, the password will be included.\n   */\n  public toString(withPassword: boolean = false): string {\n    const { host, path, pass, port, projectId, protocol, user } = this;\n    return (\n      `${protocol}://${user}${withPassword && pass ? `:${pass}` : ''}` +\n      `@${host}${port ? `:${port}` : ''}/${path ? `${path}/` : path}${projectId}`\n    );\n  }\n\n  /** Parses a string into this Dsn. */\n  private _fromString(str: string): void {\n    const match = DSN_REGEX.exec(str);\n\n    if (!match) {\n      throw new SentryError(ERROR_MESSAGE);\n    }\n\n    const [protocol, user, pass = '', host, port = '', lastPath] = match.slice(1);\n    let path = '';\n    let projectId = lastPath;\n\n    const split = projectId.split('/');\n    if (split.length > 1) {\n      path = split.slice(0, -1).join('/');\n      projectId = split.pop() as string;\n    }\n\n    if (projectId) {\n      const projectMatch = projectId.match(/^\\d+/);\n      if (projectMatch) {\n        projectId = projectMatch[0];\n      }\n    }\n\n    this._fromComponents({ host, pass, path, projectId, port, protocol: protocol as DsnProtocol, user });\n  }\n\n  /** Maps Dsn components into this instance. */\n  private _fromComponents(components: DsnComponents): void {\n    this.protocol = components.protocol;\n    this.user = components.user;\n    this.pass = components.pass || '';\n    this.host = components.host;\n    this.port = components.port || '';\n    this.path = components.path || '';\n    this.projectId = components.projectId;\n  }\n\n  /** Validates this Dsn and throws on error. */\n  private _validate(): void {\n    ['protocol', 'user', 'host', 'projectId'].forEach(component => {\n      if (!this[component as keyof DsnComponents]) {\n        throw new SentryError(`${ERROR_MESSAGE}: ${component} missing`);\n      }\n    });\n\n    if (!this.projectId.match(/^\\d+$/)) {\n      throw new SentryError(`${ERROR_MESSAGE}: Invalid projectId ${this.projectId}`);\n    }\n\n    if (this.protocol !== 'http' && this.protocol !== 'https') {\n      throw new SentryError(`${ERROR_MESSAGE}: Invalid protocol ${this.protocol}`);\n    }\n\n    if (this.port && isNaN(parseInt(this.port, 10))) {\n      throw new SentryError(`${ERROR_MESSAGE}: Invalid port ${this.port}`);\n    }\n  }\n}\n"]}