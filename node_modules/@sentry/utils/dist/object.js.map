{"version": 3, "file": "object.js", "sourceRoot": "", "sources": ["../src/object.ts"], "names": [], "mappings": ";;AAGA,qCAA6C;AAC7C,2BAA+G;AAC/G,+BAA8B;AAC9B,2CAA+C;AAC/C,mCAAoC;AAEpC;;;;;;;;GAQG;AACH,SAAgB,IAAI,CAAC,MAA8B,EAAE,IAAY,EAAE,kBAA2C;IAC5G,IAAI,CAAC,CAAC,IAAI,IAAI,MAAM,CAAC,EAAE;QACrB,OAAO;KACR;IAED,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAc,CAAC;IAC3C,IAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,CAAoB,CAAC;IAEhE,0GAA0G;IAC1G,kFAAkF;IAClF,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;QACjC,IAAI;YACF,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE;gBAC/B,mBAAmB,EAAE;oBACnB,UAAU,EAAE,KAAK;oBACjB,KAAK,EAAE,QAAQ;iBAChB;aACF,CAAC,CAAC;SACJ;QAAC,OAAO,GAAG,EAAE;YACZ,iFAAiF;YACjF,mEAAmE;SACpE;KACF;IAED,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;AACzB,CAAC;AA1BD,oBA0BC;AAED;;;;;GAKG;AACH,SAAgB,SAAS,CAAC,MAA8B;IACtD,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;SACvB,GAAG,CAAC,UAAA,GAAG,IAAI,OAAG,kBAAkB,CAAC,GAAG,CAAC,SAAI,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAG,EAA/D,CAA+D,CAAC;SAC3E,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAJD,8BAIC;AAED;;;;;GAKG;AACH,SAAS,aAAa,CACpB,KAAU;IAIV,IAAI,YAAO,CAAC,KAAK,CAAC,EAAE;QAClB,IAAM,KAAK,GAAG,KAAsB,CAAC;QACrC,IAAM,GAAG,GAKL;YACF,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC;QAEF,KAAK,IAAM,CAAC,IAAI,KAAK,EAAE;YACrB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE;gBAClD,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;aACnB;SACF;QAED,OAAO,GAAG,CAAC;KACZ;IAED,IAAI,YAAO,CAAC,KAAK,CAAC,EAAE;QAWlB,IAAM,OAAK,GAAG,KAAoB,CAAC;QAEnC,IAAM,MAAM,GAER,EAAE,CAAC;QAEP,MAAM,CAAC,IAAI,GAAG,OAAK,CAAC,IAAI,CAAC;QAEzB,sEAAsE;QACtE,IAAI;YACF,MAAM,CAAC,MAAM,GAAG,cAAS,CAAC,OAAK,CAAC,MAAM,CAAC;gBACrC,CAAC,CAAC,0BAAgB,CAAC,OAAK,CAAC,MAAM,CAAC;gBAChC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAK,CAAC,MAAM,CAAC,CAAC;SAClD;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC;SAC7B;QAED,IAAI;YACF,MAAM,CAAC,aAAa,GAAG,cAAS,CAAC,OAAK,CAAC,aAAa,CAAC;gBACnD,CAAC,CAAC,0BAAgB,CAAC,OAAK,CAAC,aAAa,CAAC;gBACvC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAK,CAAC,aAAa,CAAC,CAAC;SACzD;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,CAAC,aAAa,GAAG,WAAW,CAAC;SACpC;QAED,IAAI,OAAO,WAAW,KAAK,WAAW,IAAI,iBAAY,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE;YAC1E,MAAM,CAAC,MAAM,GAAG,OAAK,CAAC,MAAM,CAAC;SAC9B;QAED,KAAK,IAAM,CAAC,IAAI,OAAK,EAAE;YACrB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAK,EAAE,CAAC,CAAC,EAAE;gBAClD,MAAM,CAAC,CAAC,CAAC,GAAG,OAAK,CAAC;aACnB;SACF;QAED,OAAO,MAAM,CAAC;KACf;IAED,OAAO,KAEN,CAAC;AACJ,CAAC;AAED,4CAA4C;AAC5C,SAAS,UAAU,CAAC,KAAa;IAC/B,sCAAsC;IACtC,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AAClD,CAAC;AAED,4CAA4C;AAC5C,SAAS,QAAQ,CAAC,KAAU;IAC1B,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3C,CAAC;AAED,YAAY;AACZ,SAAgB,eAAe,CAC7B,MAA8B;AAC9B,6BAA6B;AAC7B,KAAiB;AACjB,iEAAiE;AACjE,OAA4B;IAF5B,sBAAA,EAAA,SAAiB;IAEjB,wBAAA,EAAA,UAAkB,GAAG,GAAG,IAAI;IAE5B,IAAM,UAAU,GAAG,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAE5C,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,OAAO,EAAE;QAClC,OAAO,eAAe,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;KACpD;IAED,OAAO,UAAe,CAAC;AACzB,CAAC;AAdD,0CAcC;AAED;;;;;;;;GAQG;AACH,SAAS,cAAc,CAAC,KAAU;IAChC,IAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEnD,wBAAwB;IACxB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,KAAK,CAAC;KACd;IACD,IAAI,IAAI,KAAK,iBAAiB,EAAE;QAC9B,OAAO,UAAU,CAAC;KACnB;IACD,IAAI,IAAI,KAAK,gBAAgB,EAAE;QAC7B,OAAO,SAAS,CAAC;KAClB;IAED,IAAM,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;IACzC,OAAO,gBAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;AACrD,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,cAAc,CAAI,KAAQ,EAAE,GAAS;IAC5C,IAAI,GAAG,KAAK,QAAQ,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAM,KAAsC,CAAC,OAAO,EAAE;QAC9G,OAAO,UAAU,CAAC;KACnB;IAED,IAAI,GAAG,KAAK,eAAe,EAAE;QAC3B,OAAO,iBAAiB,CAAC;KAC1B;IAED,IAAI,OAAQ,MAAc,KAAK,WAAW,IAAK,KAAiB,KAAK,MAAM,EAAE;QAC3E,OAAO,UAAU,CAAC;KACnB;IAED,IAAI,OAAQ,MAAc,KAAK,WAAW,IAAK,KAAiB,KAAK,MAAM,EAAE;QAC3E,OAAO,UAAU,CAAC;KACnB;IAED,IAAI,OAAQ,QAAgB,KAAK,WAAW,IAAK,KAAiB,KAAK,QAAQ,EAAE;QAC/E,OAAO,YAAY,CAAC;KACrB;IAED,gCAAgC;IAChC,IAAI,qBAAgB,CAAC,KAAK,CAAC,EAAE;QAC3B,OAAO,kBAAkB,CAAC;KAC3B;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,KAAK,EAAE;QAChD,OAAO,OAAO,CAAC;KAChB;IAED,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE;QACpB,OAAO,aAAa,CAAC;KACtB;IAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;QAC/B,OAAO,gBAAc,4BAAe,CAAC,KAAK,CAAC,MAAG,CAAC;KAChD;IAED,6FAA6F;IAE7F,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,MAAI,MAAM,CAAC,KAAK,CAAC,MAAG,CAAC;KAC7B;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,cAAY,MAAM,CAAC,KAAK,CAAC,MAAG,CAAC;KACrC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;GAOG;AACH,6EAA6E;AAC7E,SAAgB,IAAI,CAAC,GAAW,EAAE,KAAU,EAAE,KAAyB,EAAE,IAAuB;IAAlD,sBAAA,EAAA,SAAiB,QAAQ;IAAE,qBAAA,EAAA,WAAiB,WAAI,EAAE;IAC9F,6DAA6D;IAC7D,IAAI,KAAK,KAAK,CAAC,EAAE;QACf,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;KAC9B;IAED,+DAA+D;IAC/D,gEAAgE;IAChE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE;QAC/E,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;KACvB;IACD,8DAA8D;IAE9D,4JAA4J;IAC5J,IAAM,UAAU,GAAG,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9C,IAAI,gBAAW,CAAC,UAAU,CAAC,EAAE;QAC3B,OAAO,UAAU,CAAC;KACnB;IAED,wJAAwJ;IACxJ,IAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;IAEpC,4FAA4F;IAC5F,IAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAE3C,yEAAyE;IACzE,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,cAAc,CAAC;KACvB;IAED,8BAA8B;IAC9B,KAAK,IAAM,QAAQ,IAAI,MAAM,EAAE;QAC7B,+FAA+F;QAC/F,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;YAC3D,SAAS;SACV;QACD,+CAA+C;QAC9C,GAA8B,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;KAC/F;IAED,4EAA4E;IAC5E,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAEtB,4BAA4B;IAC5B,OAAO,GAAG,CAAC;AACb,CAAC;AA7CD,oBA6CC;AAED;;;;;;;;;;;GAWG;AACH,6EAA6E;AAC7E,SAAgB,SAAS,CAAC,KAAU,EAAE,KAAc;IAClD,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAC,GAAW,EAAE,KAAU,IAAK,OAAA,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,EAAvB,CAAuB,CAAC,CAAC,CAAC;KAChG;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,sBAAsB,CAAC;KAC/B;AACH,CAAC;AAND,8BAMC;AAED;;;;GAIG;AACH,6EAA6E;AAC7E,SAAgB,8BAA8B,CAAC,SAAc,EAAE,SAAsB;IAAtB,0BAAA,EAAA,cAAsB;IACnF,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;IACnD,IAAI,CAAC,IAAI,EAAE,CAAC;IAEZ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAChB,OAAO,sBAAsB,CAAC;KAC/B;IAED,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,SAAS,EAAE;QAC/B,OAAO,iBAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;KACrC;IAED,KAAK,IAAI,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,EAAE,EAAE;QACrE,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,UAAU,CAAC,MAAM,GAAG,SAAS,EAAE;YACjC,SAAS;SACV;QACD,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,EAAE;YAChC,OAAO,UAAU,CAAC;SACnB;QACD,OAAO,iBAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;KACxC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAxBD,wEAwBC;AAED;;;GAGG;AACH,SAAgB,iBAAiB,CAAI,GAAM;;IACzC,IAAI,kBAAa,CAAC,GAAG,CAAC,EAAE;QACtB,IAAM,GAAG,GAAG,GAA6B,CAAC;QAC1C,IAAM,EAAE,GAA2B,EAAE,CAAC;;YACtC,KAAkB,IAAA,KAAA,iBAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,gBAAA,4BAAE;gBAA/B,IAAM,GAAG,WAAA;gBACZ,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;oBACnC,EAAE,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;iBACvC;aACF;;;;;;;;;QACD,OAAO,EAAO,CAAC;KAChB;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACtB,OAAQ,GAAa,CAAC,GAAG,CAAC,iBAAiB,CAAQ,CAAC;KACrD;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAjBD,8CAiBC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { ExtendedError, WrappedFunction } from '@sentry/types';\n\nimport { htmlTreeAsString } from './browser';\nimport { isElement, isError, isEvent, isInstanceOf, isPlainObject, isPrimitive, isSyntheticEvent } from './is';\nimport { Memo } from './memo';\nimport { getFunctionName } from './stacktrace';\nimport { truncate } from './string';\n\n/**\n * Wrap a given object method with a higher-order function\n *\n * @param source An object that contains a method to be wrapped.\n * @param name A name of method to be wrapped.\n * @param replacementFactory A function that should be used to wrap a given method, returning the wrapped method which\n * will be substituted in for `source[name]`.\n * @returns void\n */\nexport function fill(source: { [key: string]: any }, name: string, replacementFactory: (...args: any[]) => any): void {\n  if (!(name in source)) {\n    return;\n  }\n\n  const original = source[name] as () => any;\n  const wrapped = replacementFactory(original) as WrappedFunction;\n\n  // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n  // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n  if (typeof wrapped === 'function') {\n    try {\n      wrapped.prototype = wrapped.prototype || {};\n      Object.defineProperties(wrapped, {\n        __sentry_original__: {\n          enumerable: false,\n          value: original,\n        },\n      });\n    } catch (_Oo) {\n      // This can throw if multiple fill happens on a global object like XMLHttpRequest\n      // Fixes https://github.com/getsentry/sentry-javascript/issues/2043\n    }\n  }\n\n  source[name] = wrapped;\n}\n\n/**\n * Encodes given object into url-friendly format\n *\n * @param object An object that contains serializable values\n * @returns string Encoded\n */\nexport function urlEncode(object: { [key: string]: any }): string {\n  return Object.keys(object)\n    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(object[key])}`)\n    .join('&');\n}\n\n/**\n * Transforms any object into an object literal with all its attributes\n * attached to it.\n *\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\n */\nfunction getWalkSource(\n  value: any,\n): {\n  [key: string]: any;\n} {\n  if (isError(value)) {\n    const error = value as ExtendedError;\n    const err: {\n      [key: string]: any;\n      stack: string | undefined;\n      message: string;\n      name: string;\n    } = {\n      message: error.message,\n      name: error.name,\n      stack: error.stack,\n    };\n\n    for (const i in error) {\n      if (Object.prototype.hasOwnProperty.call(error, i)) {\n        err[i] = error[i];\n      }\n    }\n\n    return err;\n  }\n\n  if (isEvent(value)) {\n    /**\n     * Event-like interface that's usable in browser and node\n     */\n    interface SimpleEvent {\n      [key: string]: unknown;\n      type: string;\n      target?: unknown;\n      currentTarget?: unknown;\n    }\n\n    const event = value as SimpleEvent;\n\n    const source: {\n      [key: string]: any;\n    } = {};\n\n    source.type = event.type;\n\n    // Accessing event.target can throw (see getsentry/raven-js#838, #768)\n    try {\n      source.target = isElement(event.target)\n        ? htmlTreeAsString(event.target)\n        : Object.prototype.toString.call(event.target);\n    } catch (_oO) {\n      source.target = '<unknown>';\n    }\n\n    try {\n      source.currentTarget = isElement(event.currentTarget)\n        ? htmlTreeAsString(event.currentTarget)\n        : Object.prototype.toString.call(event.currentTarget);\n    } catch (_oO) {\n      source.currentTarget = '<unknown>';\n    }\n\n    if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n      source.detail = event.detail;\n    }\n\n    for (const i in event) {\n      if (Object.prototype.hasOwnProperty.call(event, i)) {\n        source[i] = event;\n      }\n    }\n\n    return source;\n  }\n\n  return value as {\n    [key: string]: any;\n  };\n}\n\n/** Calculates bytes size of input string */\nfunction utf8Length(value: string): number {\n  // eslint-disable-next-line no-bitwise\n  return ~-encodeURI(value).split(/%..|./).length;\n}\n\n/** Calculates bytes size of input object */\nfunction jsonSize(value: any): number {\n  return utf8Length(JSON.stringify(value));\n}\n\n/** JSDoc */\nexport function normalizeToSize<T>(\n  object: { [key: string]: any },\n  // Default Node.js REPL depth\n  depth: number = 3,\n  // 100kB, as 200kB is max payload size, so half sounds reasonable\n  maxSize: number = 100 * 1024,\n): T {\n  const serialized = normalize(object, depth);\n\n  if (jsonSize(serialized) > maxSize) {\n    return normalizeToSize(object, depth - 1, maxSize);\n  }\n\n  return serialized as T;\n}\n\n/**\n * Transform any non-primitive, BigInt, or Symbol-type value into a string. Acts as a no-op on strings, numbers,\n * booleans, null, and undefined.\n *\n * @param value The value to stringify\n * @returns For non-primitive, BigInt, and Symbol-type values, a string denoting the value's type, type and value, or\n *  type and `description` property, respectively. For non-BigInt, non-Symbol primitives, returns the original value,\n *  unchanged.\n */\nfunction serializeValue(value: any): any {\n  const type = Object.prototype.toString.call(value);\n\n  // Node.js REPL notation\n  if (typeof value === 'string') {\n    return value;\n  }\n  if (type === '[object Object]') {\n    return '[Object]';\n  }\n  if (type === '[object Array]') {\n    return '[Array]';\n  }\n\n  const normalized = normalizeValue(value);\n  return isPrimitive(normalized) ? normalized : type;\n}\n\n/**\n * normalizeValue()\n *\n * Takes unserializable input and make it serializable friendly\n *\n * - translates undefined/NaN values to \"[undefined]\"/\"[NaN]\" respectively,\n * - serializes Error objects\n * - filter global objects\n */\nfunction normalizeValue<T>(value: T, key?: any): T | string {\n  if (key === 'domain' && value && typeof value === 'object' && ((value as unknown) as { _events: any })._events) {\n    return '[Domain]';\n  }\n\n  if (key === 'domainEmitter') {\n    return '[DomainEmitter]';\n  }\n\n  if (typeof (global as any) !== 'undefined' && (value as unknown) === global) {\n    return '[Global]';\n  }\n\n  if (typeof (window as any) !== 'undefined' && (value as unknown) === window) {\n    return '[Window]';\n  }\n\n  if (typeof (document as any) !== 'undefined' && (value as unknown) === document) {\n    return '[Document]';\n  }\n\n  // React's SyntheticEvent thingy\n  if (isSyntheticEvent(value)) {\n    return '[SyntheticEvent]';\n  }\n\n  if (typeof value === 'number' && value !== value) {\n    return '[NaN]';\n  }\n\n  if (value === void 0) {\n    return '[undefined]';\n  }\n\n  if (typeof value === 'function') {\n    return `[Function: ${getFunctionName(value)}]`;\n  }\n\n  // symbols and bigints are considered primitives by TS, but aren't natively JSON-serilaizable\n\n  if (typeof value === 'symbol') {\n    return `[${String(value)}]`;\n  }\n\n  if (typeof value === 'bigint') {\n    return `[BigInt: ${String(value)}]`;\n  }\n\n  return value;\n}\n\n/**\n * Walks an object to perform a normalization on it\n *\n * @param key of object that's walked in current iteration\n * @param value object to be walked\n * @param depth Optional number indicating how deep should walking be performed\n * @param memo Optional Memo class handling decycling\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function walk(key: string, value: any, depth: number = +Infinity, memo: Memo = new Memo()): any {\n  // If we reach the maximum depth, serialize whatever has left\n  if (depth === 0) {\n    return serializeValue(value);\n  }\n\n  /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n  // If value implements `toJSON` method, call it and return early\n  if (value !== null && value !== undefined && typeof value.toJSON === 'function') {\n    return value.toJSON();\n  }\n  /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n\n  // If normalized value is a primitive, there are no branches left to walk, so we can just bail out, as theres no point in going down that branch any further\n  const normalized = normalizeValue(value, key);\n  if (isPrimitive(normalized)) {\n    return normalized;\n  }\n\n  // Create source that we will use for next itterations, either objectified error object (Error type with extracted keys:value pairs) or the input itself\n  const source = getWalkSource(value);\n\n  // Create an accumulator that will act as a parent for all future itterations of that branch\n  const acc = Array.isArray(value) ? [] : {};\n\n  // If we already walked that branch, bail out, as it's circular reference\n  if (memo.memoize(value)) {\n    return '[Circular ~]';\n  }\n\n  // Walk all keys of the source\n  for (const innerKey in source) {\n    // Avoid iterating over fields in the prototype if they've somehow been exposed to enumeration.\n    if (!Object.prototype.hasOwnProperty.call(source, innerKey)) {\n      continue;\n    }\n    // Recursively walk through all the child nodes\n    (acc as { [key: string]: any })[innerKey] = walk(innerKey, source[innerKey], depth - 1, memo);\n  }\n\n  // Once walked through all the branches, remove the parent from memo storage\n  memo.unmemoize(value);\n\n  // Return accumulated values\n  return acc;\n}\n\n/**\n * normalize()\n *\n * - Creates a copy to prevent original input mutation\n * - Skip non-enumerablers\n * - Calls `toJSON` if implemented\n * - Removes circular references\n * - Translates non-serializeable values (undefined/NaN/Functions) to serializable format\n * - Translates known global objects/Classes to a string representations\n * - Takes care of Error objects serialization\n * - Optionally limit depth of final output\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function normalize(input: any, depth?: number): any {\n  try {\n    return JSON.parse(JSON.stringify(input, (key: string, value: any) => walk(key, value, depth)));\n  } catch (_oO) {\n    return '**non-serializable**';\n  }\n}\n\n/**\n * Given any captured exception, extract its keys and create a sorted\n * and truncated list that will be used inside the event message.\n * eg. `Non-error exception captured with keys: foo, bar, baz`\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function extractExceptionKeysForMessage(exception: any, maxLength: number = 40): string {\n  const keys = Object.keys(getWalkSource(exception));\n  keys.sort();\n\n  if (!keys.length) {\n    return '[object has no keys]';\n  }\n\n  if (keys[0].length >= maxLength) {\n    return truncate(keys[0], maxLength);\n  }\n\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n\n  return '';\n}\n\n/**\n * Given any object, return the new object with removed keys that value was `undefined`.\n * Works recursively on objects and arrays.\n */\nexport function dropUndefinedKeys<T>(val: T): T {\n  if (isPlainObject(val)) {\n    const obj = val as { [key: string]: any };\n    const rv: { [key: string]: any } = {};\n    for (const key of Object.keys(obj)) {\n      if (typeof obj[key] !== 'undefined') {\n        rv[key] = dropUndefinedKeys(obj[key]);\n      }\n    }\n    return rv as T;\n  }\n\n  if (Array.isArray(val)) {\n    return (val as any[]).map(dropUndefinedKeys) as any;\n  }\n\n  return val;\n}\n"]}