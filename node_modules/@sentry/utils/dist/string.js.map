{"version": 3, "file": "string.js", "sourceRoot": "", "sources": ["../src/string.ts"], "names": [], "mappings": ";AAAA,2BAA0C;AAE1C;;;;;;GAMG;AACH,SAAgB,QAAQ,CAAC,GAAW,EAAE,GAAe;IAAf,oBAAA,EAAA,OAAe;IACnD,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,CAAC,EAAE;QACxC,OAAO,GAAG,CAAC;KACZ;IACD,OAAO,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAI,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,QAAK,CAAC;AAC9D,CAAC;AALD,4BAKC;AAED;;;;;;;GAOG;AACH,SAAgB,QAAQ,CAAC,IAAY,EAAE,KAAa;IAClD,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,IAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;IAC1B,IAAI,EAAE,IAAI,GAAG,EAAE;QACb,OAAO,OAAO,CAAC;KAChB;IACD,IAAI,KAAK,GAAG,EAAE,EAAE;QACd,6CAA6C;QAC7C,KAAK,GAAG,EAAE,CAAC;KACZ;IAED,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IACpC,IAAI,KAAK,GAAG,CAAC,EAAE;QACb,KAAK,GAAG,CAAC,CAAC;KACX;IAED,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;IACpC,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,EAAE;QAChB,GAAG,GAAG,EAAE,CAAC;KACV;IACD,IAAI,GAAG,KAAK,EAAE,EAAE;QACd,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;KAChC;IAED,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACpC,IAAI,KAAK,GAAG,CAAC,EAAE;QACb,OAAO,GAAG,aAAW,OAAS,CAAC;KAChC;IACD,IAAI,GAAG,GAAG,EAAE,EAAE;QACZ,OAAO,IAAI,SAAS,CAAC;KACtB;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAjCD,4BAiCC;AAED;;;;;GAKG;AACH,8DAA8D;AAC9D,SAAgB,QAAQ,CAAC,KAAY,EAAE,SAAkB;IACvD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACzB,OAAO,EAAE,CAAC;KACX;IAED,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,4DAA4D;IAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI;YACF,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;SAC5B;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;SAC7C;KACF;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChC,CAAC;AAjBD,4BAiBC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,KAAa,EAAE,OAAwB;IACvE,IAAI,CAAC,aAAQ,CAAC,KAAK,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,aAAQ,CAAC,OAAO,CAAC,EAAE;QACrB,OAAQ,OAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACxC;IACD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;KACtC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAZD,8CAYC", "sourcesContent": ["import { isRegExp, isString } from './is';\n\n/**\n * Truncates given string to the maximum characters count\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string (0 = unlimited)\n * @returns string Encoded\n */\nexport function truncate(str: string, max: number = 0): string {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.substr(0, max)}...`;\n}\n\n/**\n * This is basically just `trim_line` from\n * https://github.com/getsentry/sentry/blob/master/src/sentry/lang/javascript/processor.py#L67\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string\n * @returns string Encoded\n */\nexport function snipLine(line: string, colno: number): string {\n  let newLine = line;\n  const ll = newLine.length;\n  if (ll <= 150) {\n    return newLine;\n  }\n  if (colno > ll) {\n    // eslint-disable-next-line no-param-reassign\n    colno = ll;\n  }\n\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n\n  let end = Math.min(start + 140, ll);\n  if (end > ll - 5) {\n    end = ll;\n  }\n  if (end === ll) {\n    start = Math.max(end - 140, 0);\n  }\n\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `'{snip} ${newLine}`;\n  }\n  if (end < ll) {\n    newLine += ' {snip}';\n  }\n\n  return newLine;\n}\n\n/**\n * Join values in array\n * @param input array of values to be joined together\n * @param delimiter string to be placed in-between values\n * @returns Joined values\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function safeJoin(input: any[], delimiter?: string): string {\n  if (!Array.isArray(input)) {\n    return '';\n  }\n\n  const output = [];\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < input.length; i++) {\n    const value = input[i];\n    try {\n      output.push(String(value));\n    } catch (e) {\n      output.push('[value cannot be serialized]');\n    }\n  }\n\n  return output.join(delimiter);\n}\n\n/**\n * Checks if the value matches a regex or includes the string\n * @param value The string value to be checked against\n * @param pattern Either a regex or a string that must be contained in value\n */\nexport function isMatchingPattern(value: string, pattern: RegExp | string): boolean {\n  if (!isString(value)) {\n    return false;\n  }\n\n  if (isRegExp(pattern)) {\n    return (pattern as RegExp).test(value);\n  }\n  if (typeof pattern === 'string') {\n    return value.indexOf(pattern) !== -1;\n  }\n  return false;\n}\n"]}