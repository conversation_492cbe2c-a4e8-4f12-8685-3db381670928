{"version": 3, "file": "misc.js", "sourceRoot": "", "sources": ["../src/misc.ts"], "names": [], "mappings": ";AAGA,+BAAmC;AACnC,mCAAoC;AAmBpC,IAAM,oBAAoB,GAAG,EAAE,CAAC;AAEhC;;;;GAIG;AACH,SAAgB,eAAe;IAC7B,OAAO,CAAC,gBAAS,EAAE;QACjB,CAAC,CAAC,MAAM;QACR,CAAC,CAAC,OAAO,MAAM,KAAK,WAAW;YAC/B,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,OAAO,IAAI,KAAK,WAAW;gBAC7B,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,oBAAoB,CAAqB,CAAC;AAChD,CAAC;AARD,0CAQC;AASD;;;;GAIG;AACH,SAAgB,KAAK;IACnB,IAAM,MAAM,GAAG,eAAe,EAAoB,CAAC;IACnD,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;IAEhD,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,eAAe,EAAE;QAClD,qCAAqC;QACrC,IAAM,GAAG,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAE5B,kBAAkB;QAClB,sCAAsC;QACtC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;QACnC,gDAAgD;QAChD,sCAAsC;QACtC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;QAEpC,IAAM,GAAG,GAAG,UAAC,GAAW;YACtB,IAAI,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACzB,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnB,CAAC,GAAG,MAAI,CAAG,CAAC;aACb;YACD,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QAEF,OAAO,CACL,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC9G,CAAC;KACH;IACD,oGAAoG;IACpG,OAAO,kCAAkC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAA,CAAC;QAC1D,sCAAsC;QACtC,IAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QACnC,sCAAsC;QACtC,IAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC1C,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;AACL,CAAC;AApCD,sBAoCC;AAED;;;;;;GAMG;AACH,SAAgB,QAAQ,CACtB,GAAW;IAOX,IAAI,CAAC,GAAG,EAAE;QACR,OAAO,EAAE,CAAC;KACX;IAED,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;IAExF,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,EAAE,CAAC;KACX;IAED,yEAAyE;IACzE,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC7B,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAChC,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACd,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACd,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;QAClB,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,QAAQ;KACtC,CAAC;AACJ,CAAC;AA3BD,4BA2BC;AAED;;;GAGG;AACH,SAAgB,mBAAmB,CAAC,KAAY;IAC9C,IAAI,KAAK,CAAC,OAAO,EAAE;QACjB,OAAO,KAAK,CAAC,OAAO,CAAC;KACtB;IACD,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;QAC1E,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE5C,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;YACrC,OAAU,SAAS,CAAC,IAAI,UAAK,SAAS,CAAC,KAAO,CAAC;SAChD;QACD,OAAO,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,IAAI,WAAW,CAAC;KAC3E;IACD,OAAO,KAAK,CAAC,QAAQ,IAAI,WAAW,CAAC;AACvC,CAAC;AAbD,kDAaC;AAOD,YAAY;AACZ,SAAgB,cAAc,CAAC,QAAmB;IAChD,IAAM,MAAM,GAAG,eAAe,EAAU,CAAC;IACzC,IAAM,MAAM,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAEnE,IAAI,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE;QAC1B,OAAO,QAAQ,EAAE,CAAC;KACnB;IAED,sEAAsE;IACtE,IAAM,eAAe,GAAI,MAAc,CAAC,OAA4B,CAAC;IACrE,IAAM,aAAa,GAA2B,EAAE,CAAC;IAEjD,sCAAsC;IACtC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;QAClB,sEAAsE;QACtE,IAAI,KAAK,IAAK,MAAc,CAAC,OAAO,IAAK,eAAe,CAAC,KAAK,CAAqB,CAAC,mBAAmB,EAAE;YACvG,aAAa,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,KAAK,CAAoB,CAAC;YACjE,eAAe,CAAC,KAAK,CAAC,GAAI,eAAe,CAAC,KAAK,CAAqB,CAAC,mBAAmB,CAAC;SAC1F;IACH,CAAC,CAAC,CAAC;IAEH,iCAAiC;IACjC,IAAM,MAAM,GAAG,QAAQ,EAAE,CAAC;IAE1B,sCAAsC;IACtC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK;QACtC,eAAe,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AA9BD,wCA8BC;AAED;;;;;;GAMG;AACH,SAAgB,qBAAqB,CAAC,KAAY,EAAE,KAAc,EAAE,IAAa;IAC/E,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC;IACxC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,EAAE,CAAC;IACtD,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC5D,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC;IACjF,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC;AACrF,CAAC;AAND,sDAMC;AAED;;;;;GAKG;AACH,SAAgB,qBAAqB,CACnC,KAAY,EACZ,SAEM;IAFN,0BAAA,EAAA,cAEM;IAEN,8EAA8E;IAC9E,IAAI;QACF,qFAAqF;QACrF,oEAAoE;QACpE,KAAK,CAAC,SAAU,CAAC,MAAO,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,SAAU,CAAC,MAAO,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC;QACpF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;YAChC,8CAA8C;YAC9C,oEAAoE;YACpE,KAAK,CAAC,SAAU,CAAC,MAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;KACJ;IAAC,OAAO,GAAG,EAAE;QACZ,WAAW;KACZ;AACH,CAAC;AAnBD,sDAmBC;AAED;;GAEG;AACH,SAAgB,eAAe;IAC7B,IAAI;QACF,OAAO,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;KAC/B;IAAC,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,CAAC;KACX;AACH,CAAC;AAND,0CAMC;AAED,6FAA6F;AAC7F,IAAM,aAAa,GAAG,qLAAqL,CAAC;AAa5M;;;GAGG;AACH,SAAgB,WAAW,CAAC,KAAa;IACvC,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC/C,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrC,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrC,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrC,OAAO;QACL,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;QACvB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK;QACvC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK;QACvC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK;QACvC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;KACrB,CAAC;AACJ,CAAC;AAZD,kCAYC;AAED,IAAM,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,aAAa;AAElD;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,GAAW,EAAE,MAA+B;IAChF,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,iBAAiB,CAAC;KAC1B;IAED,IAAM,WAAW,GAAG,QAAQ,CAAC,KAAG,MAAQ,EAAE,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;QACvB,OAAO,WAAW,GAAG,IAAI,CAAC;KAC3B;IAED,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAG,MAAQ,CAAC,CAAC;IAC3C,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;QACtB,OAAO,UAAU,GAAG,GAAG,CAAC;KACzB;IAED,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAhBD,sDAgBC;AAED;;;;;;GAMG;AACH,SAAgB,iBAAiB,CAAC,KAAe,EAAE,KAAiB,EAAE,cAA0B;IAA1B,+BAAA,EAAA,kBAA0B;IAC9F,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC;IACjC,IAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC;IAC9B,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE/D,KAAK,CAAC,WAAW,GAAG,KAAK;SACtB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,cAAc,CAAC,EAAE,UAAU,CAAC;SAC3D,GAAG,CAAC,UAAC,IAAY,IAAK,OAAA,iBAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,EAAjB,CAAiB,CAAC,CAAC;IAE5C,KAAK,CAAC,YAAY,GAAG,iBAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;IAE3F,KAAK,CAAC,YAAY,GAAG,KAAK;SACvB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,GAAG,CAAC,GAAG,cAAc,CAAC;SAC1E,GAAG,CAAC,UAAC,IAAY,IAAK,OAAA,iBAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,EAAjB,CAAiB,CAAC,CAAC;AAC9C,CAAC;AAdD,8CAcC;AAED;;;;;GAKG;AACH,SAAgB,wBAAwB,CAAC,OAAe;IACtD,6CAA6C;IAC7C,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC;AAHD,4DAGC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Event, Integration, StackFrame, WrappedFunction } from '@sentry/types';\n\nimport { isNodeEnv } from './node';\nimport { snipLine } from './string';\n\n/** Internal */\ninterface SentryGlobal {\n  Sentry?: {\n    Integrations?: Integration[];\n  };\n  SENTRY_ENVIRONMENT?: string;\n  SENTRY_DSN?: string;\n  SENTRY_RELEASE?: {\n    id?: string;\n  };\n  __SENTRY__: {\n    globalEventProcessors: any;\n    hub: any;\n    logger: any;\n  };\n}\n\nconst fallbackGlobalObject = {};\n\n/**\n * Safely get global scope object\n *\n * @returns Global scope object\n */\nexport function getGlobalObject<T>(): T & SentryGlobal {\n  return (isNodeEnv()\n    ? global\n    : typeof window !== 'undefined'\n    ? window\n    : typeof self !== 'undefined'\n    ? self\n    : fallbackGlobalObject) as T & SentryGlobal;\n}\n\n/**\n * Extended Window interface that allows for Crypto API usage in IE browsers\n */\ninterface MsCryptoWindow extends Window {\n  msCrypto?: Crypto;\n}\n\n/**\n * UUID4 generator\n *\n * @returns string Generated UUID4.\n */\nexport function uuid4(): string {\n  const global = getGlobalObject() as MsCryptoWindow;\n  const crypto = global.crypto || global.msCrypto;\n\n  if (!(crypto === void 0) && crypto.getRandomValues) {\n    // Use window.crypto API if available\n    const arr = new Uint16Array(8);\n    crypto.getRandomValues(arr);\n\n    // set 4 in byte 7\n    // eslint-disable-next-line no-bitwise\n    arr[3] = (arr[3] & 0xfff) | 0x4000;\n    // set 2 most significant bits of byte 9 to '10'\n    // eslint-disable-next-line no-bitwise\n    arr[4] = (arr[4] & 0x3fff) | 0x8000;\n\n    const pad = (num: number): string => {\n      let v = num.toString(16);\n      while (v.length < 4) {\n        v = `0${v}`;\n      }\n      return v;\n    };\n\n    return (\n      pad(arr[0]) + pad(arr[1]) + pad(arr[2]) + pad(arr[3]) + pad(arr[4]) + pad(arr[5]) + pad(arr[6]) + pad(arr[7])\n    );\n  }\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n  return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    // eslint-disable-next-line no-bitwise\n    const r = (Math.random() * 16) | 0;\n    // eslint-disable-next-line no-bitwise\n    const v = c === 'x' ? r : (r & 0x3) | 0x8;\n    return v.toString(16);\n  });\n}\n\n/**\n * Parses string form of URL into an object\n * // borrowed from https://tools.ietf.org/html/rfc3986#appendix-B\n * // intentionally using regex and not <a/> href parsing trick because React Native and other\n * // environments where DOM might not be available\n * @returns parsed URL object\n */\nexport function parseUrl(\n  url: string,\n): {\n  host?: string;\n  path?: string;\n  protocol?: string;\n  relative?: string;\n} {\n  if (!url) {\n    return {};\n  }\n\n  const match = url.match(/^(([^:/?#]+):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?$/);\n\n  if (!match) {\n    return {};\n  }\n\n  // coerce to undefined values to empty string so we don't get 'undefined'\n  const query = match[6] || '';\n  const fragment = match[8] || '';\n  return {\n    host: match[4],\n    path: match[5],\n    protocol: match[2],\n    relative: match[5] + query + fragment, // everything minus origin\n  };\n}\n\n/**\n * Extracts either message or type+value from an event that can be used for user-facing logs\n * @returns event's description\n */\nexport function getEventDescription(event: Event): string {\n  if (event.message) {\n    return event.message;\n  }\n  if (event.exception && event.exception.values && event.exception.values[0]) {\n    const exception = event.exception.values[0];\n\n    if (exception.type && exception.value) {\n      return `${exception.type}: ${exception.value}`;\n    }\n    return exception.type || exception.value || event.event_id || '<unknown>';\n  }\n  return event.event_id || '<unknown>';\n}\n\n/** JSDoc */\ninterface ExtensibleConsole extends Console {\n  [key: string]: any;\n}\n\n/** JSDoc */\nexport function consoleSandbox(callback: () => any): any {\n  const global = getGlobalObject<Window>();\n  const levels = ['debug', 'info', 'warn', 'error', 'log', 'assert'];\n\n  if (!('console' in global)) {\n    return callback();\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  const originalConsole = (global as any).console as ExtensibleConsole;\n  const wrappedLevels: { [key: string]: any } = {};\n\n  // Restore all wrapped console methods\n  levels.forEach(level => {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    if (level in (global as any).console && (originalConsole[level] as WrappedFunction).__sentry_original__) {\n      wrappedLevels[level] = originalConsole[level] as WrappedFunction;\n      originalConsole[level] = (originalConsole[level] as WrappedFunction).__sentry_original__;\n    }\n  });\n\n  // Perform callback manipulations\n  const result = callback();\n\n  // Revert restoration to wrapped state\n  Object.keys(wrappedLevels).forEach(level => {\n    originalConsole[level] = wrappedLevels[level];\n  });\n\n  return result;\n}\n\n/**\n * Adds exception values, type and value to an synthetic Exception.\n * @param event The event to modify.\n * @param value Value of the exception.\n * @param type Type of the exception.\n * @hidden\n */\nexport function addExceptionTypeValue(event: Event, value?: string, type?: string): void {\n  event.exception = event.exception || {};\n  event.exception.values = event.exception.values || [];\n  event.exception.values[0] = event.exception.values[0] || {};\n  event.exception.values[0].value = event.exception.values[0].value || value || '';\n  event.exception.values[0].type = event.exception.values[0].type || type || 'Error';\n}\n\n/**\n * Adds exception mechanism to a given event.\n * @param event The event to modify.\n * @param mechanism Mechanism of the mechanism.\n * @hidden\n */\nexport function addExceptionMechanism(\n  event: Event,\n  mechanism: {\n    [key: string]: any;\n  } = {},\n): void {\n  // TODO: Use real type with `keyof Mechanism` thingy and maybe make it better?\n  try {\n    // @ts-ignore Type 'Mechanism | {}' is not assignable to type 'Mechanism | undefined'\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    event.exception!.values![0].mechanism = event.exception!.values![0].mechanism || {};\n    Object.keys(mechanism).forEach(key => {\n      // @ts-ignore Mechanism has no index signature\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      event.exception!.values![0].mechanism[key] = mechanism[key];\n    });\n  } catch (_oO) {\n    // no-empty\n  }\n}\n\n/**\n * A safe form of location.href\n */\nexport function getLocationHref(): string {\n  try {\n    return document.location.href;\n  } catch (oO) {\n    return '';\n  }\n}\n\n// https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string\nconst SEMVER_REGEXP = /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n\n/**\n * Represents Semantic Versioning object\n */\ninterface SemVer {\n  major?: number;\n  minor?: number;\n  patch?: number;\n  prerelease?: string;\n  buildmetadata?: string;\n}\n\n/**\n * Parses input into a SemVer interface\n * @param input string representation of a semver version\n */\nexport function parseSemver(input: string): SemVer {\n  const match = input.match(SEMVER_REGEXP) || [];\n  const major = parseInt(match[1], 10);\n  const minor = parseInt(match[2], 10);\n  const patch = parseInt(match[3], 10);\n  return {\n    buildmetadata: match[5],\n    major: isNaN(major) ? undefined : major,\n    minor: isNaN(minor) ? undefined : minor,\n    patch: isNaN(patch) ? undefined : patch,\n    prerelease: match[4],\n  };\n}\n\nconst defaultRetryAfter = 60 * 1000; // 60 seconds\n\n/**\n * Extracts Retry-After value from the request header or returns default value\n * @param now current unix timestamp\n * @param header string representation of 'Retry-After' header\n */\nexport function parseRetryAfterHeader(now: number, header?: string | number | null): number {\n  if (!header) {\n    return defaultRetryAfter;\n  }\n\n  const headerDelay = parseInt(`${header}`, 10);\n  if (!isNaN(headerDelay)) {\n    return headerDelay * 1000;\n  }\n\n  const headerDate = Date.parse(`${header}`);\n  if (!isNaN(headerDate)) {\n    return headerDate - now;\n  }\n\n  return defaultRetryAfter;\n}\n\n/**\n * This function adds context (pre/post/line) lines to the provided frame\n *\n * @param lines string[] containing all lines\n * @param frame StackFrame that will be mutated\n * @param linesOfContext number of context lines we want to add pre/post\n */\nexport function addContextToFrame(lines: string[], frame: StackFrame, linesOfContext: number = 5): void {\n  const lineno = frame.lineno || 0;\n  const maxLines = lines.length;\n  const sourceLine = Math.max(Math.min(maxLines, lineno - 1), 0);\n\n  frame.pre_context = lines\n    .slice(Math.max(0, sourceLine - linesOfContext), sourceLine)\n    .map((line: string) => snipLine(line, 0));\n\n  frame.context_line = snipLine(lines[Math.min(maxLines - 1, sourceLine)], frame.colno || 0);\n\n  frame.post_context = lines\n    .slice(Math.min(sourceLine + 1, maxLines), sourceLine + 1 + linesOfContext)\n    .map((line: string) => snipLine(line, 0));\n}\n\n/**\n * Strip the query string and fragment off of a given URL or path (if present)\n *\n * @param urlPath Full URL or path, including possible query string and/or fragment\n * @returns URL or path without query string or fragment\n */\nexport function stripUrlQueryAndFragment(urlPath: string): string {\n  // eslint-disable-next-line no-useless-escape\n  return urlPath.split(/[\\?#]/, 1)[0];\n}\n"]}