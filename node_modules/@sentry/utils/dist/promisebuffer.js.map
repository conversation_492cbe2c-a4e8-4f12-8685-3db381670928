{"version": 3, "file": "promisebuffer.js", "sourceRoot": "", "sources": ["../src/promisebuffer.ts"], "names": [], "mappings": ";AAAA,iCAAsC;AACtC,6CAA4C;AAE5C,0CAA0C;AAC1C;IAIE,uBAA6B,MAAe;QAAf,WAAM,GAAN,MAAM,CAAS;QAH5C,sCAAsC;QACrB,YAAO,GAA0B,EAAE,CAAC;IAEN,CAAC;IAEhD;;OAEG;IACI,+BAAO,GAAd;QACE,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;IAClE,CAAC;IAED;;;;;OAKG;IACI,2BAAG,GAAV,UAAW,IAAoB;QAA/B,iBAgBC;QAfC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YACnB,OAAO,yBAAW,CAAC,MAAM,CAAC,IAAI,mBAAW,CAAC,iDAAiD,CAAC,CAAC,CAAC;SAC/F;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzB;QACD,IAAI;aACD,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAjB,CAAiB,CAAC;aAC7B,IAAI,CAAC,IAAI,EAAE;YACV,OAAA,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC3B,gFAAgF;gBAChF,oCAAoC;YACtC,CAAC,CAAC;QAHF,CAGE,CACH,CAAC;QACJ,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,8BAAM,GAAb,UAAc,IAAoB;QAChC,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,8BAAM,GAAb;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACI,6BAAK,GAAZ,UAAa,OAAgB;QAA7B,iBAgBC;QAfC,OAAO,IAAI,yBAAW,CAAU,UAAA,OAAO;YACrC,IAAM,kBAAkB,GAAG,UAAU,CAAC;gBACpC,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,EAAE;oBAC1B,OAAO,CAAC,KAAK,CAAC,CAAC;iBAChB;YACH,CAAC,EAAE,OAAO,CAAC,CAAC;YACZ,yBAAW,CAAC,GAAG,CAAC,KAAI,CAAC,OAAO,CAAC;iBAC1B,IAAI,CAAC;gBACJ,YAAY,CAAC,kBAAkB,CAAC,CAAC;gBACjC,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,EAAE;gBACV,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IACH,oBAAC;AAAD,CAAC,AA9ED,IA8EC;AA9EY,sCAAa", "sourcesContent": ["import { SentryError } from './error';\nimport { SyncPromise } from './syncpromise';\n\n/** A simple queue that holds promises. */\nexport class PromiseBuffer<T> {\n  /** Internal set of queued Promises */\n  private readonly _buffer: Array<PromiseLike<T>> = [];\n\n  public constructor(protected _limit?: number) {}\n\n  /**\n   * Says if the buffer is ready to take more requests\n   */\n  public isReady(): boolean {\n    return this._limit === undefined || this.length() < this._limit;\n  }\n\n  /**\n   * Add a promise to the queue.\n   *\n   * @param task Can be any PromiseLike<T>\n   * @returns The original promise.\n   */\n  public add(task: PromiseLike<T>): PromiseLike<T> {\n    if (!this.isReady()) {\n      return SyncPromise.reject(new SentryError('Not adding Promise due to buffer limit reached.'));\n    }\n    if (this._buffer.indexOf(task) === -1) {\n      this._buffer.push(task);\n    }\n    task\n      .then(() => this.remove(task))\n      .then(null, () =>\n        this.remove(task).then(null, () => {\n          // We have to add this catch here otherwise we have an unhandledPromiseRejection\n          // because it's a new Promise chain.\n        }),\n      );\n    return task;\n  }\n\n  /**\n   * Remove a promise to the queue.\n   *\n   * @param task Can be any PromiseLike<T>\n   * @returns Removed promise.\n   */\n  public remove(task: PromiseLike<T>): PromiseLike<T> {\n    const removedTask = this._buffer.splice(this._buffer.indexOf(task), 1)[0];\n    return removedTask;\n  }\n\n  /**\n   * This function returns the number of unresolved promises in the queue.\n   */\n  public length(): number {\n    return this._buffer.length;\n  }\n\n  /**\n   * This will drain the whole queue, returns true if queue is empty or drained.\n   * If timeout is provided and the queue takes longer to drain, the promise still resolves but with false.\n   *\n   * @param timeout Number in ms to wait until it resolves with false.\n   */\n  public drain(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise<boolean>(resolve => {\n      const capturedSetTimeout = setTimeout(() => {\n        if (timeout && timeout > 0) {\n          resolve(false);\n        }\n      }, timeout);\n      SyncPromise.all(this._buffer)\n        .then(() => {\n          clearTimeout(capturedSetTimeout);\n          resolve(true);\n        })\n        .then(null, () => {\n          resolve(true);\n        });\n    });\n  }\n}\n"]}