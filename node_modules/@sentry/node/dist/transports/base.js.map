{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/transports/base.ts"], "names": [], "mappings": ";;AAAA,qCAAyD;AACzD,uCAAqF;AACrF,uCAA0F;AAC1F,uBAAyB;AAGzB,yBAA2B;AAE3B,sCAAmD;AA4BnD,0CAA0C;AAC1C;IAgBE,uCAAuC;IACvC,uBAA0B,OAAyB;QAAzB,YAAO,GAAP,OAAO,CAAkB;QAPnD,4CAA4C;QACzB,YAAO,GAA4B,IAAI,qBAAa,CAAC,EAAE,CAAC,CAAC;QAE5E,mDAAmD;QAC3C,mBAAc,GAAS,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAIlD,IAAI,CAAC,IAAI,GAAG,IAAI,UAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,iCAAS,GAAhB,UAAiB,CAAQ;QACvB,MAAM,IAAI,mBAAW,CAAC,sDAAsD,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACI,6BAAK,GAAZ,UAAa,OAAgB;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,4DAA4D;IAClD,0CAAkB,GAA5B,UAA6B,GAAY;QACvC,IAAM,OAAO,yCACR,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAQ,EAAE,qBAAW,CAAC,GAClD,IAAI,CAAC,OAAO,CAAC,OAAO,CACxB,CAAC;QACM,IAAA,uBAAQ,EAAE,uBAAQ,EAAE,eAAI,EAAE,uBAAQ,CAAS;QACnD,mHAAmH;QACnH,wCAAwC;QACxC,IAAM,IAAI,GAAG,KAAG,QAAU,CAAC;QAE3B,0BACE,KAAK,EAAE,IAAI,CAAC,MAAM,EAClB,OAAO,SAAA;YACP,QAAQ,UAAA,EACR,MAAM,EAAE,MAAM,EACd,IAAI,MAAA;YACJ,IAAI,MAAA;YACJ,QAAQ,UAAA,IACL,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI;YAC1B,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;SAC1C,CAAC,EACF;IACJ,CAAC;IAED,YAAY;IACI,uCAAe,GAA/B,UAAgC,UAAsB,EAAE,KAAY;;;;gBAClE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE;oBAC9C,sBAAO,OAAO,CAAC,MAAM,CAAC,IAAI,mBAAW,CAAC,2BAAyB,IAAI,CAAC,cAAc,+BAA4B,CAAC,CAAC,EAAC;iBAClH;gBAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE;oBAC3B,sBAAO,OAAO,CAAC,MAAM,CAAC,IAAI,mBAAW,CAAC,iDAAiD,CAAC,CAAC,EAAC;iBAC3F;gBACD,sBAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CACrB,IAAI,OAAO,CAAW,UAAC,OAAO,EAAE,MAAM;wBACpC,IAAM,SAAS,GAAG,2BAAoB,CAAC,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,CAAC;wBACzD,IAAM,OAAO,GAAG,KAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;wBAEpE,IAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,UAAC,GAAyB;4BAChE,IAAM,UAAU,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC;4BACzC,IAAM,MAAM,GAAG,cAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;4BAE/C,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;4BAExB,IAAI,MAAM,KAAK,cAAM,CAAC,OAAO,EAAE;gCAC7B,OAAO,CAAC,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;6BACrB;iCAAM;gCACL,IAAI,MAAM,KAAK,cAAM,CAAC,SAAS,EAAE;oCAC/B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oCACvB;;;uCAGG;oCACH,IAAI,gBAAgB,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oCACrE,gBAAgB,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAW,CAAC;oCACxG,KAAI,CAAC,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,6BAAqB,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAC;oCACnF,cAAM,CAAC,IAAI,CAAC,0CAAwC,KAAI,CAAC,cAAgB,CAAC,CAAC;iCAC5E;gCAED,IAAI,gBAAgB,GAAG,iBAAe,UAAU,MAAG,CAAC;gCACpD,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;oCAChD,gBAAgB,IAAI,OAAK,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAG,CAAC;iCAC1D;gCAED,MAAM,CAAC,IAAI,mBAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC;6BAC3C;4BAED,4BAA4B;4BAC5B,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE;gCACb,QAAQ;4BACV,CAAC,CAAC,CAAC;4BACH,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE;gCACZ,QAAQ;4BACV,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;wBACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;wBACxB,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC1B,CAAC,CAAC,CACH,EAAC;;;KACH;IACH,oBAAC;AAAD,CAAC,AApHD,IAoHC;AApHqB,sCAAa", "sourcesContent": ["import { API, eventToSentryRequest } from '@sentry/core';\nimport { Event, Response, Status, Transport, TransportOptions } from '@sentry/types';\nimport { logger, parseRetry<PERSON><PERSON><PERSON>eader, Promise<PERSON><PERSON>er, SentryError } from '@sentry/utils';\nimport * as fs from 'fs';\nimport * as http from 'http';\nimport * as https from 'https';\nimport * as url from 'url';\n\nimport { SDK_NAME, SDK_VERSION } from '../version';\n\n/**\n * Internal used interface for typescript.\n * @hidden\n */\nexport interface HTTPModule {\n  /**\n   * Request wrapper\n   * @param options These are {@see TransportOptions}\n   * @param callback Callback when request is finished\n   */\n  request(\n    options: http.RequestOptions | https.RequestOptions | string | url.URL,\n    callback?: (res: http.IncomingMessage) => void,\n  ): http.ClientRequest;\n\n  // This is the type for nodejs versions that handle the URL argument\n  // (v10.9.0+), but we do not use it just yet because we support older node\n  // versions:\n\n  // request(\n  //   url: string | url.URL,\n  //   options: http.RequestOptions | https.RequestOptions,\n  //   callback?: (res: http.IncomingMessage) => void,\n  // ): http.ClientRequest;\n}\n\n/** Base Transport class implementation */\nexport abstract class BaseTransport implements Transport {\n  /** The Agent used for corresponding transport */\n  public module?: HTTPModule;\n\n  /** The Agent used for corresponding transport */\n  public client?: http.Agent | https.Agent;\n\n  /** API object */\n  protected _api: API;\n\n  /** A simple buffer holding all requests. */\n  protected readonly _buffer: PromiseBuffer<Response> = new PromiseBuffer(30);\n\n  /** Locks transport after receiving 429 response */\n  private _disabledUntil: Date = new Date(Date.now());\n\n  /** Create instance and set this.dsn */\n  public constructor(public options: TransportOptions) {\n    this._api = new API(options.dsn);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(_: Event): PromiseLike<Response> {\n    throw new SentryError('Transport Class has to implement `sendEvent` method.');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    return this._buffer.drain(timeout);\n  }\n\n  /** Returns a build request option object used by request */\n  protected _getRequestOptions(uri: url.URL): http.RequestOptions | https.RequestOptions {\n    const headers = {\n      ...this._api.getRequestHeaders(SDK_NAME, SDK_VERSION),\n      ...this.options.headers,\n    };\n    const { hostname, pathname, port, protocol } = uri;\n    // See https://github.com/nodejs/node/blob/38146e717fed2fabe3aacb6540d839475e0ce1c6/lib/internal/url.js#L1268-L1290\n    // We ignore the query string on purpose\n    const path = `${pathname}`;\n\n    return {\n      agent: this.client,\n      headers,\n      hostname,\n      method: 'POST',\n      path,\n      port,\n      protocol,\n      ...(this.options.caCerts && {\n        ca: fs.readFileSync(this.options.caCerts),\n      }),\n    };\n  }\n\n  /** JSDoc */\n  protected async _sendWithModule(httpModule: HTTPModule, event: Event): Promise<Response> {\n    if (new Date(Date.now()) < this._disabledUntil) {\n      return Promise.reject(new SentryError(`Transport locked till ${this._disabledUntil} due to too many requests.`));\n    }\n\n    if (!this._buffer.isReady()) {\n      return Promise.reject(new SentryError('Not adding Promise due to buffer limit reached.'));\n    }\n    return this._buffer.add(\n      new Promise<Response>((resolve, reject) => {\n        const sentryReq = eventToSentryRequest(event, this._api);\n        const options = this._getRequestOptions(new url.URL(sentryReq.url));\n\n        const req = httpModule.request(options, (res: http.IncomingMessage) => {\n          const statusCode = res.statusCode || 500;\n          const status = Status.fromHttpCode(statusCode);\n\n          res.setEncoding('utf8');\n\n          if (status === Status.Success) {\n            resolve({ status });\n          } else {\n            if (status === Status.RateLimit) {\n              const now = Date.now();\n              /**\n               * \"Key-value pairs of header names and values. Header names are lower-cased.\"\n               * https://nodejs.org/api/http.html#http_message_headers\n               */\n              let retryAfterHeader = res.headers ? res.headers['retry-after'] : '';\n              retryAfterHeader = (Array.isArray(retryAfterHeader) ? retryAfterHeader[0] : retryAfterHeader) as string;\n              this._disabledUntil = new Date(now + parseRetryAfterHeader(now, retryAfterHeader));\n              logger.warn(`Too many requests, backing off till: ${this._disabledUntil}`);\n            }\n\n            let rejectionMessage = `HTTP Error (${statusCode})`;\n            if (res.headers && res.headers['x-sentry-error']) {\n              rejectionMessage += `: ${res.headers['x-sentry-error']}`;\n            }\n\n            reject(new SentryError(rejectionMessage));\n          }\n\n          // Force the socket to drain\n          res.on('data', () => {\n            // Drain\n          });\n          res.on('end', () => {\n            // Drain\n          });\n        });\n        req.on('error', reject);\n        req.end(sentryReq.body);\n      }),\n    );\n  }\n}\n"]}