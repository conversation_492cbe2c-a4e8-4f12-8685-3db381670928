{"version": 3, "file": "stacktrace.js", "sourceRoot": "", "sources": ["../src/stacktrace.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;GAUG;;AAaH,0CAA0C;AAC1C,SAAgB,KAAK,CAAC,GAAU;IAC9B,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;QACd,OAAO,EAAE,CAAC;KACX;IAED,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE7C,OAAO,KAAK;SACT,GAAG,CAAC,UAAA,IAAI;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;YAC9B,OAAO;gBACL,YAAY,EAAE,IAAI;gBAClB,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,IAAI;aACf,CAAC;SACH;QAED,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;QACxF,IAAI,CAAC,SAAS,EAAE;YACd,OAAO,SAAS,CAAC;SAClB;QAED,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,IAAI,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;QAE3C,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;YAChB,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;gBACzC,uCAAuC;gBACvC,WAAW,EAAE,CAAC;aACf;YACD,IAAI,WAAW,GAAG,CAAC,EAAE;gBACnB,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;gBAC7C,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;gBAC9C,IAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,SAAS,GAAG,CAAC,EAAE;oBACjB,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;oBAClD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;iBACtC;aACF;YACD,QAAQ,GAAG,IAAI,CAAC;SACjB;QAED,IAAI,MAAM,EAAE;YACV,QAAQ,GAAG,MAAM,CAAC;YAClB,UAAU,GAAG,MAAM,CAAC;SACrB;QAED,IAAI,MAAM,KAAK,aAAa,EAAE;YAC5B,UAAU,GAAG,IAAI,CAAC;YAClB,YAAY,GAAG,IAAI,CAAC;SACrB;QAED,IAAM,UAAU,GAAG;YACjB,YAAY,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI;YAChD,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI;YAC9B,YAAY,cAAA;YACZ,UAAU,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI;YAC9C,UAAU,YAAA;YACV,MAAM,EAAE,QAAQ;YAChB,QAAQ,UAAA;SACT,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;SACD,MAAM,CAAC,UAAA,QAAQ,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAV,CAAU,CAAiB,CAAC;AACpD,CAAC;AA3ED,sBA2EC", "sourcesContent": ["/**\n * stack-trace - Parses node.js stack traces\n *\n * This was originally forked to fix this issue:\n * https://github.com/felixge/node-stack-trace/issues/31\n *\n * Mar 19,2019 - #4fd379e\n *\n * https://github.com/felixge/node-stack-trace/\n * @license MIT\n */\n\n/** Decoded StackFrame */\nexport interface StackFrame {\n  fileName: string;\n  lineNumber: number;\n  functionName: string;\n  typeName: string;\n  methodName: string;\n  native: boolean;\n  columnNumber: number;\n}\n\n/** Extracts StackFrames from the Error */\nexport function parse(err: Error): StackFrame[] {\n  if (!err.stack) {\n    return [];\n  }\n\n  const lines = err.stack.split('\\n').slice(1);\n\n  return lines\n    .map(line => {\n      if (line.match(/^\\s*[-]{4,}$/)) {\n        return {\n          columnNumber: null,\n          fileName: line,\n          functionName: null,\n          lineNumber: null,\n          methodName: null,\n          native: null,\n          typeName: null,\n        };\n      }\n\n      const lineMatch = line.match(/at (?:(.+?)\\s+\\()?(?:(.+?):(\\d+)(?::(\\d+))?|([^)]+))\\)?/);\n      if (!lineMatch) {\n        return undefined;\n      }\n\n      let object = null;\n      let method = null;\n      let functionName = null;\n      let typeName = null;\n      let methodName = null;\n      const isNative = lineMatch[5] === 'native';\n\n      if (lineMatch[1]) {\n        functionName = lineMatch[1];\n        let methodStart = functionName.lastIndexOf('.');\n        if (functionName[methodStart - 1] === '.') {\n          // eslint-disable-next-line no-plusplus\n          methodStart--;\n        }\n        if (methodStart > 0) {\n          object = functionName.substr(0, methodStart);\n          method = functionName.substr(methodStart + 1);\n          const objectEnd = object.indexOf('.Module');\n          if (objectEnd > 0) {\n            functionName = functionName.substr(objectEnd + 1);\n            object = object.substr(0, objectEnd);\n          }\n        }\n        typeName = null;\n      }\n\n      if (method) {\n        typeName = object;\n        methodName = method;\n      }\n\n      if (method === '<anonymous>') {\n        methodName = null;\n        functionName = null;\n      }\n\n      const properties = {\n        columnNumber: parseInt(lineMatch[4], 10) || null,\n        fileName: lineMatch[2] || null,\n        functionName,\n        lineNumber: parseInt(lineMatch[3], 10) || null,\n        methodName,\n        native: isNative,\n        typeName,\n      };\n\n      return properties;\n    })\n    .filter(callSite => !!callSite) as StackFrame[];\n}\n"]}