{"version": 3, "file": "sdk.js", "sourceRoot": "", "sources": ["../src/sdk.ts"], "names": [], "mappings": ";;AAAA,qCAA4F;AAC5F,mCAA8D;AAC9D,uCAAgD;AAChD,+BAAiC;AAGjC,mCAAsC;AACtC,+CAAwG;AAE3F,QAAA,mBAAmB,GAAG;IACjC,SAAS;IACT,IAAI,mBAAgB,CAAC,cAAc,EAAE;IACrC,IAAI,mBAAgB,CAAC,gBAAgB,EAAE;IACvC,kBAAkB;IAClB,IAAI,sBAAO,EAAE;IACb,IAAI,mBAAI,EAAE;IACV,kBAAkB;IAClB,IAAI,kCAAmB,EAAE;IACzB,IAAI,mCAAoB,EAAE;IAC1B,OAAO;IACP,IAAI,2BAAY,EAAE;CACnB,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsDG;AACH,SAAgB,IAAI,CAAC,OAAyB;IAAzB,wBAAA,EAAA,YAAyB;IAC5C,IAAI,OAAO,CAAC,mBAAmB,KAAK,SAAS,EAAE;QAC7C,OAAO,CAAC,mBAAmB,GAAG,2BAAmB,CAAC;KACnD;IAED,IAAI,OAAO,CAAC,GAAG,KAAK,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;QACvD,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;KACtC;IAED,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE;QACnF,IAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QAC3E,IAAI,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YAC9B,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;SAC7C;KACF;IAED,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;QACjC,IAAM,QAAM,GAAG,uBAAe,EAAU,CAAC;QACzC,6BAA6B;QAC7B,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;YAC9B,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;SAC9C;QACD,gEAAgE;aAC3D,IAAI,QAAM,CAAC,cAAc,IAAI,QAAM,CAAC,cAAc,CAAC,EAAE,EAAE;YAC1D,OAAO,CAAC,OAAO,GAAG,QAAM,CAAC,cAAc,CAAC,EAAE,CAAC;SAC5C;KACF;IAED,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE;QACvE,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;KACtD;IAED,0GAA0G;IAC1G,IAAK,MAAc,CAAC,MAAM,EAAE;QAC1B,qBAAe,CAAC,oBAAc,EAAE,EAAE,oBAAa,EAAE,CAAC,CAAC;KACpD;IAED,kBAAW,CAAC,mBAAU,EAAE,OAAO,CAAC,CAAC;AACnC,CAAC;AAtCD,oBAsCC;AAED;;;;GAIG;AACH,SAAgB,WAAW;IACzB,OAAO,oBAAa,EAAE,CAAC,WAAW,EAAE,CAAC;AACvC,CAAC;AAFD,kCAEC;AAED;;;;;GAKG;AACH,SAAsB,KAAK,CAAC,OAAgB;;;;YACpC,MAAM,GAAG,oBAAa,EAAE,CAAC,SAAS,EAAc,CAAC;YACvD,IAAI,MAAM,EAAE;gBACV,sBAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAC;aAC9B;YACD,sBAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAC;;;CAC9B;AAND,sBAMC;AAED;;;;;GAKG;AACH,SAAsB,KAAK,CAAC,OAAgB;;;;YACpC,MAAM,GAAG,oBAAa,EAAE,CAAC,SAAS,EAAc,CAAC;YACvD,IAAI,MAAM,EAAE;gBACV,sBAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAC;aAC9B;YACD,sBAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAC;;;CAC9B;AAND,sBAMC", "sourcesContent": ["import { getCurrentHub, initAndBind, Integrations as CoreIntegrations } from '@sentry/core';\nimport { getMainCarrier, setHubOnCarrier } from '@sentry/hub';\nimport { getGlobalObject } from '@sentry/utils';\nimport * as domain from 'domain';\n\nimport { NodeOptions } from './backend';\nimport { NodeClient } from './client';\nimport { Console, Http, LinkedErrors, OnUncaughtException, OnUnhandledRejection } from './integrations';\n\nexport const defaultIntegrations = [\n  // Common\n  new CoreIntegrations.InboundFilters(),\n  new CoreIntegrations.FunctionToString(),\n  // Native Wrappers\n  new Console(),\n  new Http(),\n  // Global Handlers\n  new OnUncaughtException(),\n  new OnUnhandledRejection(),\n  // Misc\n  new LinkedErrors(),\n];\n\n/**\n * The Sentry Node SDK Client.\n *\n * To use this SDK, call the {@link init} function as early as possible in the\n * main entry module. To set context information or send manual events, use the\n * provided methods.\n *\n * @example\n * ```\n *\n * const { init } = require('@sentry/node');\n *\n * init({\n *   dsn: '__DSN__',\n *   // ...\n * });\n * ```\n *\n * @example\n * ```\n *\n * const { configureScope } = require('@sentry/node');\n * configureScope((scope: Scope) => {\n *   scope.setExtra({ battery: 0.7 });\n *   scope.setTag({ user_mode: 'admin' });\n *   scope.setUser({ id: '4711' });\n * });\n * ```\n *\n * @example\n * ```\n *\n * const { addBreadcrumb } = require('@sentry/node');\n * addBreadcrumb({\n *   message: 'My Breadcrumb',\n *   // ...\n * });\n * ```\n *\n * @example\n * ```\n *\n * const Sentry = require('@sentry/node');\n * Sentry.captureMessage('Hello, world!');\n * Sentry.captureException(new Error('Good bye'));\n * Sentry.captureEvent({\n *   message: 'Manual',\n *   stacktrace: [\n *     // ...\n *   ],\n * });\n * ```\n *\n * @see {@link NodeOptions} for documentation on configuration options.\n */\nexport function init(options: NodeOptions = {}): void {\n  if (options.defaultIntegrations === undefined) {\n    options.defaultIntegrations = defaultIntegrations;\n  }\n\n  if (options.dsn === undefined && process.env.SENTRY_DSN) {\n    options.dsn = process.env.SENTRY_DSN;\n  }\n\n  if (options.tracesSampleRate === undefined && process.env.SENTRY_TRACES_SAMPLE_RATE) {\n    const tracesSampleRate = parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE);\n    if (isFinite(tracesSampleRate)) {\n      options.tracesSampleRate = tracesSampleRate;\n    }\n  }\n\n  if (options.release === undefined) {\n    const global = getGlobalObject<Window>();\n    // Prefer env var over global\n    if (process.env.SENTRY_RELEASE) {\n      options.release = process.env.SENTRY_RELEASE;\n    }\n    // This supports the variable that sentry-webpack-plugin injects\n    else if (global.SENTRY_RELEASE && global.SENTRY_RELEASE.id) {\n      options.release = global.SENTRY_RELEASE.id;\n    }\n  }\n\n  if (options.environment === undefined && process.env.SENTRY_ENVIRONMENT) {\n    options.environment = process.env.SENTRY_ENVIRONMENT;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n  if ((domain as any).active) {\n    setHubOnCarrier(getMainCarrier(), getCurrentHub());\n  }\n\n  initAndBind(NodeClient, options);\n}\n\n/**\n * This is the getter for lastEventId.\n *\n * @returns The last event id of a captured event.\n */\nexport function lastEventId(): string | undefined {\n  return getCurrentHub().lastEventId();\n}\n\n/**\n * A promise that resolves when all current events have been sent.\n * If you provide a timeout and the queue takes longer to drain the promise returns false.\n *\n * @param timeout Maximum time in ms the client should wait.\n */\nexport async function flush(timeout?: number): Promise<boolean> {\n  const client = getCurrentHub().getClient<NodeClient>();\n  if (client) {\n    return client.flush(timeout);\n  }\n  return Promise.reject(false);\n}\n\n/**\n * A promise that resolves when all current events have been sent.\n * If you provide a timeout and the queue takes longer to drain the promise returns false.\n *\n * @param timeout Maximum time in ms the client should wait.\n */\nexport async function close(timeout?: number): Promise<boolean> {\n  const client = getCurrentHub().getClient<NodeClient>();\n  if (client) {\n    return client.close(timeout);\n  }\n  return Promise.reject(false);\n}\n"]}