{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../../src/integrations/http.ts"], "names": [], "mappings": ";;AAAA,qCAA6C;AAE7C,uCAA0D;AAI1D,qCAOsB;AAEtB,IAAM,YAAY,GAAG,mBAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAExD,8BAA8B;AAC9B;IAqBE;;OAEG;IACH,cAAmB,OAA0D;QAA1D,wBAAA,EAAA,YAA0D;QAlB7E;;WAEG;QACI,SAAI,GAAW,IAAI,CAAC,EAAE,CAAC;QAgB5B,IAAI,CAAC,YAAY,GAAG,OAAO,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC;QAC5F,IAAI,CAAC,QAAQ,GAAG,OAAO,OAAO,CAAC,OAAO,KAAK,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IACnF,CAAC;IAED;;OAEG;IACI,wBAAS,GAAhB;QACE,2DAA2D;QAC3D,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACxC,OAAO;SACR;QAED,IAAM,mBAAmB,GAAG,kCAAkC,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEjG,IAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QACnC,YAAI,CAAC,UAAU,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC;QAC7C,YAAI,CAAC,UAAU,EAAE,SAAS,EAAE,mBAAmB,CAAC,CAAC;QAEjD,0FAA0F;QAC1F,4EAA4E;QAC5E,mGAAmG;QACnG,IAAI,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,GAAG,CAAC,EAAE;YAChD,IAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YACrC,YAAI,CAAC,WAAW,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC;YAC9C,YAAI,CAAC,WAAW,EAAE,SAAS,EAAE,mBAAmB,CAAC,CAAC;SACnD;IACH,CAAC;IAnDD;;OAEG;IACW,OAAE,GAAW,MAAM,CAAC;IAiDpC,WAAC;CAAA,AArDD,IAqDC;AArDY,oBAAI;AA4DjB;;;;;;;;GAQG;AACH,SAAS,kCAAkC,CACzC,kBAA2B,EAC3B,cAAuB;IAEvB,OAAO,SAAS,2BAA2B,CAAC,qBAA4C;QACtF,OAAO,SAAS,aAAa;YAAmC,cAA0B;iBAA1B,UAA0B,EAA1B,qBAA0B,EAA1B,IAA0B;gBAA1B,yBAA0B;;YACxF,4DAA4D;YAC5D,IAAM,UAAU,GAAG,IAAI,CAAC;YAExB,IAAM,WAAW,GAAG,2BAAoB,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAM,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACtC,IAAM,UAAU,GAAG,iBAAU,CAAC,cAAc,CAAC,CAAC;YAE9C,6GAA6G;YAC7G,IAAI,sBAAe,CAAC,UAAU,CAAC,EAAE;gBAC/B,OAAO,qBAAqB,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;aAC7D;YAED,IAAI,IAAsB,CAAC;YAC3B,IAAI,UAA4B,CAAC;YAEjC,IAAM,KAAK,GAAG,oBAAa,EAAE,CAAC,QAAQ,EAAE,CAAC;YACzC,IAAI,KAAK,IAAI,cAAc,EAAE;gBAC3B,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC7B,IAAI,UAAU,EAAE;oBACd,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC;wBAC3B,WAAW,EAAE,CAAG,cAAc,CAAC,MAAM,IAAI,KAAK,UAAI,UAAY;wBAC9D,EAAE,EAAE,SAAS;qBACd,CAAC,CAAC;oBAEH,IAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;oBAC/C,cAAM,CAAC,GAAG,CAAC,+DAA6D,iBAAmB,CAAC,CAAC;oBAC7F,cAAc,CAAC,OAAO,yCAAQ,cAAc,CAAC,OAAO,KAAE,cAAc,EAAE,iBAAiB,GAAE,CAAC;iBAC3F;aACF;YAED,sEAAsE;YACtE,OAAO,qBAAqB;iBACzB,KAAK,CAAC,UAAU,EAAE,WAAW,CAAC;iBAC9B,IAAI,CAAC,UAAU,EAAE,UAAmC,GAAyB;gBAC5E,4DAA4D;gBAC5D,IAAM,GAAG,GAAG,IAAI,CAAC;gBACjB,IAAI,kBAAkB,EAAE;oBACtB,oBAAoB,CAAC,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;iBACxD;gBACD,IAAI,cAAc,IAAI,IAAI,EAAE;oBAC1B,IAAI,GAAG,CAAC,UAAU,EAAE;wBAClB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;qBACpC;oBACD,IAAI,CAAC,WAAW,GAAG,2BAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,GAAG,CAAC,CAAC;oBAC/E,IAAI,CAAC,MAAM,EAAE,CAAC;iBACf;YACH,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,EAAE;gBACb,4DAA4D;gBAC5D,IAAM,GAAG,GAAG,IAAI,CAAC;gBAEjB,IAAI,kBAAkB,EAAE;oBACtB,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;iBAChD;gBACD,IAAI,cAAc,IAAI,IAAI,EAAE;oBAC1B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;oBACxB,IAAI,CAAC,WAAW,GAAG,2BAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,GAAG,CAAC,CAAC;oBAC/E,IAAI,CAAC,MAAM,EAAE,CAAC;iBACf;YACH,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,KAAa,EAAE,GAAW,EAAE,GAAuB,EAAE,GAA0B;IAC3G,IAAI,CAAC,oBAAa,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QACzC,OAAO;KACR;IAED,oBAAa,EAAE,CAAC,aAAa,CAC3B;QACE,QAAQ,EAAE,MAAM;QAChB,IAAI,EAAE;YACJ,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,WAAW,EAAE,GAAG,IAAI,GAAG,CAAC,UAAU;YAClC,GAAG,KAAA;SACJ;QACD,IAAI,EAAE,MAAM;KACb,EACD;QACE,KAAK,OAAA;QACL,OAAO,EAAE,GAAG;QACZ,QAAQ,EAAE,GAAG;KACd,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["import { getCurrentHub } from '@sentry/core';\nimport { Integration, Span } from '@sentry/types';\nimport { fill, logger, parseSemver } from '@sentry/utils';\nimport * as http from 'http';\nimport * as https from 'https';\n\nimport {\n  cleanSpanDescription,\n  extractUrl,\n  isSentryRequest,\n  normalizeRequestArgs,\n  RequestMethod,\n  RequestMethodArgs,\n} from './utils/http';\n\nconst NODE_VERSION = parseSemver(process.versions.node);\n\n/** http module integration */\nexport class Http implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Http';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = Http.id;\n\n  /**\n   * @inheritDoc\n   */\n  private readonly _breadcrumbs: boolean;\n\n  /**\n   * @inheritDoc\n   */\n  private readonly _tracing: boolean;\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(options: { breadcrumbs?: boolean; tracing?: boolean } = {}) {\n    this._breadcrumbs = typeof options.breadcrumbs === 'undefined' ? true : options.breadcrumbs;\n    this._tracing = typeof options.tracing === 'undefined' ? false : options.tracing;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    // No need to instrument if we don't want to track anything\n    if (!this._breadcrumbs && !this._tracing) {\n      return;\n    }\n\n    const wrappedHandlerMaker = _createWrappedRequestMethodFactory(this._breadcrumbs, this._tracing);\n\n    const httpModule = require('http');\n    fill(httpModule, 'get', wrappedHandlerMaker);\n    fill(httpModule, 'request', wrappedHandlerMaker);\n\n    // NOTE: Prior to Node 9, `https` used internals of `http` module, thus we don't patch it.\n    // If we do, we'd get double breadcrumbs and double spans for `https` calls.\n    // It has been changed in Node 9, so for all versions equal and above, we patch `https` separately.\n    if (NODE_VERSION.major && NODE_VERSION.major > 8) {\n      const httpsModule = require('https');\n      fill(httpsModule, 'get', wrappedHandlerMaker);\n      fill(httpsModule, 'request', wrappedHandlerMaker);\n    }\n  }\n}\n\n// for ease of reading below\ntype OriginalRequestMethod = RequestMethod;\ntype WrappedRequestMethod = RequestMethod;\ntype WrappedRequestMethodFactory = (original: OriginalRequestMethod) => WrappedRequestMethod;\n\n/**\n * Function which creates a function which creates wrapped versions of internal `request` and `get` calls within `http`\n * and `https` modules. (NB: Not a typo - this is a creator^2!)\n *\n * @param breadcrumbsEnabled Whether or not to record outgoing requests as breadcrumbs\n * @param tracingEnabled Whether or not to record outgoing requests as tracing spans\n *\n * @returns A function which accepts the exiting handler and returns a wrapped handler\n */\nfunction _createWrappedRequestMethodFactory(\n  breadcrumbsEnabled: boolean,\n  tracingEnabled: boolean,\n): WrappedRequestMethodFactory {\n  return function wrappedRequestMethodFactory(originalRequestMethod: OriginalRequestMethod): WrappedRequestMethod {\n    return function wrappedMethod(this: typeof http | typeof https, ...args: RequestMethodArgs): http.ClientRequest {\n      // eslint-disable-next-line @typescript-eslint/no-this-alias\n      const httpModule = this;\n\n      const requestArgs = normalizeRequestArgs(args);\n      const requestOptions = requestArgs[0];\n      const requestUrl = extractUrl(requestOptions);\n\n      // we don't want to record requests to Sentry as either breadcrumbs or spans, so just use the original method\n      if (isSentryRequest(requestUrl)) {\n        return originalRequestMethod.apply(httpModule, requestArgs);\n      }\n\n      let span: Span | undefined;\n      let parentSpan: Span | undefined;\n\n      const scope = getCurrentHub().getScope();\n      if (scope && tracingEnabled) {\n        parentSpan = scope.getSpan();\n        if (parentSpan) {\n          span = parentSpan.startChild({\n            description: `${requestOptions.method || 'GET'} ${requestUrl}`,\n            op: 'request',\n          });\n\n          const sentryTraceHeader = span.toTraceparent();\n          logger.log(`[Tracing] Adding sentry-trace header to outgoing request: ${sentryTraceHeader}`);\n          requestOptions.headers = { ...requestOptions.headers, 'sentry-trace': sentryTraceHeader };\n        }\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      return originalRequestMethod\n        .apply(httpModule, requestArgs)\n        .once('response', function(this: http.ClientRequest, res: http.IncomingMessage): void {\n          // eslint-disable-next-line @typescript-eslint/no-this-alias\n          const req = this;\n          if (breadcrumbsEnabled) {\n            addRequestBreadcrumb('response', requestUrl, req, res);\n          }\n          if (tracingEnabled && span) {\n            if (res.statusCode) {\n              span.setHttpStatus(res.statusCode);\n            }\n            span.description = cleanSpanDescription(span.description, requestOptions, req);\n            span.finish();\n          }\n        })\n        .once('error', function(this: http.ClientRequest): void {\n          // eslint-disable-next-line @typescript-eslint/no-this-alias\n          const req = this;\n\n          if (breadcrumbsEnabled) {\n            addRequestBreadcrumb('error', requestUrl, req);\n          }\n          if (tracingEnabled && span) {\n            span.setHttpStatus(500);\n            span.description = cleanSpanDescription(span.description, requestOptions, req);\n            span.finish();\n          }\n        });\n    };\n  };\n}\n\n/**\n * Captures Breadcrumb based on provided request/response pair\n */\nfunction addRequestBreadcrumb(event: string, url: string, req: http.ClientRequest, res?: http.IncomingMessage): void {\n  if (!getCurrentHub().getIntegration(Http)) {\n    return;\n  }\n\n  getCurrentHub().addBreadcrumb(\n    {\n      category: 'http',\n      data: {\n        method: req.method,\n        status_code: res && res.statusCode,\n        url,\n      },\n      type: 'http',\n    },\n    {\n      event,\n      request: req,\n      response: res,\n    },\n  );\n}\n"]}