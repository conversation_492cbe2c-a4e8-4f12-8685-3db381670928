{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../../../src/integrations/utils/http.ts"], "names": [], "mappings": ";;AAAA,qCAA6C;AAE7C,2BAA0B;AAE1B;;;GAGG;AACH,SAAgB,eAAe,CAAC,GAAW;;IACzC,IAAM,GAAG,SAAG,oBAAa,EAAE;SACxB,SAAS,EAAE,0CACV,MAAM,EAAE,CAAC;IACb,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC9C,CAAC;AALD,0CAKC;AAED;;;;;GAKG;AACH,SAAgB,UAAU,CAAC,cAA8B;IACvD,IAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;IAC/C,IAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,IAAI,cAAc,CAAC,IAAI,IAAI,EAAE,CAAC;IACtE,2EAA2E;IAC3E,IAAM,IAAI,GACR,CAAC,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,KAAK,EAAE,IAAI,cAAc,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAI,cAAc,CAAC,IAAM,CAAC;IACrH,IAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;IAE7D,OAAU,QAAQ,UAAK,QAAQ,GAAG,IAAI,GAAG,IAAM,CAAC;AAClD,CAAC;AATD,gCASC;AAED;;;;;;;;GAQG;AACH,SAAgB,oBAAoB,CAClC,WAA+B,EAC/B,cAA8B,EAC9B,OAA2B;;IAE3B,mBAAmB;IACnB,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,WAAW,CAAC;KACpB;IAED,wCAAwC;IACpC,IAAA,8CAA6C,EAA5C,cAAM,EAAE,kBAAoC,CAAC;IAElD,sHAAsH;IACtH,sEAAsE;IACtE,IAAI,cAAc,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;QACnD,0GAA0G;QAC1G,cAAc,CAAC,QAAQ,eAAI,OAAe,0CAAE,KAAK,0CAAE,QAAQ,CAAC,CAAC,8DAA8D;QAC3H,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC;KACzC;IAED,mFAAmF;IACnF,UAAI,UAAU,0CAAE,UAAU,CAAC,KAAK,GAAG;QACjC,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAClC;IAED,OAAU,MAAM,SAAI,UAAY,CAAC;AACnC,CAAC;AA3BD,oDA2BC;AAUD;;;;;;;GAOG;AACH,SAAgB,YAAY,CAAC,GAAQ;IACnC,IAAM,OAAO,GAAmB;QAC9B,QAAQ,EAAE,GAAG,CAAC,QAAQ;QACtB,QAAQ,EACN,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ;QAC7G,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,QAAQ,EAAE,GAAG,CAAC,QAAQ;QACtB,IAAI,EAAE,MAAG,GAAG,CAAC,QAAQ,IAAI,EAAE,KAAG,GAAG,CAAC,MAAM,IAAI,EAAE,CAAE;QAChD,IAAI,EAAE,GAAG,CAAC,IAAI;KACf,CAAC;IACF,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE;QACnB,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KACjC;IACD,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,EAAE;QAChC,OAAO,CAAC,IAAI,GAAM,GAAG,CAAC,QAAQ,SAAI,GAAG,CAAC,QAAU,CAAC;KAClD;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAlBD,oCAkBC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,oBAAoB,CAClC,WAA8B;IAE9B,IAAI,QAAQ,EAAE,cAAc,CAAC;IAE7B,wCAAwC;IACxC,IAAI,OAAO,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;QAC7D,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAqB,CAAC;KACjD;IAED,0DAA0D;IAC1D,IAAI,OAAO,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACtC,cAAc,GAAG,YAAY,CAAC,IAAI,SAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACxD;SAAM,IAAI,WAAW,CAAC,CAAC,CAAC,YAAY,SAAG,EAAE;QACxC,cAAc,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C;SAAM;QACL,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;KACjC;IAED,kEAAkE;IAClE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,cAAc,yCAAQ,cAAc,GAAK,WAAW,CAAC,CAAC,CAAC,CAAE,CAAC;KAC3D;IAED,mCAAmC;IACnC,IAAI,QAAQ,EAAE;QACZ,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;KACnC;SAAM;QACL,OAAO,CAAC,cAAc,CAAC,CAAC;KACzB;AACH,CAAC;AA9BD,oDA8BC", "sourcesContent": ["import { getCurrentHub } from '@sentry/core';\nimport * as http from 'http';\nimport { URL } from 'url';\n\n/**\n * Checks whether given url points to Sentry server\n * @param url url to verify\n */\nexport function isSentryRequest(url: string): boolean {\n  const dsn = getCurrentHub()\n    .getClient()\n    ?.getDsn();\n  return dsn ? url.includes(dsn.host) : false;\n}\n\n/**\n * Assemble a URL to be used for breadcrumbs and spans.\n *\n * @param requestOptions RequestOptions object containing the component parts for a URL\n * @returns Fully-formed URL\n */\nexport function extractUrl(requestOptions: RequestOptions): string {\n  const protocol = requestOptions.protocol || '';\n  const hostname = requestOptions.hostname || requestOptions.host || '';\n  // Don't log standard :80 (http) and :443 (https) ports to reduce the noise\n  const port =\n    !requestOptions.port || requestOptions.port === 80 || requestOptions.port === 443 ? '' : `:${requestOptions.port}`;\n  const path = requestOptions.path ? requestOptions.path : '/';\n\n  return `${protocol}//${hostname}${port}${path}`;\n}\n\n/**\n * Handle various edge cases in the span description (for spans representing http(s) requests).\n *\n * @param description current `description` property of the span representing the request\n * @param requestOptions Configuration data for the request\n * @param Request Request object\n *\n * @returns The cleaned description\n */\nexport function cleanSpanDescription(\n  description: string | undefined,\n  requestOptions: RequestOptions,\n  request: http.ClientRequest,\n): string | undefined {\n  // nothing to clean\n  if (!description) {\n    return description;\n  }\n\n  // eslint-disable-next-line prefer-const\n  let [method, requestUrl] = description.split(' ');\n\n  // superagent sticks the protocol in a weird place (we check for host because if both host *and* protocol are missing,\n  // we're likely dealing with an internal route and this doesn't apply)\n  if (requestOptions.host && !requestOptions.protocol) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n    requestOptions.protocol = (request as any)?.agent?.protocol; // worst comes to worst, this is undefined and nothing changes\n    requestUrl = extractUrl(requestOptions);\n  }\n\n  // internal routes can end up starting with a triple slash rather than a single one\n  if (requestUrl?.startsWith('///')) {\n    requestUrl = requestUrl.slice(2);\n  }\n\n  return `${method} ${requestUrl}`;\n}\n\n// the node types are missing a few properties which node's `urlToOptions` function spits out\nexport type RequestOptions = http.RequestOptions & { hash?: string; search?: string; pathname?: string; href?: string };\ntype RequestCallback = (response: http.IncomingMessage) => void;\nexport type RequestMethodArgs =\n  | [RequestOptions | string | URL, RequestCallback?]\n  | [string | URL, RequestOptions, RequestCallback?];\nexport type RequestMethod = (...args: RequestMethodArgs) => http.ClientRequest;\n\n/**\n * Convert a URL object into a RequestOptions object.\n *\n * Copied from Node's internals (where it's used in http(s).request() and http(s).get()), modified only to use the\n * RequestOptions type above.\n *\n * See https://github.com/nodejs/node/blob/master/lib/internal/url.js.\n */\nexport function urlToOptions(url: URL): RequestOptions {\n  const options: RequestOptions = {\n    protocol: url.protocol,\n    hostname:\n      typeof url.hostname === 'string' && url.hostname.startsWith('[') ? url.hostname.slice(1, -1) : url.hostname,\n    hash: url.hash,\n    search: url.search,\n    pathname: url.pathname,\n    path: `${url.pathname || ''}${url.search || ''}`,\n    href: url.href,\n  };\n  if (url.port !== '') {\n    options.port = Number(url.port);\n  }\n  if (url.username || url.password) {\n    options.auth = `${url.username}:${url.password}`;\n  }\n  return options;\n}\n\n/**\n * Normalize inputs to `http(s).request()` and `http(s).get()`.\n *\n * Legal inputs to `http(s).request()` and `http(s).get()` can take one of ten forms:\n *     [ RequestOptions | string | URL ],\n *     [ RequestOptions | string | URL, RequestCallback ],\n *     [ string | URL, RequestOptions ], and\n *     [ string | URL, RequestOptions, RequestCallback ].\n *\n * This standardizes to one of two forms: [ RequestOptions ] and [ RequestOptions, RequestCallback ]. A similar thing is\n * done as the first step of `http(s).request()` and `http(s).get()`; this just does it early so that we can interact\n * with the args in a standard way.\n *\n * @param requestArgs The inputs to `http(s).request()` or `http(s).get()`, as an array.\n *\n * @returns Equivalent args of the form [ RequestOptions ] or [ RequestOptions, RequestCallback ].\n */\nexport function normalizeRequestArgs(\n  requestArgs: RequestMethodArgs,\n): [RequestOptions] | [RequestOptions, RequestCallback] {\n  let callback, requestOptions;\n\n  // pop off the callback, if there is one\n  if (typeof requestArgs[requestArgs.length - 1] === 'function') {\n    callback = requestArgs.pop() as RequestCallback;\n  }\n\n  // create a RequestOptions object of whatever's at index 0\n  if (typeof requestArgs[0] === 'string') {\n    requestOptions = urlToOptions(new URL(requestArgs[0]));\n  } else if (requestArgs[0] instanceof URL) {\n    requestOptions = urlToOptions(requestArgs[0]);\n  } else {\n    requestOptions = requestArgs[0];\n  }\n\n  // if the options were given separately from the URL, fold them in\n  if (requestArgs.length === 2) {\n    requestOptions = { ...requestOptions, ...requestArgs[1] };\n  }\n\n  // return args in standardized form\n  if (callback) {\n    return [requestOptions, callback];\n  } else {\n    return [requestOptions];\n  }\n}\n"]}