{"version": 3, "file": "handlers.js", "sourceRoot": "", "sources": ["../src/handlers.ts"], "names": [], "mappings": ";;AAAA,8BAA8B;AAC9B,uDAAuD;AACvD,qCAA4F;AAC5F,2CAA+D;AAE/D,uCAOuB;AACvB,+BAAiC;AAEjC,uBAAyB;AAGzB,6BAA8B;AAE9B,IAAM,wBAAwB,GAAG,IAAI,CAAC;AAoBtC;;;GAGG;AACH,SAAgB,cAAc;IAK5B,OAAO,SAAS,uBAAuB,CACrC,GAAyB,EACzB,GAAwB,EACxB,IAA2B;QAE3B,6GAA6G;QAC7G,IAAI,eAAe,CAAC;QACpB,IAAI,GAAG,CAAC,OAAO,IAAI,gBAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE;YACxD,eAAe,GAAG,gCAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAW,CAAC,CAAC;SACjF;QAED,IAAM,WAAW,GAAG,uBAAgB,oBAClC,IAAI,EAAE,6BAA6B,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,EACtE,EAAE,EAAE,aAAa,IACd,eAAe,EAClB,CAAC;QAEH,yEAAyE;QACzE,oBAAa,EAAE,CAAC,cAAc,CAAC,UAAA,KAAK;YAClC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,mGAAmG;QACnG,qBAAqB;QACrB,sEAAsE;QACrE,GAAW,CAAC,oBAAoB,GAAG,WAAW,CAAC;QAEhD,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjB,gHAAgH;YAChH,SAAS;YACT,YAAY,CAAC;gBACX,0BAA0B,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;gBAC7C,WAAW,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC1C,WAAW,CAAC,MAAM,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC;AA5CD,wCA4CC;AAED;;;GAGG;AACH,SAAS,0BAA0B,CAAC,WAAoC,EAAE,GAAmB;IAC3F,IAAI,CAAC,WAAW;QAAE,OAAO;IACzB,WAAW,CAAC,IAAI,GAAG,6BAA6B,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;IACpF,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC;IAC5C,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;IAC5C,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,6BAA6B,CACpC,GAAmB,EACnB,OAAkD;IAAlD,wBAAA,EAAA,YAAkD;;IAElD,IAAM,MAAM,SAAG,GAAG,CAAC,MAAM,0CAAE,WAAW,EAAE,CAAC;IAEzC,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAI,GAAG,CAAC,KAAK,EAAE;QACb,+FAA+F;QAC/F,gHAAgH;QAChH,IAAI,GAAG,KAAG,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,IAAM,CAAC;KAC1C;SAAM,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG,EAAE;QACrC,IAAI,GAAG,gCAAwB,CAAC,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;KACnE;IAED,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAI,OAAO,CAAC,MAAM,IAAI,MAAM,EAAE;QAC5B,IAAI,IAAI,MAAM,CAAC;KAChB;IACD,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE;QAClC,IAAI,IAAI,GAAG,CAAC;KACb;IACD,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;QACxB,IAAI,IAAI,IAAI,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAID,YAAY;AACZ,SAAS,kBAAkB,CAAC,GAAmB,EAAE,IAAuC;;IACtF,QAAQ,IAAI,EAAE;QACZ,KAAK,MAAM,CAAC,CAAC;YACX,OAAO,6BAA6B,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;SAC3D;QACD,KAAK,SAAS,CAAC,CAAC;YACd,OAAO,OAAA,GAAG,CAAC,KAAK,0CAAE,KAAK,CAAC,CAAC,EAAE,IAAI,KAAI,aAAa,CAAC;SAClD;QACD,KAAK,YAAY,CAAC;QAClB,OAAO,CAAC,CAAC;YACP,OAAO,6BAA6B,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;SACzE;KACF;AACH,CAAC;AAED,yEAAyE;AACzE,IAAM,iBAAiB,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAEtD,YAAY;AACZ,SAAS,eAAe,CACtB,IAEC,EACD,IAAwB;IAExB,IAAM,aAAa,GAA2B,EAAE,CAAC;IACjD,IAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC;IAElE,UAAU,CAAC,OAAO,CAAC,UAAA,GAAG;QACpB,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE;YACvB,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;SAChC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,aAAa,CAAC;AACvB,CAAC;AAcD;;;;;;;GAOG;AACH,SAAgB,YAAY,CAAC,KAAY,EAAE,GAAmB,EAAE,OAA6B;IAC3F,6CAA6C;IAC7C,OAAO,sBACL,EAAE,EAAE,KAAK,EACT,OAAO,EAAE,IAAI,EACb,UAAU,EAAE,IAAI,EAChB,WAAW,EAAE,IAAI,EACjB,IAAI,EAAE,IAAI,EACV,OAAO,EAAE,IAAI,IACV,OAAO,CACX,CAAC;IAEF,IAAI,OAAO,CAAC,OAAO,EAAE;QACnB,KAAK,CAAC,QAAQ,yCACT,KAAK,CAAC,QAAQ,KACjB,OAAO,EAAE;gBACP,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;aAChC,GACF,CAAC;KACH;IAED,IAAI,OAAO,CAAC,OAAO,EAAE;QACnB,mHAAmH;QACnH,IAAM,oBAAoB,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;YACzD,CAAC,CAAC,8BAAsB,CAAC,GAAG,EAAE,OAAO,CAAC,OAAO,CAAC;YAC9C,CAAC,CAAC,8BAAsB,CAAC,GAAG,CAAC,CAAC;QAChC,KAAK,CAAC,OAAO,yCACR,KAAK,CAAC,OAAO,GACb,oBAAoB,CACxB,CAAC;KACH;IAED,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;QAC5C,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;KACrE;IAED,IAAI,OAAO,CAAC,IAAI,EAAE;QAChB,IAAM,aAAa,GAAG,GAAG,CAAC,IAAI,IAAI,qBAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEzG,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YAC9B,KAAK,CAAC,IAAI,yCACL,KAAK,CAAC,IAAI,GACV,aAAa,CACjB,CAAC;SACH;KACF;IAED,aAAa;IACb,uCAAuC;IACvC,yBAAyB;IACzB,IAAI,OAAO,CAAC,EAAE,EAAE;QACd,IAAM,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QACtE,IAAI,EAAE,EAAE;YACN,KAAK,CAAC,IAAI,yCACL,KAAK,CAAC,IAAI,KACb,UAAU,EAAE,EAAE,GACf,CAAC;SACH;KACF;IAED,IAAI,OAAO,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;QAC7C,KAAK,CAAC,WAAW,GAAG,kBAAkB,CAAC,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;KAClE;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAlED,oCAkEC;AAMD;;;GAGG;AACH,SAAgB,cAAc,CAC5B,OAA+B;IAE/B,OAAO,SAAS,uBAAuB,CACrC,GAAyB,EACzB,GAAwB,EACxB,IAA2B;QAE3B,IAAI,OAAO,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,GAAG,CAAC,EAAE;YAC/D,6DAA6D;YAC7D,IAAM,MAAI,GAAG,GAAG,CAAC,GAAG,CAAC;YACrB,GAAG,CAAC,GAAG,GAAG,UAAS,KAA0B,EAAE,QAAgC,EAAE,EAAe;gBAAtF,iBAQT;gBAPC,WAAK,CAAC,OAAO,CAAC,YAAY,CAAC;qBACxB,IAAI,CAAC;oBACJ,MAAI,CAAC,IAAI,CAAC,KAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;gBACvC,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,EAAE,UAAA,CAAC;oBACX,cAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;SACH;QACD,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QAC9B,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACf,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACf,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxB,KAAK,CAAC,GAAG,CAAC;YACR,oBAAa,EAAE,CAAC,cAAc,CAAC,UAAA,KAAK;gBAClC,OAAA,KAAK,CAAC,iBAAiB,CAAC,UAAC,KAAY,IAAK,OAAA,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,EAAjC,CAAiC,CAAC;YAA5E,CAA4E,CAC7E,CAAC;YACF,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAhCD,wCAgCC;AAYD,YAAY;AACZ,SAAS,yBAAyB,CAAC,KAAsB;IACvD,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACtH,OAAO,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAoB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC/D,CAAC;AAED,6DAA6D;AAC7D,SAAS,wBAAwB,CAAC,KAAsB;IACtD,IAAM,MAAM,GAAG,yBAAyB,CAAC,KAAK,CAAC,CAAC;IAChD,OAAO,MAAM,IAAI,GAAG,CAAC;AACvB,CAAC;AAED;;;GAGG;AACH,SAAgB,YAAY,CAAC,OAM5B;IAMC,OAAO,SAAS,qBAAqB,CACnC,KAAsB,EACtB,IAA0B,EAC1B,GAAwB,EACxB,IAAsC;QAEtC,6DAA6D;QAC7D,IAAM,iBAAiB,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,iBAAiB,CAAC,IAAI,wBAAwB,CAAC;QAE7F,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE;YAC5B,gBAAS,CAAC,UAAA,MAAM;gBACd,oEAAoE;gBACpE,sEAAsE;gBACtE,IAAM,WAAW,GAAI,GAAW,CAAC,oBAA4B,CAAC;gBAC9D,IAAI,WAAW,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,SAAS,EAAE;oBACjD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;iBAC7B;gBACD,IAAM,OAAO,GAAG,uBAAgB,CAAC,KAAK,CAAC,CAAC;gBACxC,sEAAsE;gBACrE,GAAW,CAAC,MAAM,GAAG,OAAO,CAAC;gBAC9B,IAAI,CAAC,KAAK,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,OAAO;SACR;QAED,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAxCD,oCAwCC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,KAAY;IAC5C,sCAAsC;IACtC,OAAO,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAE1D,IAAM,MAAM,GAAG,oBAAa,EAAE,CAAC,SAAS,EAAc,CAAC;IAEvD,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,cAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QAC1E,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,OAAO;KACR;IAED,IAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;IACpC,IAAM,OAAO,GACX,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,eAAe,GAAG,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC;QAC9F,wBAAwB,CAAC;IAC3B,cAAM,CACJ,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAC,MAAe;QACzC,IAAI,CAAC,MAAM,EAAE;YACX,cAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;SAC3F;QACD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CACH,CAAC;AACJ,CAAC;AAxBD,8CAwBC", "sourcesContent": ["/* eslint-disable max-lines */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { captureException, getCurrentHub, startTransaction, withScope } from '@sentry/core';\nimport { extractTraceparentData, Span } from '@sentry/tracing';\nimport { Event, Transaction } from '@sentry/types';\nimport {\n  extractNodeRequestData,\n  forget,\n  isPlainObject,\n  isString,\n  logger,\n  stripUrlQueryAndFragment,\n} from '@sentry/utils';\nimport * as domain from 'domain';\nimport * as http from 'http';\nimport * as os from 'os';\n\nimport { NodeClient } from './client';\nimport { flush } from './sdk';\n\nconst DEFAULT_SHUTDOWN_TIMEOUT = 2000;\n\nexport interface ExpressRequest extends http.IncomingMessage {\n  [key: string]: any;\n  baseUrl?: string;\n  ip?: string;\n  originalUrl?: string;\n  route?: {\n    path: string;\n    stack: [\n      {\n        name: string;\n      },\n    ];\n  };\n  user?: {\n    [key: string]: any;\n  };\n}\n\n/**\n * Express-compatible tracing handler.\n * @see Exposed as `Handlers.tracingHandler`\n */\nexport function tracingHandler(): (\n  req: http.IncomingMessage,\n  res: http.ServerResponse,\n  next: (error?: any) => void,\n) => void {\n  return function sentryTracingMiddleware(\n    req: http.IncomingMessage,\n    res: http.ServerResponse,\n    next: (error?: any) => void,\n  ): void {\n    // If there is a trace header set, we extract the data from it (parentSpanId, traceId, and sampling decision)\n    let traceparentData;\n    if (req.headers && isString(req.headers['sentry-trace'])) {\n      traceparentData = extractTraceparentData(req.headers['sentry-trace'] as string);\n    }\n\n    const transaction = startTransaction({\n      name: extractExpressTransactionName(req, { path: true, method: true }),\n      op: 'http.server',\n      ...traceparentData,\n    });\n\n    // We put the transaction on the scope so users can attach children to it\n    getCurrentHub().configureScope(scope => {\n      scope.setSpan(transaction);\n    });\n\n    // We also set __sentry_transaction on the response so people can grab the transaction there to add\n    // spans to it later.\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    (res as any).__sentry_transaction = transaction;\n\n    res.once('finish', () => {\n      // Push `transaction.finish` to the next event loop so open spans have a chance to finish before the transaction\n      // closes\n      setImmediate(() => {\n        addExpressReqToTransaction(transaction, req);\n        transaction.setHttpStatus(res.statusCode);\n        transaction.finish();\n      });\n    });\n\n    next();\n  };\n}\n\n/**\n * Set parameterized as transaction name e.g.: `GET /users/:id`\n * Also adds more context data on the transaction from the request\n */\nfunction addExpressReqToTransaction(transaction: Transaction | undefined, req: ExpressRequest): void {\n  if (!transaction) return;\n  transaction.name = extractExpressTransactionName(req, { path: true, method: true });\n  transaction.setData('url', req.originalUrl);\n  transaction.setData('baseUrl', req.baseUrl);\n  transaction.setData('query', req.query);\n}\n\n/**\n * Extracts complete generalized path from the request object and uses it to construct transaction name.\n *\n * eg. GET /mountpoint/user/:id\n *\n * @param req The ExpressRequest object\n * @param options What to include in the transaction name (method, path, or both)\n *\n * @returns The fully constructed transaction name\n */\nfunction extractExpressTransactionName(\n  req: ExpressRequest,\n  options: { path?: boolean; method?: boolean } = {},\n): string {\n  const method = req.method?.toUpperCase();\n\n  let path = '';\n  if (req.route) {\n    // if the mountpoint is `/`, req.baseUrl is '' (not undefined), so it's safe to include it here\n    // see https://github.com/expressjs/express/blob/508936853a6e311099c9985d4c11a4b1b8f6af07/test/req.baseUrl.js#L7\n    path = `${req.baseUrl}${req.route.path}`;\n  } else if (req.originalUrl || req.url) {\n    path = stripUrlQueryAndFragment(req.originalUrl || req.url || '');\n  }\n\n  let info = '';\n  if (options.method && method) {\n    info += method;\n  }\n  if (options.method && options.path) {\n    info += ` `;\n  }\n  if (options.path && path) {\n    info += path;\n  }\n\n  return info;\n}\n\ntype TransactionNamingScheme = 'path' | 'methodPath' | 'handler';\n\n/** JSDoc */\nfunction extractTransaction(req: ExpressRequest, type: boolean | TransactionNamingScheme): string {\n  switch (type) {\n    case 'path': {\n      return extractExpressTransactionName(req, { path: true });\n    }\n    case 'handler': {\n      return req.route?.stack[0].name || '<anonymous>';\n    }\n    case 'methodPath':\n    default: {\n      return extractExpressTransactionName(req, { path: true, method: true });\n    }\n  }\n}\n\n/** Default user keys that'll be used to extract data from the request */\nconst DEFAULT_USER_KEYS = ['id', 'username', 'email'];\n\n/** JSDoc */\nfunction extractUserData(\n  user: {\n    [key: string]: any;\n  },\n  keys: boolean | string[],\n): { [key: string]: any } {\n  const extractedUser: { [key: string]: any } = {};\n  const attributes = Array.isArray(keys) ? keys : DEFAULT_USER_KEYS;\n\n  attributes.forEach(key => {\n    if (user && key in user) {\n      extractedUser[key] = user[key];\n    }\n  });\n\n  return extractedUser;\n}\n\n/**\n * Options deciding what parts of the request to use when enhancing an event\n */\nexport interface ParseRequestOptions {\n  ip?: boolean;\n  request?: boolean | string[];\n  serverName?: boolean;\n  transaction?: boolean | TransactionNamingScheme;\n  user?: boolean | string[];\n  version?: boolean;\n}\n\n/**\n * Enriches passed event with request data.\n *\n * @param event Will be mutated and enriched with req data\n * @param req Request object\n * @param options object containing flags to enable functionality\n * @hidden\n */\nexport function parseRequest(event: Event, req: ExpressRequest, options?: ParseRequestOptions): Event {\n  // eslint-disable-next-line no-param-reassign\n  options = {\n    ip: false,\n    request: true,\n    serverName: true,\n    transaction: true,\n    user: true,\n    version: true,\n    ...options,\n  };\n\n  if (options.version) {\n    event.contexts = {\n      ...event.contexts,\n      runtime: {\n        name: 'node',\n        version: global.process.version,\n      },\n    };\n  }\n\n  if (options.request) {\n    // if the option value is `true`, use the default set of keys by not passing anything to `extractNodeRequestData()`\n    const extractedRequestData = Array.isArray(options.request)\n      ? extractNodeRequestData(req, options.request)\n      : extractNodeRequestData(req);\n    event.request = {\n      ...event.request,\n      ...extractedRequestData,\n    };\n  }\n\n  if (options.serverName && !event.server_name) {\n    event.server_name = global.process.env.SENTRY_NAME || os.hostname();\n  }\n\n  if (options.user) {\n    const extractedUser = req.user && isPlainObject(req.user) ? extractUserData(req.user, options.user) : {};\n\n    if (Object.keys(extractedUser)) {\n      event.user = {\n        ...event.user,\n        ...extractedUser,\n      };\n    }\n  }\n\n  // client ip:\n  //   node: req.connection.remoteAddress\n  //   express, koa: req.ip\n  if (options.ip) {\n    const ip = req.ip || (req.connection && req.connection.remoteAddress);\n    if (ip) {\n      event.user = {\n        ...event.user,\n        ip_address: ip,\n      };\n    }\n  }\n\n  if (options.transaction && !event.transaction) {\n    event.transaction = extractTransaction(req, options.transaction);\n  }\n\n  return event;\n}\n\nexport type RequestHandlerOptions = ParseRequestOptions & {\n  flushTimeout?: number;\n};\n\n/**\n * Express compatible request handler.\n * @see Exposed as `Handlers.requestHandler`\n */\nexport function requestHandler(\n  options?: RequestHandlerOptions,\n): (req: http.IncomingMessage, res: http.ServerResponse, next: (error?: any) => void) => void {\n  return function sentryRequestMiddleware(\n    req: http.IncomingMessage,\n    res: http.ServerResponse,\n    next: (error?: any) => void,\n  ): void {\n    if (options && options.flushTimeout && options.flushTimeout > 0) {\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      const _end = res.end;\n      res.end = function(chunk?: any | (() => void), encoding?: string | (() => void), cb?: () => void): void {\n        flush(options.flushTimeout)\n          .then(() => {\n            _end.call(this, chunk, encoding, cb);\n          })\n          .then(null, e => {\n            logger.error(e);\n          });\n      };\n    }\n    const local = domain.create();\n    local.add(req);\n    local.add(res);\n    local.on('error', next);\n    local.run(() => {\n      getCurrentHub().configureScope(scope =>\n        scope.addEventProcessor((event: Event) => parseRequest(event, req, options)),\n      );\n      next();\n    });\n  };\n}\n\n/** JSDoc */\ninterface MiddlewareError extends Error {\n  status?: number | string;\n  statusCode?: number | string;\n  status_code?: number | string;\n  output?: {\n    statusCode?: number | string;\n  };\n}\n\n/** JSDoc */\nfunction getStatusCodeFromResponse(error: MiddlewareError): number {\n  const statusCode = error.status || error.statusCode || error.status_code || (error.output && error.output.statusCode);\n  return statusCode ? parseInt(statusCode as string, 10) : 500;\n}\n\n/** Returns true if response code is internal server error */\nfunction defaultShouldHandleError(error: MiddlewareError): boolean {\n  const status = getStatusCodeFromResponse(error);\n  return status >= 500;\n}\n\n/**\n * Express compatible error handler.\n * @see Exposed as `Handlers.errorHandler`\n */\nexport function errorHandler(options?: {\n  /**\n   * Callback method deciding whether error should be captured and sent to Sentry\n   * @param error Captured middleware error\n   */\n  shouldHandleError?(error: MiddlewareError): boolean;\n}): (\n  error: MiddlewareError,\n  req: http.IncomingMessage,\n  res: http.ServerResponse,\n  next: (error: MiddlewareError) => void,\n) => void {\n  return function sentryErrorMiddleware(\n    error: MiddlewareError,\n    _req: http.IncomingMessage,\n    res: http.ServerResponse,\n    next: (error: MiddlewareError) => void,\n  ): void {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const shouldHandleError = (options && options.shouldHandleError) || defaultShouldHandleError;\n\n    if (shouldHandleError(error)) {\n      withScope(_scope => {\n        // For some reason we need to set the transaction on the scope again\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        const transaction = (res as any).__sentry_transaction as Span;\n        if (transaction && _scope.getSpan() === undefined) {\n          _scope.setSpan(transaction);\n        }\n        const eventId = captureException(error);\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        (res as any).sentry = eventId;\n        next(error);\n      });\n\n      return;\n    }\n\n    next(error);\n  };\n}\n\n/**\n * @hidden\n */\nexport function logAndExitProcess(error: Error): void {\n  // eslint-disable-next-line no-console\n  console.error(error && error.stack ? error.stack : error);\n\n  const client = getCurrentHub().getClient<NodeClient>();\n\n  if (client === undefined) {\n    logger.warn('No NodeClient was defined, we are exiting the process now.');\n    global.process.exit(1);\n    return;\n  }\n\n  const options = client.getOptions();\n  const timeout =\n    (options && options.shutdownTimeout && options.shutdownTimeout > 0 && options.shutdownTimeout) ||\n    DEFAULT_SHUTDOWN_TIMEOUT;\n  forget(\n    client.close(timeout).then((result: boolean) => {\n      if (!result) {\n        logger.warn('We reached the timeout for emptying the request buffer, still exiting now!');\n      }\n      global.process.exit(1);\n    }),\n  );\n}\n"]}