{"version": 3, "file": "getFID.js", "sourceRoot": "", "sources": ["../../../src/browser/web-vitals/getFID.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,OAAO,EAA2B,MAAM,eAAe,CAAC;AACjE,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAwB1C,MAAM,CAAC,IAAM,MAAM,GAAG,UAAC,QAAuB;IAC5C,IAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IACjC,IAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IAErC,IAAM,YAAY,GAAG,UAAC,KAA6B;QACjD,kEAAkE;QAClE,IAAI,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,EAAE;YAC3C,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YACtB,MAAM,EAAE,CAAC;SACV;IACH,CAAC,CAAC;IAEF,IAAM,EAAE,GAAG,OAAO,CAAC,aAAa,EAAE,YAAuC,CAAC,CAAC;IAC3E,IAAM,MAAM,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;IAElD,IAAI,EAAE,EAAE;QACN,QAAQ,CAAC;YACP,EAAE,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,YAAuC,CAAC,CAAC;YAC9D,EAAE,CAAC,UAAU,EAAE,CAAC;QAClB,CAAC,EAAE,IAAI,CAAC,CAAC;KACV;SAAM;QACL,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE;YAC9D,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC,UAAC,KAAa,EAAE,KAAY;gBAC/D,kEAAkE;gBAClE,IAAI,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,EAAE;oBAC3C,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;oBACrB,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;oBACtB,MAAM,CAAC,OAAO,GAAG;wBACf;4BACE,SAAS,EAAE,aAAa;4BACxB,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,MAAM,EAAE,KAAK,CAAC,MAAM;4BACpB,UAAU,EAAE,KAAK,CAAC,UAAU;4BAC5B,SAAS,EAAE,KAAK,CAAC,SAAS;4BAC1B,eAAe,EAAE,KAAK,CAAC,SAAS,GAAG,KAAK;yBACf;qBAC5B,CAAC;oBACF,MAAM,EAAE,CAAC;iBACV;YACH,CAAC,CAAC,CAAC;SACJ;KACF;AACH,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { getFirstHidden } from './lib/getFirstHidden';\nimport { initMetric } from './lib/initMetric';\nimport { observe, PerformanceEntryHandler } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport { ReportHandler } from './types';\n\ninterface FIDPolyfillCallback {\n  (value: number, event: Event): void;\n}\n\ninterface FIDPolyfill {\n  onFirstInputDelay: (onReport: FIDPolyfillCallback) => void;\n}\n\ndeclare global {\n  interface Window {\n    perfMetrics: FIDPolyfill;\n  }\n}\n\n// https://wicg.github.io/event-timing/#sec-performance-event-timing\ninterface PerformanceEventTiming extends PerformanceEntry {\n  processingStart: DOMHighResTimeStamp;\n  cancelable?: boolean;\n  target?: Element;\n}\n\nexport const getFID = (onReport: ReportHandler): void => {\n  const metric = initMetric('FID');\n  const firstHidden = getFirstHidden();\n\n  const entryHandler = (entry: PerformanceEventTiming): void => {\n    // Only report if the page wasn't hidden prior to the first input.\n    if (entry.startTime < firstHidden.timeStamp) {\n      metric.value = entry.processingStart - entry.startTime;\n      metric.entries.push(entry);\n      metric.isFinal = true;\n      report();\n    }\n  };\n\n  const po = observe('first-input', entryHandler as PerformanceEntryHandler);\n  const report = bindReporter(onReport, metric, po);\n\n  if (po) {\n    onHidden(() => {\n      po.takeRecords().map(entryHandler as PerformanceEntryHandler);\n      po.disconnect();\n    }, true);\n  } else {\n    if (window.perfMetrics && window.perfMetrics.onFirstInputDelay) {\n      window.perfMetrics.onFirstInputDelay((value: number, event: Event) => {\n        // Only report if the page wasn't hidden prior to the first input.\n        if (event.timeStamp < firstHidden.timeStamp) {\n          metric.value = value;\n          metric.isFinal = true;\n          metric.entries = [\n            {\n              entryType: 'first-input',\n              name: event.type,\n              target: event.target,\n              cancelable: event.cancelable,\n              startTime: event.timeStamp,\n              processingStart: event.timeStamp + value,\n            } as PerformanceEventTiming,\n          ];\n          report();\n        }\n      });\n    }\n  }\n};\n"]}