{"version": 3, "file": "browsertracing.js", "sourceRoot": "", "sources": ["../../src/browser/browsertracing.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAEvC,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AACxD,OAAO,EAAE,oBAAoB,EAAmB,MAAM,oBAAoB,CAAC;AAC3E,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,sBAAsB,EAAE,OAAO,EAAE,MAAM,UAAU,CAAC;AAC3D,OAAO,EAAE,8BAA8B,EAAE,MAAM,iBAAiB,CAAC;AACjE,OAAO,EAAE,sBAAsB,EAAE,MAAM,WAAW,CAAC;AACnD,OAAO,EACL,oCAAoC,EACpC,8BAA8B,GAE/B,MAAM,WAAW,CAAC;AACnB,OAAO,EAAE,6BAA6B,EAAE,MAAM,UAAU,CAAC;AAEzD,MAAM,CAAC,IAAM,wCAAwC,GAAG,GAAG,CAAC;AAoE5D,IAAM,+BAA+B,cACnC,WAAW,EAAE,oBAAoB,EACjC,0BAA0B,EAAE,IAAI,EAChC,sBAAsB,EAAE,wCAAwC,EAChE,sBAAsB,EAAE,6BAA6B,EACrD,gCAAgC,EAAE,IAAI,EACtC,0BAA0B,EAAE,IAAI,IAC7B,oCAAoC,CACxC,CAAC;AAEF;;;;;;GAMG;AACH;IAoBE,wBAAmB,QAAyC;QAX5D;;WAEG;QACI,SAAI,GAAW,cAAc,CAAC,EAAE,CAAC;QAIvB,aAAQ,GAA2B,IAAI,sBAAsB,EAAE,CAAC;QAEhE,wBAAmB,GAAY,KAAK,CAAC;QAGpD,IAAI,cAAc,GAAG,oCAAoC,CAAC,cAAc,CAAC;QACzE,8FAA8F;QAC9F,IACE,QAAQ;YACR,QAAQ,CAAC,cAAc;YACvB,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC;YACtC,QAAQ,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EACpC;YACA,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC;SAC1C;aAAM;YACL,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;QAED,IAAI,CAAC,OAAO,kCACP,+BAA+B,GAC/B,QAAQ,KACX,cAAc,gBAAA,GACf,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,kCAAS,GAAhB,UAAiB,CAAqC,EAAE,aAAwB;QAAhF,iBAmCC;QAlCC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,MAAM,CAAC,IAAI,CACT,0GAA0G,CAC3G,CAAC;YACF,MAAM,CAAC,IAAI,CACT,sDAAoD,oCAAoC,CAAC,cAAgB,CAC1G,CAAC;SACH;QAED,6DAA6D;QACvD,IAAA,iBASU,EARd,kDAAsB,EACtB,sEAAgC,EAChC,0DAA0B,EAC1B,0DAA0B,EAC1B,0BAAU,EACV,sBAAQ,EACR,kCAAc,EACd,0DACc,CAAC;QAEjB,sBAAsB,CACpB,UAAC,OAA2B,IAAK,OAAA,KAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAArC,CAAqC,EACtE,0BAA0B,EAC1B,gCAAgC,CACjC,CAAC;QAEF,IAAI,0BAA0B,EAAE;YAC9B,8BAA8B,EAAE,CAAC;SAClC;QAED,8BAA8B,CAAC,EAAE,UAAU,YAAA,EAAE,QAAQ,UAAA,EAAE,cAAc,gBAAA,EAAE,0BAA0B,4BAAA,EAAE,CAAC,CAAC;IACvG,CAAC;IAED,uCAAuC;IAC/B,gDAAuB,GAA/B,UAAgC,OAA2B;QAA3D,iBAmCC;QAlCC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,MAAM,CAAC,IAAI,CAAC,8BAA4B,OAAO,CAAC,EAAE,oDAAiD,CAAC,CAAC;YACrG,OAAO,SAAS,CAAC;SAClB;QAED,6DAA6D;QACvD,IAAA,iBAAsE,EAApE,kCAAc,EAAE,4BAAW,EAAE,kDAAuC,CAAC;QAE7E,IAAM,uBAAuB,GAAG,OAAO,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAE3F,IAAM,eAAe,kCAChB,OAAO,GACP,uBAAuB,KAC1B,OAAO,EAAE,IAAI,GACd,CAAC;QACF,IAAM,eAAe,GAAG,OAAO,cAAc,KAAK,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC;QAEjH,iHAAiH;QACjH,8BAA8B;QAC9B,IAAM,YAAY,GAAG,eAAe,KAAK,SAAS,CAAC,CAAC,uBAAM,eAAe,KAAE,OAAO,EAAE,KAAK,IAAG,CAAC,CAAC,eAAe,CAAC;QAE9G,IAAI,YAAY,CAAC,OAAO,KAAK,KAAK,EAAE;YAClC,MAAM,CAAC,GAAG,CAAC,6BAA2B,YAAY,CAAC,EAAE,4CAAyC,CAAC,CAAC;SACjG;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAClC,IAAM,eAAe,GAAG,oBAAoB,CAAC,GAAG,EAAE,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QACnF,MAAM,CAAC,GAAG,CAAC,wBAAsB,YAAY,CAAC,EAAE,0BAAuB,CAAC,CAAC;QACzE,eAAe,CAAC,4BAA4B,CAAC,UAAC,WAAW,EAAE,YAAY;YACrE,KAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;YACjD,yBAAyB,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,OAAO,eAA8B,CAAC;IACxC,CAAC;IApHD;;OAEG;IACW,iBAAE,GAAW,gBAAgB,CAAC;IAkH9C,qBAAC;CAAA,AAtHD,IAsHC;SAtHY,cAAc;AAwH3B;;;;GAIG;AACH,MAAM,UAAU,gBAAgB;IAC9B,IAAM,MAAM,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;IAC9C,IAAI,MAAM,EAAE;QACV,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC;KACvC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,sCAAsC;AACtC,MAAM,UAAU,cAAc,CAAC,QAAgB;IAC7C,IAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,eAAa,QAAQ,MAAG,CAAC,CAAC;IAC5D,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAChD,CAAC;AAED,kEAAkE;AAClE,SAAS,yBAAyB,CAAC,WAAmB,EAAE,WAA4B,EAAE,YAAoB;IACxG,IAAM,IAAI,GAAG,YAAY,GAAG,WAAW,CAAC,cAAc,CAAC;IACvD,IAAM,qBAAqB,GAAG,YAAY,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;IAC/E,IAAI,qBAAqB,EAAE;QACzB,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;QACnD,WAAW,CAAC,MAAM,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAC;KAC9D;AACH,CAAC", "sourcesContent": ["import { Hub } from '@sentry/hub';\nimport { EventProcessor, Integration, Transaction, TransactionContext } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\nimport { startIdleTransaction } from '../hubextensions';\nimport { DEFAULT_IDLE_TIMEOUT, IdleTransaction } from '../idletransaction';\nimport { SpanStatus } from '../spanstatus';\nimport { extractTraceparentData, secToMs } from '../utils';\nimport { registerBackgroundTabDetection } from './backgroundtab';\nimport { MetricsInstrumentation } from './metrics';\nimport {\n  defaultRequestInstrumentationOptions,\n  registerRequestInstrumentation,\n  RequestInstrumentationOptions,\n} from './request';\nimport { defaultRoutingInstrumentation } from './router';\n\nexport const DEFAULT_MAX_TRANSACTION_DURATION_SECONDS = 600;\n\n/** Options for Browser Tracing integration */\nexport interface BrowserTracingOptions extends RequestInstrumentationOptions {\n  /**\n   * The time to wait in ms until the transaction will be finished. The transaction will use the end timestamp of\n   * the last finished span as the endtime for the transaction.\n   * Time is in ms.\n   *\n   * Default: 1000\n   */\n  idleTimeout: number;\n\n  /**\n   * Flag to enable/disable creation of `navigation` transaction on history changes.\n   *\n   * Default: true\n   */\n  startTransactionOnLocationChange: boolean;\n\n  /**\n   * Flag to enable/disable creation of `pageload` transaction on first pageload.\n   *\n   * Default: true\n   */\n  startTransactionOnPageLoad: boolean;\n\n  /**\n   * The maximum duration of a transaction before it will be marked as \"deadline_exceeded\".\n   * If you never want to mark a transaction set it to 0.\n   * Time is in seconds.\n   *\n   * Default: 600\n   */\n  maxTransactionDuration: number;\n\n  /**\n   * Flag Transactions where tabs moved to background with \"cancelled\". Browser background tab timing is\n   * not suited towards doing precise measurements of operations. By default, we recommend that this option\n   * be enabled as background transactions can mess up your statistics in nondeterministic ways.\n   *\n   * Default: true\n   */\n  markBackgroundTransactions: boolean;\n\n  /**\n   * beforeNavigate is called before a pageload/navigation transaction is created and allows users to modify transaction\n   * context data, or drop the transaction entirely (by setting `sampled = false` in the context).\n   *\n   * Note: For legacy reasons, transactions can also be dropped by returning `undefined`.\n   *\n   * @param context: The context data which will be passed to `startTransaction` by default\n   *\n   * @returns A (potentially) modified context object, with `sampled = false` if the transaction should be dropped.\n   */\n  beforeNavigate?(context: TransactionContext): TransactionContext | undefined;\n\n  /**\n   * Instrumentation that creates routing change transactions. By default creates\n   * pageload and navigation transactions.\n   */\n  routingInstrumentation<T extends Transaction>(\n    startTransaction: (context: TransactionContext) => T | undefined,\n    startTransactionOnPageLoad?: boolean,\n    startTransactionOnLocationChange?: boolean,\n  ): void;\n}\n\nconst DEFAULT_BROWSER_TRACING_OPTIONS = {\n  idleTimeout: DEFAULT_IDLE_TIMEOUT,\n  markBackgroundTransactions: true,\n  maxTransactionDuration: DEFAULT_MAX_TRANSACTION_DURATION_SECONDS,\n  routingInstrumentation: defaultRoutingInstrumentation,\n  startTransactionOnLocationChange: true,\n  startTransactionOnPageLoad: true,\n  ...defaultRequestInstrumentationOptions,\n};\n\n/**\n * The Browser Tracing integration automatically instruments browser pageload/navigation\n * actions as transactions, and captures requests, metrics and errors as spans.\n *\n * The integration can be configured with a variety of options, and can be extended to use\n * any routing library. This integration uses {@see IdleTransaction} to create transactions.\n */\nexport class BrowserTracing implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'BrowserTracing';\n\n  /** Browser Tracing integration options */\n  public options: BrowserTracingOptions;\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = BrowserTracing.id;\n\n  private _getCurrentHub?: () => Hub;\n\n  private readonly _metrics: MetricsInstrumentation = new MetricsInstrumentation();\n\n  private readonly _emitOptionsWarning: boolean = false;\n\n  public constructor(_options?: Partial<BrowserTracingOptions>) {\n    let tracingOrigins = defaultRequestInstrumentationOptions.tracingOrigins;\n    // NOTE: Logger doesn't work in constructors, as it's initialized after integrations instances\n    if (\n      _options &&\n      _options.tracingOrigins &&\n      Array.isArray(_options.tracingOrigins) &&\n      _options.tracingOrigins.length !== 0\n    ) {\n      tracingOrigins = _options.tracingOrigins;\n    } else {\n      this._emitOptionsWarning = true;\n    }\n\n    this.options = {\n      ...DEFAULT_BROWSER_TRACING_OPTIONS,\n      ..._options,\n      tracingOrigins,\n    };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(_: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    this._getCurrentHub = getCurrentHub;\n\n    if (this._emitOptionsWarning) {\n      logger.warn(\n        '[Tracing] You need to define `tracingOrigins` in the options. Set an array of urls or patterns to trace.',\n      );\n      logger.warn(\n        `[Tracing] We added a reasonable default for you: ${defaultRequestInstrumentationOptions.tracingOrigins}`,\n      );\n    }\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const {\n      routingInstrumentation,\n      startTransactionOnLocationChange,\n      startTransactionOnPageLoad,\n      markBackgroundTransactions,\n      traceFetch,\n      traceXHR,\n      tracingOrigins,\n      shouldCreateSpanForRequest,\n    } = this.options;\n\n    routingInstrumentation(\n      (context: TransactionContext) => this._createRouteTransaction(context),\n      startTransactionOnPageLoad,\n      startTransactionOnLocationChange,\n    );\n\n    if (markBackgroundTransactions) {\n      registerBackgroundTabDetection();\n    }\n\n    registerRequestInstrumentation({ traceFetch, traceXHR, tracingOrigins, shouldCreateSpanForRequest });\n  }\n\n  /** Create routing idle transaction. */\n  private _createRouteTransaction(context: TransactionContext): Transaction | undefined {\n    if (!this._getCurrentHub) {\n      logger.warn(`[Tracing] Did not create ${context.op} transaction because _getCurrentHub is invalid.`);\n      return undefined;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const { beforeNavigate, idleTimeout, maxTransactionDuration } = this.options;\n\n    const parentContextFromHeader = context.op === 'pageload' ? getHeaderContext() : undefined;\n\n    const expandedContext = {\n      ...context,\n      ...parentContextFromHeader,\n      trimEnd: true,\n    };\n    const modifiedContext = typeof beforeNavigate === 'function' ? beforeNavigate(expandedContext) : expandedContext;\n\n    // For backwards compatibility reasons, beforeNavigate can return undefined to \"drop\" the transaction (prevent it\n    // from being sent to Sentry).\n    const finalContext = modifiedContext === undefined ? { ...expandedContext, sampled: false } : modifiedContext;\n\n    if (finalContext.sampled === false) {\n      logger.log(`[Tracing] Will not send ${finalContext.op} transaction because of beforeNavigate.`);\n    }\n\n    const hub = this._getCurrentHub();\n    const idleTransaction = startIdleTransaction(hub, finalContext, idleTimeout, true);\n    logger.log(`[Tracing] Starting ${finalContext.op} transaction on scope`);\n    idleTransaction.registerBeforeFinishCallback((transaction, endTimestamp) => {\n      this._metrics.addPerformanceEntries(transaction);\n      adjustTransactionDuration(secToMs(maxTransactionDuration), transaction, endTimestamp);\n    });\n\n    return idleTransaction as Transaction;\n  }\n}\n\n/**\n * Gets transaction context from a sentry-trace meta.\n *\n * @returns Transaction context data from the header or undefined if there's no header or the header is malformed\n */\nexport function getHeaderContext(): Partial<TransactionContext> | undefined {\n  const header = getMetaContent('sentry-trace');\n  if (header) {\n    return extractTraceparentData(header);\n  }\n\n  return undefined;\n}\n\n/** Returns the value of a meta tag */\nexport function getMetaContent(metaName: string): string | null {\n  const el = document.querySelector(`meta[name=${metaName}]`);\n  return el ? el.getAttribute('content') : null;\n}\n\n/** Adjusts transaction value based on max transaction duration */\nfunction adjustTransactionDuration(maxDuration: number, transaction: IdleTransaction, endTimestamp: number): void {\n  const diff = endTimestamp - transaction.startTimestamp;\n  const isOutdatedTransaction = endTimestamp && (diff > maxDuration || diff < 0);\n  if (isOutdatedTransaction) {\n    transaction.setStatus(SpanStatus.DeadlineExceeded);\n    transaction.setTag('maxTransactionDurationExceeded', 'true');\n  }\n}\n"]}