/*! @sentry/tracing & @sentry/browser 5.30.0 (6de2dd4) | https://github.com/getsentry/sentry-javascript */
var Sentry=function(t){var n=function(t,r){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var r in n)n.hasOwnProperty(r)&&(t[r]=n[r])})(t,r)};function r(t,r){function i(){this.constructor=t}n(t,r),t.prototype=null===r?Object.create(r):(i.prototype=r.prototype,new i)}var i,e,o,u,a,s=function(){return(s=Object.assign||function(t){for(var n,r=1,i=arguments.length;r<i;r++)for(var e in n=arguments[r])Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e]);return t}).apply(this,arguments)};function c(t,n){var r={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&n.indexOf(i)<0&&(r[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var e=0;for(i=Object.getOwnPropertySymbols(t);e<i.length;e++)n.indexOf(i[e])<0&&(r[i[e]]=t[i[e]])}return r}function f(t){var n="function"==typeof Symbol&&t[Symbol.iterator],r=0;return n?n.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}}function h(t,n){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var i,e,o=r.call(t),u=[];try{for(;(void 0===n||n-- >0)&&!(i=o.next()).done;)u.push(i.value)}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return u}function v(){for(var t=[],n=0;n<arguments.length;n++)t=t.concat(h(arguments[n]));return t}function d(t){switch(Object.prototype.toString.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return _(t,Error)}}function l(t){return"[object ErrorEvent]"===Object.prototype.toString.call(t)}function p(t){return"[object DOMError]"===Object.prototype.toString.call(t)}function m(t){return"[object String]"===Object.prototype.toString.call(t)}function y(t){return null===t||"object"!=typeof t&&"function"!=typeof t}function g(t){return"[object Object]"===Object.prototype.toString.call(t)}function b(t){return"undefined"!=typeof Event&&_(t,Event)}function w(t){return"undefined"!=typeof Element&&_(t,Element)}function T(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function _(t,n){try{return t instanceof n}catch(t){return!1}}function E(t){try{for(var n=t,r=[],i=0,e=0,o=" > ".length,u=void 0;n&&i++<5&&!("html"===(u=S(n))||i>1&&e+r.length*o+u.length>=80);)r.push(u),e+=u.length,n=n.parentNode;return r.reverse().join(" > ")}catch(t){return"<unknown>"}}function S(t){var n,r,i,e,o,u=t,a=[];if(!u||!u.tagName)return"";if(a.push(u.tagName.toLowerCase()),u.id&&a.push("#"+u.id),(n=u.className)&&m(n))for(r=n.split(/\s+/),o=0;o<r.length;o++)a.push("."+r[o]);var s=["type","name","title","alt"];for(o=0;o<s.length;o++)i=s[o],(e=u.getAttribute(i))&&a.push("["+i+'="'+e+'"]');return a.join("")}!function(t){t[t.None=0]="None",t[t.Error=1]="Error",t[t.Debug=2]="Debug",t[t.Verbose=3]="Verbose"}(i||(i={})),function(t){t.Ok="ok",t.Exited="exited",t.Crashed="crashed",t.Abnormal="abnormal"}(e||(e={})),(o=t.Severity||(t.Severity={})).Fatal="fatal",o.Error="error",o.Warning="warning",o.Log="log",o.Info="info",o.Debug="debug",o.Critical="critical",function(t){t.fromString=function(n){switch(n){case"debug":return t.Debug;case"info":return t.Info;case"warn":case"warning":return t.Warning;case"error":return t.Error;case"fatal":return t.Fatal;case"critical":return t.Critical;case"log":default:return t.Log}}}(t.Severity||(t.Severity={})),(u=t.Status||(t.Status={})).Unknown="unknown",u.Skipped="skipped",u.Success="success",u.RateLimit="rate_limit",u.Invalid="invalid",u.Failed="failed",function(t){t.fromHttpCode=function(n){return n>=200&&n<300?t.Success:429===n?t.RateLimit:n>=400&&n<500?t.Invalid:n>=500?t.Failed:t.Unknown}}(t.Status||(t.Status={})),function(t){t.Explicit="explicitly_set",t.Sampler="client_sampler",t.Rate="client_rate",t.Inheritance="inheritance"}(a||(a={}));var k=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,n){return t.__proto__=n,t}:function(t,n){for(var r in n)t.hasOwnProperty(r)||(t[r]=n[r]);return t});var x=function(t){function n(n){var r=this.constructor,i=t.call(this,n)||this;return i.message=n,i.name=r.prototype.constructor.name,k(i,r.prototype),i}return r(n,t),n}(Error),j=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/,O=function(){function t(t){"string"==typeof t?this.t(t):this.i(t),this.o()}return t.prototype.toString=function(t){void 0===t&&(t=!1);var n=this,r=n.host,i=n.path,e=n.pass,o=n.port,u=n.projectId;return n.protocol+"://"+n.user+(t&&e?":"+e:"")+"@"+r+(o?":"+o:"")+"/"+(i?i+"/":i)+u},t.prototype.t=function(t){var n=j.exec(t);if(!n)throw new x("Invalid Dsn");var r=h(n.slice(1),6),i=r[0],e=r[1],o=r[2],u=void 0===o?"":o,a=r[3],s=r[4],c=void 0===s?"":s,f="",v=r[5],d=v.split("/");if(d.length>1&&(f=d.slice(0,-1).join("/"),v=d.pop()),v){var l=v.match(/^\d+/);l&&(v=l[0])}this.i({host:a,pass:u,path:f,projectId:v,port:c,protocol:i,user:e})},t.prototype.i=function(t){this.protocol=t.protocol,this.user=t.user,this.pass=t.pass||"",this.host=t.host,this.port=t.port||"",this.path=t.path||"",this.projectId=t.projectId},t.prototype.o=function(){var t=this;if(["protocol","user","host","projectId"].forEach(function(n){if(!t[n])throw new x("Invalid Dsn: "+n+" missing")}),!this.projectId.match(/^\d+$/))throw new x("Invalid Dsn: Invalid projectId "+this.projectId);if("http"!==this.protocol&&"https"!==this.protocol)throw new x("Invalid Dsn: Invalid protocol "+this.protocol);if(this.port&&isNaN(parseInt(this.port,10)))throw new x("Invalid Dsn: Invalid port "+this.port)},t}(),D=function(){function t(){this.u="function"==typeof WeakSet,this.s=this.u?new WeakSet:[]}return t.prototype.memoize=function(t){if(this.u)return!!this.s.has(t)||(this.s.add(t),!1);for(var n=0;n<this.s.length;n++){if(this.s[n]===t)return!0}return this.s.push(t),!1},t.prototype.unmemoize=function(t){if(this.u)this.s.delete(t);else for(var n=0;n<this.s.length;n++)if(this.s[n]===t){this.s.splice(n,1);break}},t}(),N="<anonymous>";function R(t){try{return t&&"function"==typeof t&&t.name||N}catch(t){return N}}function I(t,n){return void 0===n&&(n=0),"string"!=typeof t||0===n?t:t.length<=n?t:t.substr(0,n)+"..."}function M(t,n){if(!Array.isArray(t))return"";for(var r=[],i=0;i<t.length;i++){var e=t[i];try{r.push(String(e))}catch(t){r.push("[value cannot be serialized]")}}return r.join(n)}function C(t,n){return!!m(t)&&(r=n,"[object RegExp]"===Object.prototype.toString.call(r)?n.test(t):"string"==typeof n&&-1!==t.indexOf(n));var r}function A(t,n,r){if(n in t){var i=t[n],e=r(i);if("function"==typeof e)try{e.prototype=e.prototype||{},Object.defineProperties(e,{__sentry_original__:{enumerable:!1,value:i}})}catch(t){}t[n]=e}}function L(t){if(d(t)){var n=t,r={message:n.message,name:n.name,stack:n.stack};for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(r[i]=n[i]);return r}if(b(t)){var e=t,o={};o.type=e.type;try{o.target=w(e.target)?E(e.target):Object.prototype.toString.call(e.target)}catch(t){o.target="<unknown>"}try{o.currentTarget=w(e.currentTarget)?E(e.currentTarget):Object.prototype.toString.call(e.currentTarget)}catch(t){o.currentTarget="<unknown>"}for(var i in"undefined"!=typeof CustomEvent&&_(t,CustomEvent)&&(o.detail=e.detail),e)Object.prototype.hasOwnProperty.call(e,i)&&(o[i]=e);return o}return t}function q(t){return function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(t))}function F(t,n,r){void 0===n&&(n=3),void 0===r&&(r=102400);var i=H(t,n);return q(i)>r?F(t,n-1,r):i}function U(t,n){return"domain"===n&&t&&"object"==typeof t&&t.h?"[Domain]":"domainEmitter"===n?"[DomainEmitter]":"undefined"!=typeof global&&t===global?"[Global]":"undefined"!=typeof window&&t===window?"[Window]":"undefined"!=typeof document&&t===document?"[Document]":g(r=t)&&"nativeEvent"in r&&"preventDefault"in r&&"stopPropagation"in r?"[SyntheticEvent]":"number"==typeof t&&t!=t?"[NaN]":void 0===t?"[undefined]":"function"==typeof t?"[Function: "+R(t)+"]":"symbol"==typeof t?"["+String(t)+"]":"bigint"==typeof t?"[BigInt: "+String(t)+"]":t;var r}function P(t,n,r,i){if(void 0===r&&(r=1/0),void 0===i&&(i=new D),0===r)return function(t){var n=Object.prototype.toString.call(t);if("string"==typeof t)return t;if("[object Object]"===n)return"[Object]";if("[object Array]"===n)return"[Array]";var r=U(t);return y(r)?r:n}(n);if(null!=n&&"function"==typeof n.toJSON)return n.toJSON();var e=U(n,t);if(y(e))return e;var o=L(n),u=Array.isArray(n)?[]:{};if(i.memoize(n))return"[Circular ~]";for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(u[a]=P(a,o[a],r-1,i));return i.unmemoize(n),u}function H(t,n){try{return JSON.parse(JSON.stringify(t,function(t,r){return P(t,r,n)}))}catch(t){return"**non-serializable**"}}function B(t,n){void 0===n&&(n=40);var r=Object.keys(L(t));if(r.sort(),!r.length)return"[object has no keys]";if(r[0].length>=n)return I(r[0],n);for(var i=r.length;i>0;i--){var e=r.slice(0,i).join(", ");if(!(e.length>n))return i===r.length?e:I(e,n)}return""}function J(t){var n,r;if(g(t)){var i=t,e={};try{for(var o=f(Object.keys(i)),u=o.next();!u.done;u=o.next()){var a=u.value;void 0!==i[a]&&(e[a]=J(i[a]))}}catch(t){n={error:t}}finally{try{u&&!u.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return e}return Array.isArray(t)?t.map(J):t}function X(){return"[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0)}function G(t,n){return t.require(n)}var W=["cookies","data","headers","method","query_string","url"];var $={};function z(){return X()?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:$}function V(){var t=z(),n=t.crypto||t.msCrypto;if(void 0!==n&&n.getRandomValues){var r=new Uint16Array(8);n.getRandomValues(r),r[3]=4095&r[3]|16384,r[4]=16383&r[4]|32768;var i=function(t){for(var n=t.toString(16);n.length<4;)n="0"+n;return n};return i(r[0])+i(r[1])+i(r[2])+i(r[3])+i(r[4])+i(r[5])+i(r[6])+i(r[7])}return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,function(t){var n=16*Math.random()|0;return("x"===t?n:3&n|8).toString(16)})}function K(t){if(!t)return{};var n=t.match(/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!n)return{};var r=n[6]||"",i=n[8]||"";return{host:n[4],path:n[5],protocol:n[2],relative:n[5]+r+i}}function Y(t){if(t.message)return t.message;if(t.exception&&t.exception.values&&t.exception.values[0]){var n=t.exception.values[0];return n.type&&n.value?n.type+": "+n.value:n.type||n.value||t.event_id||"<unknown>"}return t.event_id||"<unknown>"}function Q(t){var n=z();if(!("console"in n))return t();var r=n.console,i={};["debug","info","warn","error","log","assert"].forEach(function(t){t in n.console&&r[t].__sentry_original__&&(i[t]=r[t],r[t]=r[t].__sentry_original__)});var e=t();return Object.keys(i).forEach(function(t){r[t]=i[t]}),e}function Z(t,n,r){t.exception=t.exception||{},t.exception.values=t.exception.values||[],t.exception.values[0]=t.exception.values[0]||{},t.exception.values[0].value=t.exception.values[0].value||n||"",t.exception.values[0].type=t.exception.values[0].type||r||"Error"}function tt(t,n){void 0===n&&(n={});try{t.exception.values[0].mechanism=t.exception.values[0].mechanism||{},Object.keys(n).forEach(function(r){t.exception.values[0].mechanism[r]=n[r]})}catch(t){}}var nt=6e4;var rt=z(),it="Sentry Logger ",et=function(){function t(){this.v=!1}return t.prototype.disable=function(){this.v=!1},t.prototype.enable=function(){this.v=!0},t.prototype.log=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.v&&Q(function(){rt.console.log(it+"[Log]: "+t.join(" "))})},t.prototype.warn=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.v&&Q(function(){rt.console.warn(it+"[Warn]: "+t.join(" "))})},t.prototype.error=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.v&&Q(function(){rt.console.error(it+"[Error]: "+t.join(" "))})},t}();rt.__SENTRY__=rt.__SENTRY__||{};var ot=rt.__SENTRY__.logger||(rt.__SENTRY__.logger=new et);function ut(){if(!("fetch"in z()))return!1;try{return new Headers,new Request(""),new Response,!0}catch(t){return!1}}function at(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function st(){if(!ut())return!1;try{return new Request("_",{referrerPolicy:"origin"}),!0}catch(t){return!1}}var ct,ft=z(),ht={},vt={};function dt(t){if(!vt[t])switch(vt[t]=!0,t){case"console":!function(){if(!("console"in ft))return;["debug","info","warn","error","log","assert"].forEach(function(t){t in ft.console&&A(ft.console,t,function(n){return function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];pt("console",{args:r,level:t}),n&&Function.prototype.apply.call(n,ft.console,r)}})})}();break;case"dom":!function(){if(!("document"in ft))return;ft.document.addEventListener("click",_t("click",pt.bind(null,"dom")),!1),ft.document.addEventListener("keypress",Et(pt.bind(null,"dom")),!1),["EventTarget","Node"].forEach(function(t){var n=ft[t]&&ft[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(A(n,"addEventListener",function(t){return function(n,r,i){return r&&r.handleEvent?("click"===n&&A(r,"handleEvent",function(t){return function(n){return _t("click",pt.bind(null,"dom"))(n),t.call(this,n)}}),"keypress"===n&&A(r,"handleEvent",function(t){return function(n){return Et(pt.bind(null,"dom"))(n),t.call(this,n)}})):("click"===n&&_t("click",pt.bind(null,"dom"),!0)(this),"keypress"===n&&Et(pt.bind(null,"dom"))(this)),t.call(this,n,r,i)}}),A(n,"removeEventListener",function(t){return function(n,r,i){try{t.call(this,n,r.__sentry_wrapped__,i)}catch(t){}return t.call(this,n,r,i)}}))})}();break;case"xhr":!function(){if(!("XMLHttpRequest"in ft))return;var t=[],n=[],r=XMLHttpRequest.prototype;A(r,"open",function(r){return function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];var o=this,u=i[1];o.__sentry_xhr__={method:m(i[0])?i[0].toUpperCase():i[0],url:i[1]},m(u)&&"POST"===o.__sentry_xhr__.method&&u.match(/sentry_key/)&&(o.__sentry_own_request__=!0);var a=function(){if(4===o.readyState){try{o.__sentry_xhr__&&(o.__sentry_xhr__.status_code=o.status)}catch(t){}try{var r=t.indexOf(o);if(-1!==r){t.splice(r);var e=n.splice(r)[0];o.__sentry_xhr__&&void 0!==e[0]&&(o.__sentry_xhr__.body=e[0])}}catch(t){}pt("xhr",{args:i,endTimestamp:Date.now(),startTimestamp:Date.now(),xhr:o})}};return"onreadystatechange"in o&&"function"==typeof o.onreadystatechange?A(o,"onreadystatechange",function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return a(),t.apply(o,n)}}):o.addEventListener("readystatechange",a),r.apply(o,i)}}),A(r,"send",function(r){return function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];return t.push(this),n.push(i),pt("xhr",{args:i,startTimestamp:Date.now(),xhr:this}),r.apply(this,i)}})}();break;case"fetch":!function(){if(!function(){if(!ut())return!1;var t=z();if(at(t.fetch))return!0;var n=!1,r=t.document;if(r&&"function"==typeof r.createElement)try{var i=r.createElement("iframe");i.hidden=!0,r.head.appendChild(i),i.contentWindow&&i.contentWindow.fetch&&(n=at(i.contentWindow.fetch)),r.head.removeChild(i)}catch(t){ot.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",t)}return n}())return;A(ft,"fetch",function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i={args:n,fetchData:{method:mt(n),url:yt(n)},startTimestamp:Date.now()};return pt("fetch",s({},i)),t.apply(ft,n).then(function(t){return pt("fetch",s(s({},i),{endTimestamp:Date.now(),response:t})),t},function(t){throw pt("fetch",s(s({},i),{endTimestamp:Date.now(),error:t})),t})}})}();break;case"history":!function(){if(t=z(),n=t.chrome,r=n&&n.app&&n.app.runtime,i="history"in t&&!!t.history.pushState&&!!t.history.replaceState,r||!i)return;var t,n,r,i;var e=ft.onpopstate;function o(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=n.length>2?n[2]:void 0;if(i){var e=ct,o=String(i);ct=o,pt("history",{from:e,to:o})}return t.apply(this,n)}}ft.onpopstate=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=ft.location.href,i=ct;if(ct=r,pt("history",{from:i,to:r}),e)return e.apply(this,t)},A(ft.history,"pushState",o),A(ft.history,"replaceState",o)}();break;case"error":St=ft.onerror,ft.onerror=function(t,n,r,i,e){return pt("error",{column:i,error:e,line:r,msg:t,url:n}),!!St&&St.apply(this,arguments)};break;case"unhandledrejection":xt=ft.onunhandledrejection,ft.onunhandledrejection=function(t){return pt("unhandledrejection",t),!xt||xt.apply(this,arguments)};break;default:ot.warn("unknown instrumentation type:",t)}}function lt(t){t&&"string"==typeof t.type&&"function"==typeof t.callback&&(ht[t.type]=ht[t.type]||[],ht[t.type].push(t.callback),dt(t.type))}function pt(t,n){var r,i;if(t&&ht[t])try{for(var e=f(ht[t]||[]),o=e.next();!o.done;o=e.next()){var u=o.value;try{u(n)}catch(n){ot.error("Error while triggering instrumentation handler.\nType: "+t+"\nName: "+R(u)+"\nError: "+n)}}}catch(t){r={error:t}}finally{try{o&&!o.done&&(i=e.return)&&i.call(e)}finally{if(r)throw r.error}}}function mt(t){return void 0===t&&(t=[]),"Request"in ft&&_(t[0],Request)&&t[0].method?String(t[0].method).toUpperCase():t[1]&&t[1].method?String(t[1].method).toUpperCase():"GET"}function yt(t){return void 0===t&&(t=[]),"string"==typeof t[0]?t[0]:"Request"in ft&&_(t[0],Request)?t[0].url:String(t[0])}var gt,bt,wt=1e3,Tt=0;function _t(t,n,r){return void 0===r&&(r=!1),function(i){gt=void 0,i&&bt!==i&&(bt=i,Tt&&clearTimeout(Tt),r?Tt=setTimeout(function(){n({event:i,name:t})}):n({event:i,name:t}))}}function Et(t){return function(n){var r;try{r=n.target}catch(t){return}var i=r&&r.tagName;i&&("INPUT"===i||"TEXTAREA"===i||r.isContentEditable)&&(gt||_t("input",t)(n),clearTimeout(gt),gt=setTimeout(function(){gt=void 0},wt))}}var St=null;var kt,xt=null;!function(t){t.PENDING="PENDING",t.RESOLVED="RESOLVED",t.REJECTED="REJECTED"}(kt||(kt={}));var jt=function(){function t(t){var n=this;this.l=kt.PENDING,this.p=[],this.m=function(t){n.g(kt.RESOLVED,t)},this.T=function(t){n.g(kt.REJECTED,t)},this.g=function(t,r){n.l===kt.PENDING&&(T(r)?r.then(n.m,n.T):(n.l=t,n._=r,n.S()))},this.k=function(t){n.p=n.p.concat(t),n.S()},this.S=function(){if(n.l!==kt.PENDING){var t=n.p.slice();n.p=[],t.forEach(function(t){t.done||(n.l===kt.RESOLVED&&t.onfulfilled&&t.onfulfilled(n._),n.l===kt.REJECTED&&t.onrejected&&t.onrejected(n._),t.done=!0)})}};try{t(this.m,this.T)}catch(t){this.T(t)}}return t.resolve=function(n){return new t(function(t){t(n)})},t.reject=function(n){return new t(function(t,r){r(n)})},t.all=function(n){return new t(function(r,i){if(Array.isArray(n))if(0!==n.length){var e=n.length,o=[];n.forEach(function(n,u){t.resolve(n).then(function(t){o[u]=t,0===(e-=1)&&r(o)}).then(null,i)})}else r([]);else i(new TypeError("Promise.all requires an array as input."))})},t.prototype.then=function(n,r){var i=this;return new t(function(t,e){i.k({done:!1,onfulfilled:function(r){if(n)try{return void t(n(r))}catch(t){return void e(t)}else t(r)},onrejected:function(n){if(r)try{return void t(r(n))}catch(t){return void e(t)}else e(n)}})})},t.prototype.catch=function(t){return this.then(function(t){return t},t)},t.prototype.finally=function(n){var r=this;return new t(function(t,i){var e,o;return r.then(function(t){o=!1,e=t,n&&n()},function(t){o=!0,e=t,n&&n()}).then(function(){o?i(e):t(e)})})},t.prototype.toString=function(){return"[object SyncPromise]"},t}(),Ot=function(){function t(t){this.j=t,this.O=[]}return t.prototype.isReady=function(){return void 0===this.j||this.length()<this.j},t.prototype.add=function(t){var n=this;return this.isReady()?(-1===this.O.indexOf(t)&&this.O.push(t),t.then(function(){return n.remove(t)}).then(null,function(){return n.remove(t).then(null,function(){})}),t):jt.reject(new x("Not adding Promise due to buffer limit reached."))},t.prototype.remove=function(t){return this.O.splice(this.O.indexOf(t),1)[0]},t.prototype.length=function(){return this.O.length},t.prototype.drain=function(t){var n=this;return new jt(function(r){var i=setTimeout(function(){t&&t>0&&r(!1)},t);jt.all(n.O).then(function(){clearTimeout(i),r(!0)}).then(null,function(){r(!0)})})},t}(),Dt={nowSeconds:function(){return Date.now()/1e3}};var Nt=X()?function(){try{return G(module,"perf_hooks").performance}catch(t){return}}():function(){var t=z().performance;if(t&&t.now)return{now:function(){return t.now()},timeOrigin:Date.now()-t.now()}}(),Rt=void 0===Nt?Dt:{nowSeconds:function(){return(Nt.timeOrigin+Nt.now())/1e3}},It=Dt.nowSeconds.bind(Dt),Mt=Rt.nowSeconds.bind(Rt),Ct=function(){var t=z().performance;if(t)return t.timeOrigin?t.timeOrigin:t.timing&&t.timing.navigationStart||Date.now()}(),At=function(){function t(){this.D=!1,this.N=[],this.R=[],this.I=[],this.M={},this.C={},this.A={},this.L={}}return t.clone=function(n){var r=new t;return n&&(r.I=v(n.I),r.C=s({},n.C),r.A=s({},n.A),r.L=s({},n.L),r.M=n.M,r.q=n.q,r.F=n.F,r.U=n.U,r.P=n.P,r.H=n.H,r.R=v(n.R)),r},t.prototype.addScopeListener=function(t){this.N.push(t)},t.prototype.addEventProcessor=function(t){return this.R.push(t),this},t.prototype.setUser=function(t){return this.M=t||{},this.U&&this.U.update({user:t}),this.B(),this},t.prototype.getUser=function(){return this.M},t.prototype.setTags=function(t){return this.C=s(s({},this.C),t),this.B(),this},t.prototype.setTag=function(t,n){var r;return this.C=s(s({},this.C),((r={})[t]=n,r)),this.B(),this},t.prototype.setExtras=function(t){return this.A=s(s({},this.A),t),this.B(),this},t.prototype.setExtra=function(t,n){var r;return this.A=s(s({},this.A),((r={})[t]=n,r)),this.B(),this},t.prototype.setFingerprint=function(t){return this.H=t,this.B(),this},t.prototype.setLevel=function(t){return this.q=t,this.B(),this},t.prototype.setTransactionName=function(t){return this.P=t,this.B(),this},t.prototype.setTransaction=function(t){return this.setTransactionName(t)},t.prototype.setContext=function(t,n){var r;return null===n?delete this.L[t]:this.L=s(s({},this.L),((r={})[t]=n,r)),this.B(),this},t.prototype.setSpan=function(t){return this.F=t,this.B(),this},t.prototype.getSpan=function(){return this.F},t.prototype.getTransaction=function(){var t,n,r,i,e=this.getSpan();return(null===(t=e)||void 0===t?void 0:t.transaction)?null===(n=e)||void 0===n?void 0:n.transaction:(null===(i=null===(r=e)||void 0===r?void 0:r.spanRecorder)||void 0===i?void 0:i.spans[0])?e.spanRecorder.spans[0]:void 0},t.prototype.setSession=function(t){return t?this.U=t:delete this.U,this.B(),this},t.prototype.getSession=function(){return this.U},t.prototype.update=function(n){if(!n)return this;if("function"==typeof n){var r=n(this);return r instanceof t?r:this}return n instanceof t?(this.C=s(s({},this.C),n.C),this.A=s(s({},this.A),n.A),this.L=s(s({},this.L),n.L),n.M&&Object.keys(n.M).length&&(this.M=n.M),n.q&&(this.q=n.q),n.H&&(this.H=n.H)):g(n)&&(n=n,this.C=s(s({},this.C),n.tags),this.A=s(s({},this.A),n.extra),this.L=s(s({},this.L),n.contexts),n.user&&(this.M=n.user),n.level&&(this.q=n.level),n.fingerprint&&(this.H=n.fingerprint)),this},t.prototype.clear=function(){return this.I=[],this.C={},this.A={},this.M={},this.L={},this.q=void 0,this.P=void 0,this.H=void 0,this.F=void 0,this.U=void 0,this.B(),this},t.prototype.addBreadcrumb=function(t,n){var r=s({timestamp:It()},t);return this.I=void 0!==n&&n>=0?v(this.I,[r]).slice(-n):v(this.I,[r]),this.B(),this},t.prototype.clearBreadcrumbs=function(){return this.I=[],this.B(),this},t.prototype.applyToEvent=function(t,n){var r;if(this.A&&Object.keys(this.A).length&&(t.extra=s(s({},this.A),t.extra)),this.C&&Object.keys(this.C).length&&(t.tags=s(s({},this.C),t.tags)),this.M&&Object.keys(this.M).length&&(t.user=s(s({},this.M),t.user)),this.L&&Object.keys(this.L).length&&(t.contexts=s(s({},this.L),t.contexts)),this.q&&(t.level=this.q),this.P&&(t.transaction=this.P),this.F){t.contexts=s({trace:this.F.getTraceContext()},t.contexts);var i=null===(r=this.F.transaction)||void 0===r?void 0:r.name;i&&(t.tags=s({transaction:i},t.tags))}return this.J(t),t.breadcrumbs=v(t.breadcrumbs||[],this.I),t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,this.X(v(Lt(),this.R),t,n)},t.prototype.X=function(t,n,r,i){var e=this;return void 0===i&&(i=0),new jt(function(o,u){var a=t[i];if(null===n||"function"!=typeof a)o(n);else{var c=a(s({},n),r);T(c)?c.then(function(n){return e.X(t,n,r,i+1).then(o)}).then(null,u):e.X(t,c,r,i+1).then(o).then(null,u)}})},t.prototype.B=function(){var t=this;this.D||(this.D=!0,this.N.forEach(function(n){n(t)}),this.D=!1)},t.prototype.J=function(t){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],this.H&&(t.fingerprint=t.fingerprint.concat(this.H)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint},t}();function Lt(){var t=z();return t.__SENTRY__=t.__SENTRY__||{},t.__SENTRY__.globalEventProcessors=t.__SENTRY__.globalEventProcessors||[],t.__SENTRY__.globalEventProcessors}function qt(t){Lt().push(t)}var Ft=function(){function t(t){this.errors=0,this.sid=V(),this.timestamp=Date.now(),this.started=Date.now(),this.duration=0,this.status=e.Ok,t&&this.update(t)}return t.prototype.update=function(t){void 0===t&&(t={}),t.user&&(t.user.ip_address&&(this.ipAddress=t.user.ip_address),t.did||(this.did=t.user.id||t.user.email||t.user.username)),this.timestamp=t.timestamp||Date.now(),t.sid&&(this.sid=32===t.sid.length?t.sid:V()),t.did&&(this.did=""+t.did),"number"==typeof t.started&&(this.started=t.started),"number"==typeof t.duration?this.duration=t.duration:this.duration=this.timestamp-this.started,t.release&&(this.release=t.release),t.environment&&(this.environment=t.environment),t.ipAddress&&(this.ipAddress=t.ipAddress),t.userAgent&&(this.userAgent=t.userAgent),"number"==typeof t.errors&&(this.errors=t.errors),t.status&&(this.status=t.status)},t.prototype.close=function(t){t?this.update({status:t}):this.status===e.Ok?this.update({status:e.Exited}):this.update()},t.prototype.toJSON=function(){return J({sid:""+this.sid,init:!0,started:new Date(this.started).toISOString(),timestamp:new Date(this.timestamp).toISOString(),status:this.status,errors:this.errors,did:"number"==typeof this.did||"string"==typeof this.did?""+this.did:void 0,duration:this.duration,attrs:J({release:this.release,environment:this.environment,ip_address:this.ipAddress,user_agent:this.userAgent})})},t}(),Ut=3,Pt=function(){function t(t,n,r){void 0===n&&(n=new At),void 0===r&&(r=Ut),this.G=r,this.W=[{}],this.getStackTop().scope=n,this.bindClient(t)}return t.prototype.isOlderThan=function(t){return this.G<t},t.prototype.bindClient=function(t){this.getStackTop().client=t,t&&t.setupIntegrations&&t.setupIntegrations()},t.prototype.pushScope=function(){var t=At.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t},t.prototype.popScope=function(){return!(this.getStack().length<=1)&&!!this.getStack().pop()},t.prototype.withScope=function(t){var n=this.pushScope();try{t(n)}finally{this.popScope()}},t.prototype.getClient=function(){return this.getStackTop().client},t.prototype.getScope=function(){return this.getStackTop().scope},t.prototype.getStack=function(){return this.W},t.prototype.getStackTop=function(){return this.W[this.W.length-1]},t.prototype.captureException=function(t,n){var r=this.$=V(),i=n;if(!n){var e=void 0;try{throw new Error("Sentry syntheticException")}catch(t){e=t}i={originalException:t,syntheticException:e}}return this.V("captureException",t,s(s({},i),{event_id:r})),r},t.prototype.captureMessage=function(t,n,r){var i=this.$=V(),e=r;if(!r){var o=void 0;try{throw new Error(t)}catch(t){o=t}e={originalException:t,syntheticException:o}}return this.V("captureMessage",t,n,s(s({},e),{event_id:i})),i},t.prototype.captureEvent=function(t,n){var r=this.$=V();return this.V("captureEvent",t,s(s({},n),{event_id:r})),r},t.prototype.lastEventId=function(){return this.$},t.prototype.addBreadcrumb=function(t,n){var r=this.getStackTop(),i=r.scope,e=r.client;if(i&&e){var o=e.getOptions&&e.getOptions()||{},u=o.beforeBreadcrumb,a=void 0===u?null:u,c=o.maxBreadcrumbs,f=void 0===c?100:c;if(!(f<=0)){var h=It(),v=s({timestamp:h},t),d=a?Q(function(){return a(v,n)}):v;null!==d&&i.addBreadcrumb(d,Math.min(f,100))}}},t.prototype.setUser=function(t){var n=this.getScope();n&&n.setUser(t)},t.prototype.setTags=function(t){var n=this.getScope();n&&n.setTags(t)},t.prototype.setExtras=function(t){var n=this.getScope();n&&n.setExtras(t)},t.prototype.setTag=function(t,n){var r=this.getScope();r&&r.setTag(t,n)},t.prototype.setExtra=function(t,n){var r=this.getScope();r&&r.setExtra(t,n)},t.prototype.setContext=function(t,n){var r=this.getScope();r&&r.setContext(t,n)},t.prototype.configureScope=function(t){var n=this.getStackTop(),r=n.scope,i=n.client;r&&i&&t(r)},t.prototype.run=function(t){var n=Bt(this);try{t(this)}finally{Bt(n)}},t.prototype.getIntegration=function(t){var n=this.getClient();if(!n)return null;try{return n.getIntegration(t)}catch(n){return ot.warn("Cannot retrieve integration "+t.id+" from the current Hub"),null}},t.prototype.startSpan=function(t){return this.K("startSpan",t)},t.prototype.startTransaction=function(t,n){return this.K("startTransaction",t,n)},t.prototype.traceHeaders=function(){return this.K("traceHeaders")},t.prototype.startSession=function(t){this.endSession();var n=this.getStackTop(),r=n.scope,i=n.client,e=i&&i.getOptions()||{},o=e.release,u=e.environment,a=new Ft(s(s({release:o,environment:u},r&&{user:r.getUser()}),t));return r&&r.setSession(a),a},t.prototype.endSession=function(){var t=this.getStackTop(),n=t.scope,r=t.client;if(n){var i=n.getSession&&n.getSession();i&&(i.close(),r&&r.captureSession&&r.captureSession(i),n.setSession())}},t.prototype.V=function(t){for(var n,r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];var e=this.getStackTop(),o=e.scope,u=e.client;u&&u[t]&&(n=u)[t].apply(n,v(r,[o]))},t.prototype.K=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var i=Ht().__SENTRY__;if(i&&i.extensions&&"function"==typeof i.extensions[t])return i.extensions[t].apply(this,n);ot.warn("Extension method "+t+" couldn't be found, doing nothing.")},t}();function Ht(){var t=z();return t.__SENTRY__=t.__SENTRY__||{extensions:{},hub:void 0},t}function Bt(t){var n=Ht(),r=Wt(n);return $t(n,t),r}function Jt(){var t=Ht();return Gt(t)&&!Wt(t).isOlderThan(Ut)||$t(t,new Pt),X()?function(t){try{var n=Xt();if(!n)return Wt(t);if(!Gt(n)||Wt(n).isOlderThan(Ut)){var r=Wt(t).getStackTop();$t(n,new Pt(r.client,At.clone(r.scope)))}return Wt(n)}catch(n){return Wt(t)}}(t):Wt(t)}function Xt(){var t=Ht().__SENTRY__;return t&&t.extensions&&t.extensions.domain&&t.extensions.domain.active}function Gt(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function Wt(t){return t&&t.__SENTRY__&&t.__SENTRY__.hub?t.__SENTRY__.hub:(t.__SENTRY__=t.__SENTRY__||{},t.__SENTRY__.hub=new Pt,t.__SENTRY__.hub)}function $t(t,n){return!!t&&(t.__SENTRY__=t.__SENTRY__||{},t.__SENTRY__.hub=n,!0)}function zt(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var i=Jt();if(i&&i[t])return i[t].apply(i,v(n));throw new Error("No hub defined or "+t+" was not found on the hub, please open a bug report.")}function captureException(t,n){var r;try{throw new Error("Sentry syntheticException")}catch(t){r=t}return zt("captureException",t,{captureContext:n,originalException:t,syntheticException:r})}function Vt(t){zt("withScope",t)}var Kt=function(){function t(t){this.dsn=t,this.Y=new O(t)}return t.prototype.getDsn=function(){return this.Y},t.prototype.getBaseApiEndpoint=function(){var t=this.Y,n=t.protocol?t.protocol+":":"",r=t.port?":"+t.port:"";return n+"//"+t.host+r+(t.path?"/"+t.path:"")+"/api/"},t.prototype.getStoreEndpoint=function(){return this.Z("store")},t.prototype.getStoreEndpointWithUrlEncodedAuth=function(){return this.getStoreEndpoint()+"?"+this.tt()},t.prototype.getEnvelopeEndpointWithUrlEncodedAuth=function(){return this.nt()+"?"+this.tt()},t.prototype.getStoreEndpointPath=function(){var t=this.Y;return(t.path?"/"+t.path:"")+"/api/"+t.projectId+"/store/"},t.prototype.getRequestHeaders=function(t,n){var r=this.Y,i=["Sentry sentry_version=7"];return i.push("sentry_client="+t+"/"+n),i.push("sentry_key="+r.user),r.pass&&i.push("sentry_secret="+r.pass),{"Content-Type":"application/json","X-Sentry-Auth":i.join(", ")}},t.prototype.getReportDialogEndpoint=function(t){void 0===t&&(t={});var n=this.Y,r=this.getBaseApiEndpoint()+"embed/error-page/",i=[];for(var e in i.push("dsn="+n.toString()),t)if("dsn"!==e)if("user"===e){if(!t.user)continue;t.user.name&&i.push("name="+encodeURIComponent(t.user.name)),t.user.email&&i.push("email="+encodeURIComponent(t.user.email))}else i.push(encodeURIComponent(e)+"="+encodeURIComponent(t[e]));return i.length?r+"?"+i.join("&"):r},t.prototype.nt=function(){return this.Z("envelope")},t.prototype.Z=function(t){return""+this.getBaseApiEndpoint()+this.Y.projectId+"/"+t+"/"},t.prototype.tt=function(){var t,n={sentry_key:this.Y.user,sentry_version:"7"};return t=n,Object.keys(t).map(function(n){return encodeURIComponent(n)+"="+encodeURIComponent(t[n])}).join("&")},t}(),Yt=[];function Qt(t){var n={};return function(t){var n=t.defaultIntegrations&&v(t.defaultIntegrations)||[],r=t.integrations,i=[];if(Array.isArray(r)){var e=r.map(function(t){return t.name}),o=[];n.forEach(function(t){-1===e.indexOf(t.name)&&-1===o.indexOf(t.name)&&(i.push(t),o.push(t.name))}),r.forEach(function(t){-1===o.indexOf(t.name)&&(i.push(t),o.push(t.name))})}else"function"==typeof r?(i=r(n),i=Array.isArray(i)?i:[i]):i=v(n);var u=i.map(function(t){return t.name});return-1!==u.indexOf("Debug")&&i.push.apply(i,v(i.splice(u.indexOf("Debug"),1))),i}(t).forEach(function(t){n[t.name]=t,function(t){-1===Yt.indexOf(t.name)&&(t.setupOnce(qt,Jt),Yt.push(t.name),ot.log("Integration installed: "+t.name))}(t)}),n}var Zt,tn=function(){function t(t,n){this.rt={},this.it=0,this.et=new t(n),this.ot=n,n.dsn&&(this.ut=new O(n.dsn))}return t.prototype.captureException=function(t,n,r){var i=this,e=n&&n.event_id;return this.at(this.st().eventFromException(t,n).then(function(t){return i.ct(t,n,r)}).then(function(t){e=t})),e},t.prototype.captureMessage=function(t,n,r,i){var e=this,o=r&&r.event_id,u=y(t)?this.st().eventFromMessage(String(t),n,r):this.st().eventFromException(t,r);return this.at(u.then(function(t){return e.ct(t,r,i)}).then(function(t){o=t})),o},t.prototype.captureEvent=function(t,n,r){var i=n&&n.event_id;return this.at(this.ct(t,n,r).then(function(t){i=t})),i},t.prototype.captureSession=function(t){t.release?this.ft(t):ot.warn("Discarded session because of missing release")},t.prototype.getDsn=function(){return this.ut},t.prototype.getOptions=function(){return this.ot},t.prototype.flush=function(t){var n=this;return this.ht(t).then(function(r){return n.st().getTransport().close(t).then(function(t){return r&&t})})},t.prototype.close=function(t){var n=this;return this.flush(t).then(function(t){return n.getOptions().enabled=!1,t})},t.prototype.setupIntegrations=function(){this.vt()&&(this.rt=Qt(this.ot))},t.prototype.getIntegration=function(t){try{return this.rt[t.id]||null}catch(n){return ot.warn("Cannot retrieve integration "+t.id+" from the current Client"),null}},t.prototype.dt=function(t,n){var r,i,o,u=!1,a=!1,c=n.exception&&n.exception.values;if(c){a=!0;try{for(var h=f(c),v=h.next();!v.done;v=h.next()){var d=v.value.mechanism;if(d&&!1===d.handled){u=!0;break}}}catch(t){r={error:t}}finally{try{v&&!v.done&&(i=h.return)&&i.call(h)}finally{if(r)throw r.error}}}var l=n.user;if(!t.userAgent){var p=n.request?n.request.headers:{};for(var m in p)if("user-agent"===m.toLowerCase()){o=p[m];break}}t.update(s(s({},u&&{status:e.Crashed}),{user:l,userAgent:o,errors:t.errors+Number(a||u)}))},t.prototype.ft=function(t){this.st().sendSession(t)},t.prototype.ht=function(t){var n=this;return new jt(function(r){var i=0,e=setInterval(function(){0==n.it?(clearInterval(e),r(!0)):(i+=1,t&&i>=t&&(clearInterval(e),r(!1)))},1)})},t.prototype.st=function(){return this.et},t.prototype.vt=function(){return!1!==this.getOptions().enabled&&void 0!==this.ut},t.prototype.lt=function(t,n,r){var i=this,e=this.getOptions().normalizeDepth,o=void 0===e?3:e,u=s(s({},t),{event_id:t.event_id||(r&&r.event_id?r.event_id:V()),timestamp:t.timestamp||It()});this.pt(u),this.yt(u);var a=n;r&&r.captureContext&&(a=At.clone(a).update(r.captureContext));var c=jt.resolve(u);return a&&(c=a.applyToEvent(u,r)),c.then(function(t){return"number"==typeof o&&o>0?i.gt(t,o):t})},t.prototype.gt=function(t,n){if(!t)return null;var r=s(s(s(s(s({},t),t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(function(t){return s(s({},t),t.data&&{data:H(t.data,n)})})}),t.user&&{user:H(t.user,n)}),t.contexts&&{contexts:H(t.contexts,n)}),t.extra&&{extra:H(t.extra,n)});return t.contexts&&t.contexts.trace&&(r.contexts.trace=t.contexts.trace),r},t.prototype.pt=function(t){var n=this.getOptions(),r=n.environment,i=n.release,e=n.dist,o=n.maxValueLength,u=void 0===o?250:o;"environment"in t||(t.environment="environment"in n?r:"production"),void 0===t.release&&void 0!==i&&(t.release=i),void 0===t.dist&&void 0!==e&&(t.dist=e),t.message&&(t.message=I(t.message,u));var a=t.exception&&t.exception.values&&t.exception.values[0];a&&a.value&&(a.value=I(a.value,u));var s=t.request;s&&s.url&&(s.url=I(s.url,u))},t.prototype.yt=function(t){var n=t.sdk,r=Object.keys(this.rt);n&&r.length>0&&(n.integrations=r)},t.prototype.bt=function(t){this.st().sendEvent(t)},t.prototype.ct=function(t,n,r){return this.wt(t,n,r).then(function(t){return t.event_id},function(t){ot.error(t)})},t.prototype.wt=function(t,n,r){var i=this,e=this.getOptions(),o=e.beforeSend,u=e.sampleRate;if(!this.vt())return jt.reject(new x("SDK not enabled, will not send event."));var a="transaction"===t.type;return!a&&"number"==typeof u&&Math.random()>u?jt.reject(new x("Discarding event because it's not included in the random sample (sampling rate = "+u+")")):this.lt(t,r,n).then(function(t){if(null===t)throw new x("An event processor returned null, will not send event.");if(n&&n.data&&!0===n.data.__sentry__||a||!o)return t;var r=o(t,n);if(void 0===r)throw new x("`beforeSend` method has to return `null` or a valid event.");return T(r)?r.then(function(t){return t},function(t){throw new x("beforeSend rejected with "+t)}):r}).then(function(t){if(null===t)throw new x("`beforeSend` returned `null`, will not send event.");var n=r&&r.getSession&&r.getSession();return!a&&n&&i.dt(n,t),i.bt(t),t}).then(null,function(t){if(t instanceof x)throw t;throw i.captureException(t,{data:{__sentry__:!0},originalException:t}),new x("Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: "+t)})},t.prototype.at=function(t){var n=this;this.it+=1,t.then(function(t){return n.it-=1,t},function(t){return n.it-=1,t})},t}(),nn=function(){function n(){}return n.prototype.sendEvent=function(n){return jt.resolve({reason:"NoopTransport: Event has been skipped because no Dsn is configured.",status:t.Status.Skipped})},n.prototype.close=function(t){return jt.resolve(!0)},n}(),rn=function(){function t(t){this.ot=t,this.ot.dsn||ot.warn("No DSN provided, backend will not do anything."),this.Tt=this._t()}return t.prototype.eventFromException=function(t,n){throw new x("Backend has to implement `eventFromException` method")},t.prototype.eventFromMessage=function(t,n,r){throw new x("Backend has to implement `eventFromMessage` method")},t.prototype.sendEvent=function(t){this.Tt.sendEvent(t).then(null,function(t){ot.error("Error while sending event: "+t)})},t.prototype.sendSession=function(t){this.Tt.sendSession?this.Tt.sendSession(t).then(null,function(t){ot.error("Error while sending session: "+t)}):ot.warn("Dropping session because custom transport doesn't implement sendSession")},t.prototype.getTransport=function(){return this.Tt},t.prototype._t=function(){return new nn},t}();function en(t,n){return{body:JSON.stringify({sent_at:(new Date).toISOString()})+"\n"+JSON.stringify({type:"session"})+"\n"+JSON.stringify(t),type:"session",url:n.getEnvelopeEndpointWithUrlEncodedAuth()}}function on(t,n){var r=t.tags||{},i=r.__sentry_samplingMethod,e=r.__sentry_sampleRate,o=c(r,["__sentry_samplingMethod","__sentry_sampleRate"]);t.tags=o;var u="transaction"===t.type,a={body:JSON.stringify(t),type:t.type||"event",url:u?n.getEnvelopeEndpointWithUrlEncodedAuth():n.getStoreEndpointWithUrlEncodedAuth()};if(u){var s=JSON.stringify({event_id:t.event_id,sent_at:(new Date).toISOString()})+"\n"+JSON.stringify({type:t.type,sample_rates:[{id:i,rate:e}]})+"\n"+a.body;a.body=s}return a}var un=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(){Zt=Function.prototype.toString,Function.prototype.toString=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=this.__sentry_original__||this;return Zt.apply(r,t)}},t.id="FunctionToString",t}(),an=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/],sn=function(){function t(n){void 0===n&&(n={}),this.ot=n,this.name=t.id}return t.prototype.setupOnce=function(){qt(function(n){var r=Jt();if(!r)return n;var i=r.getIntegration(t);if(i){var e=r.getClient(),o=e?e.getOptions():{},u=i.Et(o);if(i.St(n,u))return null}return n})},t.prototype.St=function(t,n){return this.kt(t,n)?(ot.warn("Event dropped due to being internal Sentry Error.\nEvent: "+Y(t)),!0):this.xt(t,n)?(ot.warn("Event dropped due to being matched by `ignoreErrors` option.\nEvent: "+Y(t)),!0):this.jt(t,n)?(ot.warn("Event dropped due to being matched by `denyUrls` option.\nEvent: "+Y(t)+".\nUrl: "+this.Ot(t)),!0):!this.Dt(t,n)&&(ot.warn("Event dropped due to not being matched by `allowUrls` option.\nEvent: "+Y(t)+".\nUrl: "+this.Ot(t)),!0)},t.prototype.kt=function(t,n){if(!n.ignoreInternal)return!1;try{return t&&t.exception&&t.exception.values&&t.exception.values[0]&&"SentryError"===t.exception.values[0].type||!1}catch(t){return!1}},t.prototype.xt=function(t,n){return!(!n.ignoreErrors||!n.ignoreErrors.length)&&this.Nt(t).some(function(t){return n.ignoreErrors.some(function(n){return C(t,n)})})},t.prototype.jt=function(t,n){if(!n.denyUrls||!n.denyUrls.length)return!1;var r=this.Ot(t);return!!r&&n.denyUrls.some(function(t){return C(r,t)})},t.prototype.Dt=function(t,n){if(!n.allowUrls||!n.allowUrls.length)return!0;var r=this.Ot(t);return!r||n.allowUrls.some(function(t){return C(r,t)})},t.prototype.Et=function(t){return void 0===t&&(t={}),{allowUrls:v(this.ot.whitelistUrls||[],this.ot.allowUrls||[],t.whitelistUrls||[],t.allowUrls||[]),denyUrls:v(this.ot.blacklistUrls||[],this.ot.denyUrls||[],t.blacklistUrls||[],t.denyUrls||[]),ignoreErrors:v(this.ot.ignoreErrors||[],t.ignoreErrors||[],an),ignoreInternal:void 0===this.ot.ignoreInternal||this.ot.ignoreInternal}},t.prototype.Nt=function(t){if(t.message)return[t.message];if(t.exception)try{var n=t.exception.values&&t.exception.values[0]||{},r=n.type,i=void 0===r?"":r,e=n.value,o=void 0===e?"":e;return[""+o,i+": "+o]}catch(n){return ot.error("Cannot extract message for event "+Y(t)),[]}return[]},t.prototype.Ot=function(t){try{if(t.stacktrace){var n=t.stacktrace.frames;return n&&n[n.length-1].filename||null}if(t.exception){var r=t.exception.values&&t.exception.values[0].stacktrace&&t.exception.values[0].stacktrace.frames;return r&&r[r.length-1].filename||null}return null}catch(n){return ot.error("Cannot extract url for event "+Y(t)),null}},t.id="InboundFilters",t}(),cn=Object.freeze({__proto__:null,FunctionToString:un,InboundFilters:sn}),fn="?",hn=/^\s*at (?:(.*?) ?\()?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,vn=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|capacitor).*?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. \/=]+)(?::(\d+))?(?::(\d+))?\s*$/i,dn=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,ln=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,pn=/\((\S*)(?::(\d+))(?::(\d+))\)/,mn=/Minified React error #\d+;/i;function yn(t){var n=null,r=0;t&&("number"==typeof t.framesToPop?r=t.framesToPop:mn.test(t.message)&&(r=1));try{if(n=function(t){if(!t||!t.stacktrace)return null;for(var n,r=t.stacktrace,i=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,e=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\((.*)\))? in (.*):\s*$/i,o=r.split("\n"),u=[],a=0;a<o.length;a+=2){var s=null;(n=i.exec(o[a]))?s={url:n[2],func:n[3],args:[],line:+n[1],column:null}:(n=e.exec(o[a]))&&(s={url:n[6],func:n[3]||n[4],args:n[5]?n[5].split(","):[],line:+n[1],column:+n[2]}),s&&(!s.func&&s.line&&(s.func=fn),u.push(s))}if(!u.length)return null;return{message:bn(t),name:t.name,stack:u}}(t))return gn(n,r)}catch(t){}try{if(n=function(t){if(!t||!t.stack)return null;for(var n,r,i,e=[],o=t.stack.split("\n"),u=0;u<o.length;++u){if(r=hn.exec(o[u])){var a=r[2]&&0===r[2].indexOf("native");r[2]&&0===r[2].indexOf("eval")&&(n=pn.exec(r[2]))&&(r[2]=n[1],r[3]=n[2],r[4]=n[3]),i={url:r[2]&&0===r[2].indexOf("address at ")?r[2].substr("address at ".length):r[2],func:r[1]||fn,args:a?[r[2]]:[],line:r[3]?+r[3]:null,column:r[4]?+r[4]:null}}else if(r=dn.exec(o[u]))i={url:r[2],func:r[1]||fn,args:[],line:+r[3],column:r[4]?+r[4]:null};else{if(!(r=vn.exec(o[u])))continue;r[3]&&r[3].indexOf(" > eval")>-1&&(n=ln.exec(r[3]))?(r[1]=r[1]||"eval",r[3]=n[1],r[4]=n[2],r[5]=""):0!==u||r[5]||void 0===t.columnNumber||(e[0].column=t.columnNumber+1),i={url:r[3],func:r[1]||fn,args:r[2]?r[2].split(","):[],line:r[4]?+r[4]:null,column:r[5]?+r[5]:null}}!i.func&&i.line&&(i.func=fn),e.push(i)}if(!e.length)return null;return{message:bn(t),name:t.name,stack:e}}(t))return gn(n,r)}catch(t){}return{message:bn(t),name:t&&t.name,stack:[],failed:!0}}function gn(t,n){try{return s(s({},t),{stack:t.stack.slice(n)})}catch(n){return t}}function bn(t){var n=t&&t.message;return n?n.error&&"string"==typeof n.error.message?n.error.message:n:"No error message"}var wn=50;function Tn(t){var n=En(t.stack),r={type:t.name,value:t.message};return n&&n.length&&(r.stacktrace={frames:n}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function _n(t){return{exception:{values:[Tn(t)]}}}function En(t){if(!t||!t.length)return[];var n=t,r=n[0].func||"",i=n[n.length-1].func||"";return-1===r.indexOf("captureMessage")&&-1===r.indexOf("captureException")||(n=n.slice(1)),-1!==i.indexOf("sentryWrapped")&&(n=n.slice(0,-1)),n.slice(0,wn).map(function(t){return{colno:null===t.column?void 0:t.column,filename:t.url||n[0].url,function:t.func||"?",in_app:!0,lineno:null===t.line?void 0:t.line}}).reverse()}function Sn(t,n,r){var i,e;if(void 0===r&&(r={}),l(t)&&t.error)return i=_n(yn(t=t.error));if(p(t)||(e=t,"[object DOMException]"===Object.prototype.toString.call(e))){var o=t,u=o.name||(p(o)?"DOMError":"DOMException"),a=o.message?u+": "+o.message:u;return Z(i=kn(a,n,r),a),"code"in o&&(i.tags=s(s({},i.tags),{"DOMException.code":""+o.code})),i}return d(t)?i=_n(yn(t)):g(t)||b(t)?(tt(i=function(t,n,r){var i={exception:{values:[{type:b(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:"Non-Error "+(r?"promise rejection":"exception")+" captured with keys: "+B(t)}]},extra:{__serialized__:F(t)}};if(n){var e=En(yn(n).stack);i.stacktrace={frames:e}}return i}(t,n,r.rejection),{synthetic:!0}),i):(Z(i=kn(t,n,r),""+t,void 0),tt(i,{synthetic:!0}),i)}function kn(t,n,r){void 0===r&&(r={});var i={message:t};if(r.attachStacktrace&&n){var e=En(yn(n).stack);i.stacktrace={frames:e}}return i}var xn=function(){function n(t){this.options=t,this.O=new Ot(30),this.Rt={},this.It=new Kt(this.options.dsn),this.url=this.It.getStoreEndpointWithUrlEncodedAuth()}return n.prototype.sendEvent=function(t){throw new x("Transport Class has to implement `sendEvent` method")},n.prototype.close=function(t){return this.O.drain(t)},n.prototype.Mt=function(n){var r=n.requestType,i=n.response,e=n.headers,o=n.resolve,u=n.reject,a=t.Status.fromHttpCode(i.status);this.Ct(e)&&ot.warn("Too many requests, backing off until: "+this.At(r)),a!==t.Status.Success?u(i):o({status:a})},n.prototype.At=function(t){return this.Rt[t]||this.Rt.all},n.prototype.Lt=function(t){return this.At(t)>new Date(Date.now())},n.prototype.Ct=function(t){var n,r,i,e,o=Date.now(),u=t["x-sentry-rate-limits"],a=t["retry-after"];if(u){try{for(var s=f(u.trim().split(",")),c=s.next();!c.done;c=s.next()){var h=c.value.split(":",2),v=parseInt(h[0],10),d=1e3*(isNaN(v)?60:v);try{for(var l=(i=void 0,f(h[1].split(";"))),p=l.next();!p.done;p=l.next()){var m=p.value;this.Rt[m||"all"]=new Date(o+d)}}catch(t){i={error:t}}finally{try{p&&!p.done&&(e=l.return)&&e.call(l)}finally{if(i)throw i.error}}}}catch(t){n={error:t}}finally{try{c&&!c.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return!0}return!!a&&(this.Rt.all=new Date(o+function(t,n){if(!n)return nt;var r=parseInt(""+n,10);if(!isNaN(r))return 1e3*r;var i=Date.parse(""+n);return isNaN(i)?nt:i-t}(o,a)),!0)},n}(),jn=z(),On=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r(n,t),n.prototype.sendEvent=function(t){return this.qt(on(t,this.It),t)},n.prototype.sendSession=function(t){return this.qt(en(t,this.It),t)},n.prototype.qt=function(t,n){var r=this;if(this.Lt(t.type))return Promise.reject({event:n,type:t.type,reason:"Transport locked till "+this.At(t.type)+" due to too many requests.",status:429});var i={body:t.body,method:"POST",referrerPolicy:st()?"origin":""};return void 0!==this.options.fetchParameters&&Object.assign(i,this.options.fetchParameters),void 0!==this.options.headers&&(i.headers=this.options.headers),this.O.add(new jt(function(n,e){jn.fetch(t.url,i).then(function(i){var o={"x-sentry-rate-limits":i.headers.get("X-Sentry-Rate-Limits"),"retry-after":i.headers.get("Retry-After")};r.Mt({requestType:t.type,response:i,headers:o,resolve:n,reject:e})}).catch(e)}))},n}(xn),Dn=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r(n,t),n.prototype.sendEvent=function(t){return this.qt(on(t,this.It),t)},n.prototype.sendSession=function(t){return this.qt(en(t,this.It),t)},n.prototype.qt=function(t,n){var r=this;return this.Lt(t.type)?Promise.reject({event:n,type:t.type,reason:"Transport locked till "+this.At(t.type)+" due to too many requests.",status:429}):this.O.add(new jt(function(n,i){var e=new XMLHttpRequest;for(var o in e.onreadystatechange=function(){if(4===e.readyState){var o={"x-sentry-rate-limits":e.getResponseHeader("X-Sentry-Rate-Limits"),"retry-after":e.getResponseHeader("Retry-After")};r.Mt({requestType:t.type,response:e,headers:o,resolve:n,reject:i})}},e.open("POST",t.url),r.options.headers)r.options.headers.hasOwnProperty(o)&&e.setRequestHeader(o,r.options.headers[o]);e.send(t.body)}))},n}(xn),Nn=Object.freeze({__proto__:null,BaseTransport:xn,FetchTransport:On,XHRTransport:Dn}),Rn=function(n){function i(){return null!==n&&n.apply(this,arguments)||this}return r(i,n),i.prototype.eventFromException=function(n,r){return function(n,r,i){var e=Sn(r,i&&i.syntheticException||void 0,{attachStacktrace:n.attachStacktrace});return tt(e,{handled:!0,type:"generic"}),e.level=t.Severity.Error,i&&i.event_id&&(e.event_id=i.event_id),jt.resolve(e)}(this.ot,n,r)},i.prototype.eventFromMessage=function(n,r,i){return void 0===r&&(r=t.Severity.Info),function(n,r,i,e){void 0===i&&(i=t.Severity.Info);var o=kn(r,e&&e.syntheticException||void 0,{attachStacktrace:n.attachStacktrace});return o.level=i,e&&e.event_id&&(o.event_id=e.event_id),jt.resolve(o)}(this.ot,n,r,i)},i.prototype._t=function(){if(!this.ot.dsn)return n.prototype._t.call(this);var t=s(s({},this.ot.transportOptions),{dsn:this.ot.dsn});return this.ot.transport?new this.ot.transport(t):ut()?new On(t):new Dn(t)},i}(rn),In=0;function Mn(){return In>0}function Cn(t,n,r){if(void 0===n&&(n={}),"function"!=typeof t)return t;try{if(t.__sentry__)return t;if(t.__sentry_wrapped__)return t.__sentry_wrapped__}catch(n){return t}var sentryWrapped=function(){var i=Array.prototype.slice.call(arguments);try{r&&"function"==typeof r&&r.apply(this,arguments);var e=i.map(function(t){return Cn(t,n)});return t.handleEvent?t.handleEvent.apply(this,e):t.apply(this,e)}catch(t){throw In+=1,setTimeout(function(){In-=1}),Vt(function(r){r.addEventProcessor(function(t){var r=s({},t);return n.mechanism&&(Z(r,void 0,void 0),tt(r,n.mechanism)),r.extra=s(s({},r.extra),{arguments:i}),r}),captureException(t)}),t}};try{for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(sentryWrapped[i]=t[i])}catch(t){}t.prototype=t.prototype||{},sentryWrapped.prototype=t.prototype,Object.defineProperty(t,"__sentry_wrapped__",{enumerable:!1,value:sentryWrapped}),Object.defineProperties(sentryWrapped,{__sentry__:{enumerable:!1,value:!0},__sentry_original__:{enumerable:!1,value:t}});try{Object.getOwnPropertyDescriptor(sentryWrapped,"name").configurable&&Object.defineProperty(sentryWrapped,"name",{get:function(){return t.name}})}catch(t){}return sentryWrapped}function An(t){if(void 0===t&&(t={}),t.eventId)if(t.dsn){var n=document.createElement("script");n.async=!0,n.src=new Kt(t.dsn).getReportDialogEndpoint(t),t.onLoad&&(n.onload=t.onLoad),(document.head||document.body).appendChild(n)}else ot.error("Missing dsn option in showReportDialog call");else ot.error("Missing eventId option in showReportDialog call")}var Ln=function(){function n(t){this.name=n.id,this.Ft=!1,this.Ut=!1,this.ot=s({onerror:!0,onunhandledrejection:!0},t)}return n.prototype.setupOnce=function(){Error.stackTraceLimit=50,this.ot.onerror&&(ot.log("Global Handler attached: onerror"),this.Pt()),this.ot.onunhandledrejection&&(ot.log("Global Handler attached: onunhandledrejection"),this.Ht())},n.prototype.Pt=function(){var t=this;this.Ft||(lt({callback:function(r){var i=r.error,e=Jt(),o=e.getIntegration(n),u=i&&!0===i.__sentry_own_request__;if(o&&!Mn()&&!u){var a=e.getClient(),s=y(i)?t.Bt(r.msg,r.url,r.line,r.column):t.Jt(Sn(i,void 0,{attachStacktrace:a&&a.getOptions().attachStacktrace,rejection:!1}),r.url,r.line,r.column);tt(s,{handled:!1,type:"onerror"}),e.captureEvent(s,{originalException:i})}},type:"error"}),this.Ft=!0)},n.prototype.Ht=function(){var r=this;this.Ut||(lt({callback:function(i){var e=i;try{"reason"in i?e=i.reason:"detail"in i&&"reason"in i.detail&&(e=i.detail.reason)}catch(t){}var o=Jt(),u=o.getIntegration(n),a=e&&!0===e.__sentry_own_request__;if(!u||Mn()||a)return!0;var s=o.getClient(),c=y(e)?r.Xt(e):Sn(e,void 0,{attachStacktrace:s&&s.getOptions().attachStacktrace,rejection:!0});c.level=t.Severity.Error,tt(c,{handled:!1,type:"onunhandledrejection"}),o.captureEvent(c,{originalException:e})},type:"unhandledrejection"}),this.Ut=!0)},n.prototype.Bt=function(t,n,r,i){var e,o=l(t)?t.message:t;if(m(o)){var u=o.match(/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i);u&&(e=u[1],o=u[2])}var a={exception:{values:[{type:e||"Error",value:o}]}};return this.Jt(a,n,r,i)},n.prototype.Xt=function(t){return{exception:{values:[{type:"UnhandledRejection",value:"Non-Error promise rejection captured with value: "+String(t)}]}}},n.prototype.Jt=function(t,n,r,i){t.exception=t.exception||{},t.exception.values=t.exception.values||[],t.exception.values[0]=t.exception.values[0]||{},t.exception.values[0].stacktrace=t.exception.values[0].stacktrace||{},t.exception.values[0].stacktrace.frames=t.exception.values[0].stacktrace.frames||[];var e=isNaN(parseInt(i,10))?void 0:i,o=isNaN(parseInt(r,10))?void 0:r,u=m(n)&&n.length>0?n:function(){try{return document.location.href}catch(t){return""}}();return 0===t.exception.values[0].stacktrace.frames.length&&t.exception.values[0].stacktrace.frames.push({colno:e,filename:u,function:"?",in_app:!0,lineno:o}),t},n.id="GlobalHandlers",n}(),qn=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Fn=function(){function t(n){this.name=t.id,this.ot=s({XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0},n)}return t.prototype.setupOnce=function(){var t=z();(this.ot.setTimeout&&A(t,"setTimeout",this.Gt.bind(this)),this.ot.setInterval&&A(t,"setInterval",this.Gt.bind(this)),this.ot.requestAnimationFrame&&A(t,"requestAnimationFrame",this.Wt.bind(this)),this.ot.XMLHttpRequest&&"XMLHttpRequest"in t&&A(XMLHttpRequest.prototype,"send",this.$t.bind(this)),this.ot.eventTarget)&&(Array.isArray(this.ot.eventTarget)?this.ot.eventTarget:qn).forEach(this.zt.bind(this))},t.prototype.Gt=function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=n[0];return n[0]=Cn(i,{mechanism:{data:{function:R(t)},handled:!0,type:"instrument"}}),t.apply(this,n)}},t.prototype.Wt=function(t){return function(n){return t.call(this,Cn(n,{mechanism:{data:{function:"requestAnimationFrame",handler:R(t)},handled:!0,type:"instrument"}}))}},t.prototype.zt=function(t){var n=z(),r=n[t]&&n[t].prototype;r&&r.hasOwnProperty&&r.hasOwnProperty("addEventListener")&&(A(r,"addEventListener",function(n){return function(r,i,e){try{"function"==typeof i.handleEvent&&(i.handleEvent=Cn(i.handleEvent.bind(i),{mechanism:{data:{function:"handleEvent",handler:R(i),target:t},handled:!0,type:"instrument"}}))}catch(t){}return n.call(this,r,Cn(i,{mechanism:{data:{function:"addEventListener",handler:R(i),target:t},handled:!0,type:"instrument"}}),e)}}),A(r,"removeEventListener",function(t){return function(n,r,i){var e,o=r;try{var u=null===(e=o)||void 0===e?void 0:e.__sentry_wrapped__;u&&t.call(this,n,u,i)}catch(t){}return t.call(this,n,o,i)}}))},t.prototype.$t=function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(function(t){t in i&&"function"==typeof i[t]&&A(i,t,function(n){var r={mechanism:{data:{function:t,handler:R(n)},handled:!0,type:"instrument"}};return n.__sentry_original__&&(r.mechanism.data.handler=R(n.__sentry_original__)),Cn(n,r)})}),t.apply(this,n)}},t.id="TryCatch",t}(),Un=function(){function n(t){this.name=n.id,this.ot=s({console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0},t)}return n.prototype.addSentryBreadcrumb=function(t){this.ot.sentry&&Jt().addBreadcrumb({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:Y(t)},{event:t})},n.prototype.setupOnce=function(){var t=this;this.ot.console&&lt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Vt.apply(t,v(n))},type:"console"}),this.ot.dom&&lt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Kt.apply(t,v(n))},type:"dom"}),this.ot.xhr&&lt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Yt.apply(t,v(n))},type:"xhr"}),this.ot.fetch&&lt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Qt.apply(t,v(n))},type:"fetch"}),this.ot.history&&lt({callback:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t.Zt.apply(t,v(n))},type:"history"})},n.prototype.Vt=function(n){var r={category:"console",data:{arguments:n.args,logger:"console"},level:t.Severity.fromString(n.level),message:M(n.args," ")};if("assert"===n.level){if(!1!==n.args[0])return;r.message="Assertion failed: "+(M(n.args.slice(1)," ")||"console.assert"),r.data.arguments=n.args.slice(1)}Jt().addBreadcrumb(r,{input:n.args,level:n.level})},n.prototype.Kt=function(t){var n;try{n=t.event.target?E(t.event.target):E(t.event)}catch(t){n="<unknown>"}0!==n.length&&Jt().addBreadcrumb({category:"ui."+t.name,message:n},{event:t.event,name:t.name})},n.prototype.Yt=function(t){if(t.endTimestamp){if(t.xhr.__sentry_own_request__)return;var n=t.xhr.__sentry_xhr__||{},r=n.method,i=n.url,e=n.status_code,o=n.body;Jt().addBreadcrumb({category:"xhr",data:{method:r,url:i,status_code:e},type:"http"},{xhr:t.xhr,input:o})}else;},n.prototype.Qt=function(n){n.endTimestamp&&(n.fetchData.url.match(/sentry_key/)&&"POST"===n.fetchData.method||(n.error?Jt().addBreadcrumb({category:"fetch",data:n.fetchData,level:t.Severity.Error,type:"http"},{data:n.error,input:n.args}):Jt().addBreadcrumb({category:"fetch",data:s(s({},n.fetchData),{status_code:n.response.status}),type:"http"},{input:n.args,response:n.response})))},n.prototype.Zt=function(t){var n=z(),r=t.from,i=t.to,e=K(n.location.href),o=K(r),u=K(i);o.path||(o=e),e.protocol===u.protocol&&e.host===u.host&&(i=u.relative),e.protocol===o.protocol&&e.host===o.host&&(r=o.relative),Jt().addBreadcrumb({category:"navigation",data:{from:r,to:i}})},n.id="Breadcrumbs",n}(),Pn="cause",Hn=5,Bn=function(){function t(n){void 0===n&&(n={}),this.name=t.id,this.tn=n.key||Pn,this.j=n.limit||Hn}return t.prototype.setupOnce=function(){qt(function(n,r){var i=Jt().getIntegration(t);return i?i.nn(n,r):n})},t.prototype.nn=function(t,n){if(!(t.exception&&t.exception.values&&n&&_(n.originalException,Error)))return t;var r=this.rn(n.originalException,this.tn);return t.exception.values=v(r,t.exception.values),t},t.prototype.rn=function(t,n,r){if(void 0===r&&(r=[]),!_(t[n],Error)||r.length+1>=this.j)return r;var i=Tn(yn(t[n]));return this.rn(t[n],n,v([i],r))},t.id="LinkedErrors",t}(),Jn=z(),Xn=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(){qt(function(n){var r,i,e;if(Jt().getIntegration(t)){if(!Jn.navigator&&!Jn.location&&!Jn.document)return n;var o=(null===(r=n.request)||void 0===r?void 0:r.url)||(null===(i=Jn.location)||void 0===i?void 0:i.href),u=(Jn.document||{}).referrer,a=(Jn.navigator||{}).userAgent,c=s(s(s({},null===(e=n.request)||void 0===e?void 0:e.headers),u&&{Referer:u}),a&&{"User-Agent":a}),f=s(s({},o&&{url:o}),{headers:c});return s(s({},n),{request:f})}return n})},t.id="UserAgent",t}(),Gn=Object.freeze({__proto__:null,GlobalHandlers:Ln,TryCatch:Fn,Breadcrumbs:Un,LinkedErrors:Bn,UserAgent:Xn}),Wn="sentry.javascript.browser",$n=function(t){function n(n){return void 0===n&&(n={}),t.call(this,Rn,n)||this}return r(n,t),n.prototype.showReportDialog=function(t){void 0===t&&(t={}),z().document&&(this.vt()?An(s(s({},t),{dsn:t.dsn||this.getDsn()})):ot.error("Trying to call showReportDialog with Sentry Client disabled"))},n.prototype.lt=function(n,r,i){return n.platform=n.platform||"javascript",n.sdk=s(s({},n.sdk),{name:Wn,packages:v(n.sdk&&n.sdk.packages||[],[{name:"npm:@sentry/browser",version:"5.30.0"}]),version:"5.30.0"}),t.prototype.lt.call(this,n,r,i)},n.prototype.bt=function(n){var r=this.getIntegration(Un);r&&r.addSentryBreadcrumb(n),t.prototype.bt.call(this,n)},n}(tn),zn=[new sn,new un,new Fn,new Un,new Ln,new Bn,new Xn];var Vn={},Kn=z();Kn.Sentry&&Kn.Sentry.Integrations&&(Vn=Kn.Sentry.Integrations);var Yn,Qn=s(s(s({},Vn),cn),Gn);!function(t){t.Ok="ok",t.DeadlineExceeded="deadline_exceeded",t.Unauthenticated="unauthenticated",t.PermissionDenied="permission_denied",t.NotFound="not_found",t.ResourceExhausted="resource_exhausted",t.InvalidArgument="invalid_argument",t.Unimplemented="unimplemented",t.Unavailable="unavailable",t.InternalError="internal_error",t.UnknownError="unknown_error",t.Cancelled="cancelled",t.AlreadyExists="already_exists",t.FailedPrecondition="failed_precondition",t.Aborted="aborted",t.OutOfRange="out_of_range",t.DataLoss="data_loss"}(Yn||(Yn={})),function(t){t.fromHttpCode=function(n){if(n<400)return t.Ok;if(n>=400&&n<500)switch(n){case 401:return t.Unauthenticated;case 403:return t.PermissionDenied;case 404:return t.NotFound;case 409:return t.AlreadyExists;case 413:return t.FailedPrecondition;case 429:return t.ResourceExhausted;default:return t.InvalidArgument}if(n>=500&&n<600)switch(n){case 501:return t.Unimplemented;case 503:return t.Unavailable;case 504:return t.DeadlineExceeded;default:return t.InternalError}return t.UnknownError}}(Yn||(Yn={}));var Zn=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function tr(t){return"tracesSampleRate"in t||"tracesSampler"in t}function nr(t){var n,r;return void 0===t&&(t=Jt()),null===(r=null===(n=t)||void 0===n?void 0:n.getScope())||void 0===r?void 0:r.getTransaction()}function rr(t){return t/1e3}function ir(){var t=nr();t&&(ot.log("[Tracing] Transaction: "+Yn.InternalError+" -> Global error occured"),t.setStatus(Yn.InternalError))}var er=function(){function t(t){void 0===t&&(t=1e3),this.spans=[],this.in=t}return t.prototype.add=function(t){this.spans.length>this.in?t.spanRecorder=void 0:this.spans.push(t)},t}(),or=function(){function t(t){if(this.traceId=V(),this.spanId=V().substring(16),this.startTimestamp=Mt(),this.tags={},this.data={},!t)return this;t.traceId&&(this.traceId=t.traceId),t.spanId&&(this.spanId=t.spanId),t.parentSpanId&&(this.parentSpanId=t.parentSpanId),"sampled"in t&&(this.sampled=t.sampled),t.op&&(this.op=t.op),t.description&&(this.description=t.description),t.data&&(this.data=t.data),t.tags&&(this.tags=t.tags),t.status&&(this.status=t.status),t.startTimestamp&&(this.startTimestamp=t.startTimestamp),t.endTimestamp&&(this.endTimestamp=t.endTimestamp)}return t.prototype.child=function(t){return this.startChild(t)},t.prototype.startChild=function(n){var r=new t(s(s({},n),{parentSpanId:this.spanId,sampled:this.sampled,traceId:this.traceId}));return r.spanRecorder=this.spanRecorder,r.spanRecorder&&r.spanRecorder.add(r),r.transaction=this.transaction,r},t.prototype.setTag=function(t,n){var r;return this.tags=s(s({},this.tags),((r={})[t]=n,r)),this},t.prototype.setData=function(t,n){var r;return this.data=s(s({},this.data),((r={})[t]=n,r)),this},t.prototype.setStatus=function(t){return this.status=t,this},t.prototype.setHttpStatus=function(t){this.setTag("http.status_code",String(t));var n=Yn.fromHttpCode(t);return n!==Yn.UnknownError&&this.setStatus(n),this},t.prototype.isSuccess=function(){return this.status===Yn.Ok},t.prototype.finish=function(t){this.endTimestamp="number"==typeof t?t:Mt()},t.prototype.toTraceparent=function(){var t="";return void 0!==this.sampled&&(t=this.sampled?"-1":"-0"),this.traceId+"-"+this.spanId+t},t.prototype.getTraceContext=function(){return J({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,trace_id:this.traceId})},t.prototype.toJSON=function(){return J({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,start_timestamp:this.startTimestamp,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this.endTimestamp,trace_id:this.traceId})},t}(),ur=function(t){function n(n,r){var i=t.call(this,n)||this;return i.en={},i.on=Jt(),_(r,Pt)&&(i.on=r),i.name=n.name?n.name:"",i.un=n.trimEnd,i.transaction=i,i}return r(n,t),n.prototype.setName=function(t){this.name=t},n.prototype.initSpanRecorder=function(t){void 0===t&&(t=1e3),this.spanRecorder||(this.spanRecorder=new er(t)),this.spanRecorder.add(this)},n.prototype.setMeasurements=function(t){this.en=s({},t)},n.prototype.finish=function(n){var r=this;if(void 0===this.endTimestamp){if(this.name||(ot.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this.name="<unlabeled transaction>"),t.prototype.finish.call(this,n),!0===this.sampled){var i=this.spanRecorder?this.spanRecorder.spans.filter(function(t){return t!==r&&t.endTimestamp}):[];this.un&&i.length>0&&(this.endTimestamp=i.reduce(function(t,n){return t.endTimestamp&&n.endTimestamp?t.endTimestamp>n.endTimestamp?t:n:t}).endTimestamp);var e={contexts:{trace:this.getTraceContext()},spans:i,start_timestamp:this.startTimestamp,tags:this.tags,timestamp:this.endTimestamp,transaction:this.name,type:"transaction"};return Object.keys(this.en).length>0&&(ot.log("[Measurements] Adding measurements to transaction",JSON.stringify(this.en,void 0,2)),e.measurements=this.en),this.on.captureEvent(e)}ot.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled.")}},n}(or),ar=1e3,sr=function(t){function n(n,r,i,e){void 0===i&&(i="");var o=t.call(this,e)||this;return o.an=n,o.sn=r,o.transactionSpanId=i,o}return r(n,t),n.prototype.add=function(n){var r=this;n.spanId!==this.transactionSpanId&&(n.finish=function(t){n.endTimestamp="number"==typeof t?t:Mt(),r.sn(n.spanId)},void 0===n.endTimestamp&&this.an(n.spanId)),t.prototype.add.call(this,n)},n}(er),cr=function(t){function n(n,r,i,e){void 0===i&&(i=ar),void 0===e&&(e=!1);var o=t.call(this,n,r)||this;return o.cn=r,o.fn=i,o.hn=e,o.activities={},o.vn=0,o.dn=0,o.ln=!1,o.pn=[],r&&e&&(fr(r),ot.log("Setting idle transaction on scope. Span ID: "+o.spanId),r.configureScope(function(t){return t.setSpan(o)})),o}return r(n,t),n.prototype.finish=function(n){var r,i,e=this;if(void 0===n&&(n=Mt()),this.ln=!0,this.activities={},this.spanRecorder){ot.log("[Tracing] finishing IdleTransaction",new Date(1e3*n).toISOString(),this.op);try{for(var o=f(this.pn),u=o.next();!u.done;u=o.next()){(0,u.value)(this,n)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(i=o.return)&&i.call(o)}finally{if(r)throw r.error}}this.spanRecorder.spans=this.spanRecorder.spans.filter(function(t){if(t.spanId===e.spanId)return!0;t.endTimestamp||(t.endTimestamp=n,t.setStatus(Yn.Cancelled),ot.log("[Tracing] cancelling span since transaction ended early",JSON.stringify(t,void 0,2)));var r=t.startTimestamp<n;return r||ot.log("[Tracing] discarding Span since it happened after Transaction was finished",JSON.stringify(t,void 0,2)),r}),this.hn&&fr(this.cn),ot.log("[Tracing] flushing IdleTransaction")}else ot.log("[Tracing] No active IdleTransaction");return t.prototype.finish.call(this,n)},n.prototype.registerBeforeFinishCallback=function(t){this.pn.push(t)},n.prototype.initSpanRecorder=function(t){var n=this;if(!this.spanRecorder){this.mn=setTimeout(function(){n.ln||n.finish()},this.fn);this.spanRecorder=new sr(function(t){n.ln||n.an(t)},function(t){n.ln||n.sn(t)},this.spanId,t),ot.log("Starting heartbeat"),this.yn()}this.spanRecorder.add(this)},n.prototype.an=function(t){this.mn&&(clearTimeout(this.mn),this.mn=void 0),ot.log("[Tracing] pushActivity: "+t),this.activities[t]=!0,ot.log("[Tracing] new activities count",Object.keys(this.activities).length)},n.prototype.sn=function(t){var n=this;if(this.activities[t]&&(ot.log("[Tracing] popActivity "+t),delete this.activities[t],ot.log("[Tracing] new activities count",Object.keys(this.activities).length)),0===Object.keys(this.activities).length){var r=this.fn,i=Mt()+r/1e3;setTimeout(function(){n.ln||n.finish(i)},r)}},n.prototype.gn=function(){if(clearTimeout(this.vn),!this.ln){var t=Object.keys(this.activities),n=t.length?t.reduce(function(t,n){return t+n}):"";n===this.bn?this.dn+=1:this.dn=1,this.bn=n,this.dn>=3?(ot.log("[Tracing] Transaction finished because of no change for 3 heart beats"),this.setStatus(Yn.DeadlineExceeded),this.setTag("heartbeat","failed"),this.finish()):this.yn()}},n.prototype.yn=function(){var t=this;ot.log("pinging Heartbeat -> current counter: "+this.dn),this.vn=setTimeout(function(){t.gn()},5e3)},n}(ur);function fr(t){if(t){var n=t.getScope();if(n)n.getTransaction()&&n.setSpan(void 0)}}function hr(){var t=this.getScope();if(t){var n=t.getSpan();if(n)return{"sentry-trace":n.toTraceparent()}}return{}}function vr(t,n,r){var i,e,o=t.getClient(),u=o&&o.getOptions()||{};return o&&tr(u)?void 0!==n.sampled?(n.tags=s(s({},n.tags),{__sentry_samplingMethod:a.Explicit}),n):("function"==typeof u.tracesSampler?(e=u.tracesSampler(r),n.tags=s(s({},n.tags),{__sentry_samplingMethod:a.Sampler,__sentry_sampleRate:String(Number(e))})):void 0!==r.parentSampled?(e=r.parentSampled,n.tags=s(s({},n.tags),{__sentry_samplingMethod:a.Inheritance})):(e=u.tracesSampleRate,n.tags=s(s({},n.tags),{__sentry_samplingMethod:a.Rate,__sentry_sampleRate:String(Number(e))})),function(t){if(isNaN(t)||"number"!=typeof t&&"boolean"!=typeof t)return ot.warn("[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got "+JSON.stringify(t)+" of type "+JSON.stringify(typeof t)+"."),!1;if(t<0||t>1)return ot.warn("[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got "+t+"."),!1;return!0}(e)?e?(n.sampled=Math.random()<e,n.sampled?(n.initSpanRecorder(null===(i=u.wn)||void 0===i?void 0:i.maxSpans),ot.log("[Tracing] starting "+n.op+" transaction - "+n.name),n):(ot.log("[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = "+Number(e)+")"),n)):(ot.log("[Tracing] Discarding transaction because "+("function"==typeof u.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),n.sampled=!1,n):(ot.warn("[Tracing] Discarding transaction because of invalid sample rate."),n.sampled=!1,n)):(n.sampled=!1,n)}function dr(t){var n={transactionContext:t,parentSampled:t.parentSampled};if(X()){var r=Xt();if(r){var i=G(module,"http").IncomingMessage,e=r.members.find(function(t){return _(t,i)});e&&(n.request=function(t,n){if(void 0===n&&(n=W),!X())throw new Error("Can't get node request data outside of a node environment");var r={},i=t.headers||t.header||{},e=t.method,o=t.hostname||t.host||i.host||"<no host>",u="https"===t.protocol||t.secure||(t.socket||{}).encrypted?"https":"http",a=t.originalUrl||t.url||"",s=u+"://"+o+a;return n.forEach(function(n){switch(n){case"headers":r.headers=i;break;case"method":r.method=e;break;case"url":r.url=s;break;case"cookies":r.cookies=t.cookies||G(module,"cookie").parse(i.cookie||"");break;case"query_string":r.query_string=G(module,"url").parse(a||"",!1).query;break;case"data":if("GET"===e||"HEAD"===e)break;void 0!==t.body&&(r.data=m(t.body)?t.body:JSON.stringify(H(t.body)));break;default:({}).hasOwnProperty.call(t,n)&&(r[n]=t[n])}}),r}(e))}}else{var o=z();"location"in o&&(n.location=s({},o.location))}return n}function lr(t,n){return vr(this,new ur(t,this),s(s({},dr(t)),n))}function pr(){var t;(t=Ht()).__SENTRY__&&(t.__SENTRY__.extensions=t.__SENTRY__.extensions||{},t.__SENTRY__.extensions.startTransaction||(t.__SENTRY__.extensions.startTransaction=lr),t.__SENTRY__.extensions.traceHeaders||(t.__SENTRY__.extensions.traceHeaders=hr)),lt({callback:ir,type:"error"}),lt({callback:ir,type:"unhandledrejection"})}var mr=z();var yr,gr,br=function(t,n,r,i){var e;return function(){r&&n.isFinal&&r.disconnect(),n.value>=0&&(i||n.isFinal||"hidden"===document.visibilityState)&&(n.delta=n.value-(e||0),(n.delta||n.isFinal||void 0===e)&&(t(n),e=n.value))}},wr=function(t,n){return void 0===n&&(n=-1),{name:t,value:n,delta:0,entries:[],id:Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12),isFinal:!1}},Tr=function(t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver(function(t){return t.getEntries().map(n)});return r.observe({type:t,buffered:!0}),r}}catch(t){}},_r=!1,Er=!1,Sr=function(t){_r=!t.persisted},kr=function(t,n){void 0===n&&(n=!1),Er||(addEventListener("pagehide",Sr),addEventListener("beforeunload",function(){}),Er=!0),addEventListener("visibilitychange",function(n){var r=n.timeStamp;"hidden"===document.visibilityState&&t({timeStamp:r,isUnloading:_r})},{capture:!0,once:n})},xr=function(){return void 0===yr&&(yr="hidden"===document.visibilityState?0:1/0,kr(function(t){var n=t.timeStamp;return yr=n},!0)),{get timeStamp(){return yr}}},jr=function(t,n){void 0===n&&(n=!1);var r,i=wr("LCP"),e=xr(),o=function(t){var n=t.startTime;n<e.timeStamp?(i.value=n,i.entries.push(t)):i.isFinal=!0,r()},u=Tr("largest-contentful-paint",o);if(u){r=br(t,i,u,n);var a=function(){i.isFinal||(u.takeRecords().map(o),i.isFinal=!0,r())};(gr||(gr=new Promise(function(t){return["scroll","keydown","pointerdown"].map(function(n){addEventListener(n,t,{once:!0,passive:!0,capture:!0})})})),gr).then(a),kr(a,!0)}},Or=z(),Dr=function(t){var n,r=wr("TTFB");n=function(){try{var n=Or.performance.getEntriesByType("navigation")[0]||function(){var t=Or.performance.timing,n={entryType:"navigation",startTime:0};for(var r in t)"navigationStart"!==r&&"toJSON"!==r&&(n[r]=Math.max(t[r]-t.navigationStart,0));return n}();r.value=r.delta=n.responseStart,r.entries=[n],t(r)}catch(t){}},"complete"===document.readyState?setTimeout(n,0):addEventListener("pageshow",n)},Nr=z(),Rr=function(){function t(){this.en={},this.Tn=0,Nr&&Nr.performance&&(Nr.performance.mark&&Nr.performance.mark("sentry-tracing-init"),this._n(),this.En(),this.Sn(),this.kn())}return t.prototype.addPerformanceEntries=function(t){var n=this;if(Nr&&Nr.performance&&Nr.performance.getEntries&&Ct){ot.log("[Tracing] Adding & adjusting spans using Performance API");var r,i,e,o=rr(Ct);if(Nr.document)for(var u=0;u<document.scripts.length;u++)if("true"===document.scripts[u].dataset.entry){r=document.scripts[u].src;break}if(Nr.performance.getEntries().slice(this.Tn).forEach(function(u){var a=rr(u.startTime),s=rr(u.duration);if(!("navigation"===t.op&&o+a<t.startTimestamp))switch(u.entryType){case"navigation":!function(t,n,r){Ir(t,n,"unloadEvent",r),Ir(t,n,"redirect",r),Ir(t,n,"domContentLoadedEvent",r),Ir(t,n,"loadEvent",r),Ir(t,n,"connect",r),Ir(t,n,"secureConnection",r,"connectEnd"),Ir(t,n,"fetch",r,"domainLookupStart"),Ir(t,n,"domainLookup",r),function(t,n,r){Mr(t,{op:"browser",description:"request",startTimestamp:r+rr(n.requestStart),endTimestamp:r+rr(n.responseEnd)}),Mr(t,{op:"browser",description:"response",startTimestamp:r+rr(n.responseStart),endTimestamp:r+rr(n.responseEnd)})}(t,n,r)}(t,u,o);break;case"mark":case"paint":case"measure":var c=function(t,n,r,i,e){var o=e+r,u=o+i;return Mr(t,{description:n.name,endTimestamp:u,op:n.entryType,startTimestamp:o}),o}(t,u,a,s,o);void 0===e&&"sentry-tracing-init"===u.name&&(e=c);var f=xr(),h=u.startTime<f.timeStamp;"first-paint"===u.name&&h&&(ot.log("[Measurements] Adding FP"),n.en.fp={value:u.startTime},n.en["mark.fp"]={value:c}),"first-contentful-paint"===u.name&&h&&(ot.log("[Measurements] Adding FCP"),n.en.fcp={value:u.startTime},n.en["mark.fcp"]={value:c});break;case"resource":var v=u.name.replace(window.location.origin,""),d=function(t,n,r,i,e,o){if("xmlhttprequest"===n.initiatorType||"fetch"===n.initiatorType)return;var u={};"transferSize"in n&&(u["Transfer Size"]=n.transferSize);"encodedBodySize"in n&&(u["Encoded Body Size"]=n.encodedBodySize);"decodedBodySize"in n&&(u["Decoded Body Size"]=n.decodedBodySize);var a=o+i,s=a+e;return Mr(t,{description:r,endTimestamp:s,op:n.initiatorType?"resource."+n.initiatorType:"resource",startTimestamp:a,data:u}),s}(t,u,v,a,s,o);void 0===i&&(r||"").indexOf(v)>-1&&(i=d)}}),void 0!==i&&void 0!==e&&Mr(t,{description:"evaluation",endTimestamp:e,op:"script",startTimestamp:i}),this.Tn=Math.max(performance.getEntries().length-1,0),this.xn(t),"pageload"===t.op){var a=rr(Ct);["fcp","fp","lcp","ttfb"].forEach(function(r){if(n.en[r]&&!(a>=t.startTimestamp)){var i=n.en[r].value,e=a+rr(i),o=Math.abs(1e3*(e-t.startTimestamp)),u=o-i;ot.log("[Measurements] Normalized "+r+" from "+i+" to "+o+" ("+u+")"),n.en[r].value=o}}),this.en["mark.fid"]&&this.en.fid&&Mr(t,{description:"first input delay",endTimestamp:this.en["mark.fid"].value+rr(this.en.fid.value),op:"web.vitals",startTimestamp:this.en["mark.fid"].value}),t.setMeasurements(this.en)}}},t.prototype._n=function(){var t=this;!function(t,n){void 0===n&&(n=!1);var r,i=wr("CLS",0),e=function(t){t.hadRecentInput||(i.value+=t.value,i.entries.push(t),r())},o=Tr("layout-shift",e);o&&(r=br(t,i,o,n),kr(function(t){var n=t.isUnloading;o.takeRecords().map(e),n&&(i.isFinal=!0),r()}))}(function(n){n.entries.pop()&&(ot.log("[Measurements] Adding CLS"),t.en.cls={value:n.value})})},t.prototype.xn=function(t){var n=Nr.navigator;if(n){var r=n.connection;r&&(r.effectiveType&&t.setTag("effectiveConnectionType",r.effectiveType),r.type&&t.setTag("connectionType",r.type),Cr(r.rtt)&&(this.en["connection.rtt"]={value:r.rtt}),Cr(r.downlink)&&(this.en["connection.downlink"]={value:r.downlink})),Cr(n.deviceMemory)&&t.setTag("deviceMemory",String(n.deviceMemory)),Cr(n.hardwareConcurrency)&&t.setTag("hardwareConcurrency",String(n.hardwareConcurrency))}},t.prototype.En=function(){var t=this;jr(function(n){var r=n.entries.pop();if(r){var i=rr(performance.timeOrigin),e=rr(r.startTime);ot.log("[Measurements] Adding LCP"),t.en.lcp={value:n.value},t.en["mark.lcp"]={value:i+e}}})},t.prototype.Sn=function(){var t,n,r,i,e,o,u=this;t=function(t){var n=t.entries.pop();if(n){var r=rr(performance.timeOrigin),i=rr(n.startTime);ot.log("[Measurements] Adding FID"),u.en.fid={value:t.value},u.en["mark.fid"]={value:r+i}}},n=wr("FID"),r=xr(),e=Tr("first-input",i=function(t){t.startTime<r.timeStamp&&(n.value=t.processingStart-t.startTime,n.entries.push(t),n.isFinal=!0,o())}),o=br(t,n,e),e?kr(function(){e.takeRecords().map(i),e.disconnect()},!0):window.perfMetrics&&window.perfMetrics.onFirstInputDelay&&window.perfMetrics.onFirstInputDelay(function(t,i){i.timeStamp<r.timeStamp&&(n.value=t,n.isFinal=!0,n.entries=[{entryType:"first-input",name:i.type,target:i.target,cancelable:i.cancelable,startTime:i.timeStamp,processingStart:i.timeStamp+t}],o())})},t.prototype.kn=function(){var t=this;Dr(function(n){var r,i=n.entries.pop();if(i){ot.log("[Measurements] Adding TTFB"),t.en.ttfb={value:n.value};var e=n.value-(r=n.entries[0],null!=r?r:i).requestStart;t.en["ttfb.requestTime"]={value:e}}})},t}();function Ir(t,n,r,i,e){var o=e?n[e]:n[r+"End"],u=n[r+"Start"];u&&o&&Mr(t,{op:"browser",description:r,startTimestamp:i+rr(u),endTimestamp:i+rr(o)})}function Mr(t,n){var r=n.startTimestamp,i=c(n,["startTimestamp"]);return r&&t.startTimestamp>r&&(t.startTimestamp=r),t.startChild(s({startTimestamp:r},i))}function Cr(t){return"number"==typeof t&&isFinite(t)}var Ar={traceFetch:!0,traceXHR:!0,tracingOrigins:["localhost",/^\//]};function Lr(t){var n=s(s({},Ar),t),r=n.traceFetch,i=n.traceXHR,e=n.tracingOrigins,o=n.shouldCreateSpanForRequest,u={},a=function(t){if(u[t])return u[t];var n=e;return u[t]=n.some(function(n){return C(t,n)})&&!C(t,"sentry_key"),u[t]},c=a;"function"==typeof o&&(c=function(t){return a(t)&&o(t)});var f={};r&&lt({callback:function(t){!function(t,n,r){var i,e=null===(i=Jt().getClient())||void 0===i?void 0:i.getOptions();if(!(e&&tr(e)&&t.fetchData&&n(t.fetchData.url)))return;if(t.endTimestamp&&t.fetchData.__span){var o=r[t.fetchData.__span];if(o){var u=t.response;u&&o.setHttpStatus(u.status),o.finish(),delete r[t.fetchData.__span]}return}var a=nr();if(a){var o=a.startChild({data:s(s({},t.fetchData),{type:"fetch"}),description:t.fetchData.method+" "+t.fetchData.url,op:"http"});t.fetchData.__span=o.spanId,r[o.spanId]=o;var c=t.args[0]=t.args[0],f=t.args[1]=t.args[1]||{},h=f.headers;_(c,Request)&&(h=c.headers),h?"function"==typeof h.append?h.append("sentry-trace",o.toTraceparent()):h=Array.isArray(h)?v(h,[["sentry-trace",o.toTraceparent()]]):s(s({},h),{"sentry-trace":o.toTraceparent()}):h={"sentry-trace":o.toTraceparent()},f.headers=h}}(t,c,f)},type:"fetch"}),i&&lt({callback:function(t){!function(t,n,r){var i,e=null===(i=Jt().getClient())||void 0===i?void 0:i.getOptions();if(!e||!tr(e)||!(t.xhr&&t.xhr.__sentry_xhr__&&n(t.xhr.__sentry_xhr__.url))||t.xhr.__sentry_own_request__)return;var o=t.xhr.__sentry_xhr__;if(t.endTimestamp&&t.xhr.__sentry_xhr_span_id__){var u=r[t.xhr.__sentry_xhr_span_id__];return void(u&&(u.setHttpStatus(o.status_code),u.finish(),delete r[t.xhr.__sentry_xhr_span_id__]))}var a=nr();if(a){var u=a.startChild({data:s(s({},o.data),{type:"xhr",method:o.method,url:o.url}),description:o.method+" "+o.url,op:"http"});if(t.xhr.__sentry_xhr_span_id__=u.spanId,r[t.xhr.__sentry_xhr_span_id__]=u,t.xhr.setRequestHeader)try{t.xhr.setRequestHeader("sentry-trace",u.toTraceparent())}catch(t){}}}(t,c,f)},type:"xhr"})}var qr=z();var Fr=s({idleTimeout:ar,markBackgroundTransactions:!0,maxTransactionDuration:600,routingInstrumentation:function(t,n,r){if(void 0===n&&(n=!0),void 0===r&&(r=!0),qr&&qr.location){var i,e=qr.location.href;n&&(i=t({name:qr.location.pathname,op:"pageload"})),r&&lt({callback:function(n){var r=n.to,o=n.from;void 0===o&&e&&-1!==e.indexOf(r)?e=void 0:o!==r&&(e=void 0,i&&(ot.log("[Tracing] Finishing current transaction with op: "+i.op),i.finish()),i=t({name:qr.location.pathname,op:"navigation"}))},type:"history"})}else ot.warn("Could not initialize routing instrumentation due to invalid location")},startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0},Ar),Ur=function(){function t(n){this.name=t.id,this.jn=new Rr,this.On=!1;var r=Ar.tracingOrigins;n&&n.tracingOrigins&&Array.isArray(n.tracingOrigins)&&0!==n.tracingOrigins.length?r=n.tracingOrigins:this.On=!0,this.options=s(s(s({},Fr),n),{tracingOrigins:r})}return t.prototype.setupOnce=function(t,n){var r=this;this.Dn=n,this.On&&(ot.warn("[Tracing] You need to define `tracingOrigins` in the options. Set an array of urls or patterns to trace."),ot.warn("[Tracing] We added a reasonable default for you: "+Ar.tracingOrigins));var i=this.options,e=i.routingInstrumentation,o=i.startTransactionOnLocationChange,u=i.startTransactionOnPageLoad,a=i.markBackgroundTransactions,s=i.traceFetch,c=i.traceXHR,f=i.tracingOrigins,h=i.shouldCreateSpanForRequest;e(function(t){return r.Nn(t)},u,o),a&&(mr&&mr.document?mr.document.addEventListener("visibilitychange",function(){var t=nr();mr.document.hidden&&t&&(ot.log("[Tracing] Transaction: "+Yn.Cancelled+" -> since tab moved to the background, op: "+t.op),t.status||t.setStatus(Yn.Cancelled),t.setTag("visibilitychange","document.hidden"),t.finish())}):ot.warn("[Tracing] Could not set up background tab detection due to lack of global document")),Lr({traceFetch:s,traceXHR:c,tracingOrigins:f,shouldCreateSpanForRequest:h})},t.prototype.Nn=function(t){var n=this;if(this.Dn){var r=this.options,i=r.beforeNavigate,e=r.idleTimeout,o=r.maxTransactionDuration,u="pageload"===t.op?function(){var t=(n="sentry-trace",r=document.querySelector("meta[name="+n+"]"),r?r.getAttribute("content"):null);var n,r;if(t)return function(t){var n=t.match(Zn);if(n){var r=void 0;return"1"===n[3]?r=!0:"0"===n[3]&&(r=!1),{traceId:n[1],parentSampled:r,parentSpanId:n[2]}}}(t);return}():void 0,a=s(s(s({},t),u),{trimEnd:!0}),c="function"==typeof i?i(a):a,f=void 0===c?s(s({},a),{sampled:!1}):c;!1===f.sampled&&ot.log("[Tracing] Will not send "+f.op+" transaction because of beforeNavigate.");var h=function(t,n,r,i){return vr(t,new cr(n,t,r,i),dr(n))}(this.Dn(),f,e,!0);return ot.log("[Tracing] Starting "+f.op+" transaction on scope"),h.registerBeforeFinishCallback(function(t,r){n.jn.addPerformanceEntries(t),function(t,n,r){var i=r-n.startTimestamp;r&&(i>t||i<0)&&(n.setStatus(Yn.DeadlineExceeded),n.setTag("maxTransactionDurationExceeded","true"))}(1e3*o,t,r)}),h}ot.warn("[Tracing] Did not create "+t.op+" transaction because _getCurrentHub is invalid.")},t.id="BrowserTracing",t}();var Pr={},Hr=z();Hr.Sentry&&Hr.Sentry.Integrations&&(Pr=Hr.Sentry.Integrations);var Br=s(s(s({},Pr),Qn),{BrowserTracing:Ur});return pr(),t.BrowserClient=$n,t.Hub=Pt,t.Integrations=Br,t.SDK_NAME=Wn,t.SDK_VERSION="5.30.0",t.Scope=At,t.Span=or,t.Transports=Nn,t.addBreadcrumb=function(t){zt("addBreadcrumb",t)},t.addExtensionMethods=pr,t.addGlobalEventProcessor=qt,t.captureEvent=function(t){return zt("captureEvent",t)},t.captureException=captureException,t.captureMessage=function(t,n){var r;try{throw new Error(t)}catch(t){r=t}return zt("captureMessage",t,"string"==typeof n?n:void 0,s({originalException:t,syntheticException:r},"string"!=typeof n?{captureContext:n}:void 0))},t.close=function(t){var n=Jt().getClient();return n?n.close(t):jt.reject(!1)},t.configureScope=function(t){zt("configureScope",t)},t.defaultIntegrations=zn,t.flush=function(t){var n=Jt().getClient();return n?n.flush(t):jt.reject(!1)},t.forceLoad=function(){},t.getCurrentHub=Jt,t.getHubFromCarrier=Wt,t.init=function(t){if(void 0===t&&(t={}),void 0===t.defaultIntegrations&&(t.defaultIntegrations=zn),void 0===t.release){var n=z();n.SENTRY_RELEASE&&n.SENTRY_RELEASE.id&&(t.release=n.SENTRY_RELEASE.id)}void 0===t.autoSessionTracking&&(t.autoSessionTracking=!1),function(t,n){!0===n.debug&&ot.enable();var r=Jt(),i=new t(n);r.bindClient(i)}($n,t),t.autoSessionTracking&&function(){var t=z(),n=Jt(),r="complete"===document.readyState,i=!1,e=function(){i&&r&&n.endSession()},o=function(){r=!0,e(),t.removeEventListener("load",o)};n.startSession(),r||t.addEventListener("load",o);try{var u=new PerformanceObserver(function(t,n){t.getEntries().forEach(function(t){"first-contentful-paint"===t.name&&t.startTime<a&&(n.disconnect(),i=!0,e())})}),a="hidden"===document.visibilityState?0:1/0;document.addEventListener("visibilitychange",function(t){a=Math.min(a,t.timeStamp)},{once:!0}),u.observe({type:"paint",buffered:!0})}catch(t){i=!0,e()}}()},t.lastEventId=function(){return Jt().lastEventId()},t.onLoad=function(t){t()},t.setContext=function(t,n){zt("setContext",t,n)},t.setExtra=function(t,n){zt("setExtra",t,n)},t.setExtras=function(t){zt("setExtras",t)},t.setTag=function(t,n){zt("setTag",t,n)},t.setTags=function(t){zt("setTags",t)},t.setUser=function(t){zt("setUser",t)},t.showReportDialog=function(t){void 0===t&&(t={}),t.eventId||(t.eventId=Jt().lastEventId());var n=Jt().getClient();n&&n.showReportDialog(t)},t.startTransaction=function(t,n){return zt("startTransaction",s({},t),n)},t.withScope=Vt,t.wrap=function(t){return Cn(t)()},t}({});
//# sourceMappingURL=bundle.tracing.min.js.map
