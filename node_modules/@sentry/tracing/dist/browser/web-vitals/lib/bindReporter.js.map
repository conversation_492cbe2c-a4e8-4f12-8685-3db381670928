{"version": 3, "file": "bindReporter.js", "sourceRoot": "", "sources": ["../../../../src/browser/web-vitals/lib/bindReporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAIU,QAAA,YAAY,GAAG,UAC1B,QAAuB,EACvB,MAAc,EACd,EAAmC,EACnC,iBAA2B;IAE3B,IAAI,SAAiB,CAAC;IACtB,OAAO;QACL,IAAI,EAAE,IAAI,MAAM,CAAC,OAAO,EAAE;YACxB,EAAE,CAAC,UAAU,EAAE,CAAC;SACjB;QACD,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE;YACrB,IAAI,iBAAiB,IAAI,MAAM,CAAC,OAAO,IAAI,QAAQ,CAAC,eAAe,KAAK,QAAQ,EAAE;gBAChF,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;gBAE/C,kEAAkE;gBAClE,sEAAsE;gBACtE,+DAA+D;gBAC/D,4DAA4D;gBAC5D,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,IAAI,SAAS,KAAK,SAAS,EAAE;oBAC7D,QAAQ,CAAC,MAAM,CAAC,CAAC;oBACjB,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;iBAC1B;aACF;SACF;IACH,CAAC,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Metric, ReportHandler } from '../types';\n\nexport const bindReporter = (\n  callback: ReportHandler,\n  metric: Metric,\n  po: PerformanceObserver | undefined,\n  observeAllUpdates?: boolean,\n): (() => void) => {\n  let prevValue: number;\n  return () => {\n    if (po && metric.isFinal) {\n      po.disconnect();\n    }\n    if (metric.value >= 0) {\n      if (observeAllUpdates || metric.isFinal || document.visibilityState === 'hidden') {\n        metric.delta = metric.value - (prevValue || 0);\n\n        // Report the metric if there's a non-zero delta, if the metric is\n        // final, or if no previous value exists (which can happen in the case\n        // of the document becoming hidden when the metric value is 0).\n        // See: https://github.com/GoogleChrome/web-vitals/issues/14\n        if (metric.delta || metric.isFinal || prevValue === undefined) {\n          callback(metric);\n          prevValue = metric.value;\n        }\n      }\n    }\n  };\n};\n"]}