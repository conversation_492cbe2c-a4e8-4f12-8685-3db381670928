{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,2CAcyB;AAbvB,kCAAA,aAAa,CAAA;AACb,qCAAA,gBAAgB,CAAA;AAChB,iCAAA,YAAY,CAAA;AACZ,mCAAA,cAAc,CAAA;AACd,mCAAA,cAAc,CAAA;AACd,qCAAA,gBAAgB,CAAA;AAChB,+BAAA,UAAU,CAAA;AACV,6BAAA,QAAQ,CAAA;AACR,8BAAA,SAAS,CAAA;AACT,2BAAA,MAAM,CAAA;AACN,4BAAA,OAAO,CAAA;AACP,4BAAA,OAAO,CAAA;AACP,8BAAA,SAAS,CAAA;AAEX,mCAA8G;AAArG,wCAAA,uBAAuB,CAAA;AAAE,8BAAA,aAAa,CAAA;AAAE,kCAAA,iBAAiB,CAAA;AAAE,oBAAA,GAAG,CAAA;AAAE,yBAAA,QAAQ,CAAA;AAAE,sBAAA,KAAK,CAAA;AACxF,6BAA4B;AAAnB,oBAAA,GAAG,CAAA;AACZ,2CAA0C;AAAjC,kCAAA,UAAU,CAAA;AACnB,6CAA0D;AAAnC,oCAAA,WAAW,CAAA;AAClC,qCAAyE;AAAhE,yCAAA,oBAAoB,CAAA;AAAE,2CAAA,sBAAsB,CAAA;AACrD,6BAAiD;AAAxC,4BAAA,WAAW,CAAA;AACpB,0CAAkD;AAAzC,+BAAA,aAAa,CAAA;AAEtB,6CAA+C;AAEtC,oCAAY", "sourcesContent": ["export {\n  addBreadcrumb,\n  captureException,\n  captureEvent,\n  captureMessage,\n  configureScope,\n  startTransaction,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  withScope,\n} from '@sentry/minimal';\nexport { addGlobalEventProcessor, getCurrentHub, getHubFromCarrier, Hub, makeMain, Scope } from '@sentry/hub';\nexport { API } from './api';\nexport { BaseClient } from './baseclient';\nexport { BackendClass, BaseBackend } from './basebackend';\nexport { eventToSentryRequest, sessionToSentryRequest } from './request';\nexport { initAndBind, ClientClass } from './sdk';\nexport { NoopTransport } from './transports/noop';\n\nimport * as Integrations from './integrations';\n\nexport { Integrations };\n"]}