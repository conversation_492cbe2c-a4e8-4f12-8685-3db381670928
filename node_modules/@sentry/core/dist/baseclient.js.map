{"version": 3, "file": "baseclient.js", "sourceRoot": "", "sources": ["../src/baseclient.ts"], "names": [], "mappings": ";;AAAA,8BAA8B;AAC9B,mCAA6C;AAC7C,uCASuB;AACvB,uCAWuB;AAGvB,6CAAoE;AAEpE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH;IAoBE;;;;;OAKG;IACH,oBAAsB,YAAgC,EAAE,OAAU;QAZlE,kCAAkC;QACxB,kBAAa,GAAqB,EAAE,CAAC;QAE/C,qCAAqC;QAC3B,gBAAW,GAAW,CAAC,CAAC;QAShC,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,IAAI,GAAG,IAAI,WAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAClC;IACH,CAAC;IAED;;OAEG;IACH,iHAAiH;IAC1G,qCAAgB,GAAvB,UAAwB,SAAc,EAAE,IAAgB,EAAE,KAAa;QAAvE,iBAaC;QAZC,IAAI,OAAO,GAAuB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC;QAExD,IAAI,CAAC,QAAQ,CACX,IAAI,CAAC,WAAW,EAAE;aACf,kBAAkB,CAAC,SAAS,EAAE,IAAI,CAAC;aACnC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,EAAtC,CAAsC,CAAC;aACrD,IAAI,CAAC,UAAA,MAAM;YACV,OAAO,GAAG,MAAM,CAAC;QACnB,CAAC,CAAC,CACL,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,mCAAc,GAArB,UAAsB,OAAe,EAAE,KAAgB,EAAE,IAAgB,EAAE,KAAa;QAAxF,iBAgBC;QAfC,IAAI,OAAO,GAAuB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC;QAExD,IAAM,aAAa,GAAG,mBAAW,CAAC,OAAO,CAAC;YACxC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;YACnE,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEzD,IAAI,CAAC,QAAQ,CACX,aAAa;aACV,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,EAAtC,CAAsC,CAAC;aACrD,IAAI,CAAC,UAAA,MAAM;YACV,OAAO,GAAG,MAAM,CAAC;QACnB,CAAC,CAAC,CACL,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,iCAAY,GAAnB,UAAoB,KAAY,EAAE,IAAgB,EAAE,KAAa;QAC/D,IAAI,OAAO,GAAuB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC;QAExD,IAAI,CAAC,QAAQ,CACX,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;YAChD,OAAO,GAAG,MAAM,CAAC;QACnB,CAAC,CAAC,CACH,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,mCAAc,GAArB,UAAsB,OAAgB;QACpC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACpB,cAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;SAC7D;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SAC5B;IACH,CAAC;IAED;;OAEG;IACI,2BAAM,GAAb;QACE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,+BAAU,GAAjB;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,0BAAK,GAAZ,UAAa,OAAgB;QAA7B,iBAOC;QANC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAA,KAAK;YACjD,OAAO,KAAI,CAAC,WAAW,EAAE;iBACtB,YAAY,EAAE;iBACd,KAAK,CAAC,OAAO,CAAC;iBACd,IAAI,CAAC,UAAA,gBAAgB,IAAI,OAAA,KAAK,IAAI,gBAAgB,EAAzB,CAAyB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,0BAAK,GAAZ,UAAa,OAAgB;QAA7B,iBAKC;QAJC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;YACpC,KAAI,CAAC,UAAU,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC;YAClC,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,sCAAiB,GAAxB;QACE,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,+BAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACvD;IACH,CAAC;IAED;;OAEG;IACI,mCAAc,GAArB,UAA6C,WAAgC;QAC3E,IAAI;YACF,OAAQ,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,CAAO,IAAI,IAAI,CAAC;SAC1D;QAAC,OAAO,GAAG,EAAE;YACZ,cAAM,CAAC,IAAI,CAAC,iCAA+B,WAAW,CAAC,EAAE,6BAA0B,CAAC,CAAC;YACrF,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED,2DAA2D;IACjD,4CAAuB,GAAjC,UAAkC,OAAgB,EAAE,KAAY;;QAC9D,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,SAAS,CAAC;QACd,IAAM,UAAU,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC;QAE7D,IAAI,UAAU,EAAE;YACd,OAAO,GAAG,IAAI,CAAC;;gBAEf,KAAiB,IAAA,eAAA,iBAAA,UAAU,CAAA,sCAAA,8DAAE;oBAAxB,IAAM,EAAE,uBAAA;oBACX,IAAM,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;oBAC/B,IAAI,SAAS,IAAI,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;wBAC5C,OAAO,GAAG,IAAI,CAAC;wBACf,MAAM;qBACP;iBACF;;;;;;;;;SACF;QAED,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACtB,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3D,KAAK,IAAM,GAAG,IAAI,OAAO,EAAE;gBACzB,IAAI,GAAG,CAAC,WAAW,EAAE,KAAK,YAAY,EAAE;oBACtC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;oBACzB,MAAM;iBACP;aACF;SACF;QAED,OAAO,CAAC,MAAM,uCACT,CAAC,OAAO,IAAI,EAAE,MAAM,EAAE,qBAAa,CAAC,OAAO,EAAE,CAAC,KACjD,IAAI,MAAA;YACJ,SAAS,WAAA,EACT,MAAM,EAAE,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,IACnD,CAAC;IACL,CAAC;IAED,yCAAyC;IAC/B,iCAAY,GAAtB,UAAuB,OAAgB;QACrC,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,uDAAuD;IAC7C,wCAAmB,GAA7B,UAA8B,OAAgB;QAA9C,iBAkBC;QAjBC,OAAO,IAAI,mBAAW,CAAC,UAAA,OAAO;YAC5B,IAAI,MAAM,GAAW,CAAC,CAAC;YACvB,IAAM,IAAI,GAAW,CAAC,CAAC;YAEvB,IAAM,QAAQ,GAAG,WAAW,CAAC;gBAC3B,IAAI,KAAI,CAAC,WAAW,IAAI,CAAC,EAAE;oBACzB,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO,CAAC,IAAI,CAAC,CAAC;iBACf;qBAAM;oBACL,MAAM,IAAI,IAAI,CAAC;oBACf,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO,EAAE;wBAChC,aAAa,CAAC,QAAQ,CAAC,CAAC;wBACxB,OAAO,CAAC,KAAK,CAAC,CAAC;qBAChB;iBACF;YACH,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mCAAmC;IACzB,gCAAW,GAArB;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,yEAAyE;IAC/D,+BAAU,GAApB;QACE,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC;IACxE,CAAC;IAED;;;;;;;;;;;;;OAaG;IACO,kCAAa,GAAvB,UAAwB,KAAY,EAAE,KAAa,EAAE,IAAgB;QAArE,iBAkCC;QAjCS,IAAA,qCAAkB,EAAlB,uCAAkB,CAAuB;QACjD,IAAM,QAAQ,yCACT,KAAK,KACR,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAK,EAAE,CAAC,EAC7E,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,8BAAsB,EAAE,GACvD,CAAC;QAEF,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACnC,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;QAE1C,8EAA8E;QAC9E,6FAA6F;QAC7F,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YAC/B,UAAU,GAAG,WAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAClE;QAED,oDAAoD;QACpD,IAAI,MAAM,GAAG,mBAAW,CAAC,OAAO,CAAe,QAAQ,CAAC,CAAC;QAEzD,2DAA2D;QAC3D,kEAAkE;QAClE,IAAI,UAAU,EAAE;YACd,wCAAwC;YACxC,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAClD;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,UAAA,GAAG;YACpB,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,cAAc,GAAG,CAAC,EAAE;gBAC5D,OAAO,KAAI,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;aAClD;YACD,OAAO,GAAG,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACO,oCAAe,GAAzB,UAA0B,KAAmB,EAAE,KAAa;QAC1D,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,IAAI,CAAC;SACb;QAED,IAAM,UAAU,4FACX,KAAK,GACL,CAAC,KAAK,CAAC,WAAW,IAAI;YACvB,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,uCACnC,CAAC,GACD,CAAC,CAAC,CAAC,IAAI,IAAI;gBACZ,IAAI,EAAE,iBAAS,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC;aAC/B,CAAC,EACF,EALsC,CAKtC,CAAC;SACJ,CAAC,GACC,CAAC,KAAK,CAAC,IAAI,IAAI;YAChB,IAAI,EAAE,iBAAS,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;SACnC,CAAC,GACC,CAAC,KAAK,CAAC,QAAQ,IAAI;YACpB,QAAQ,EAAE,iBAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC;SAC3C,CAAC,GACC,CAAC,KAAK,CAAC,KAAK,IAAI;YACjB,KAAK,EAAE,iBAAS,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC;SACrC,CAAC,CACH,CAAC;QACF,0EAA0E;QAC1E,mEAAmE;QACnE,yEAAyE;QACzE,uCAAuC;QACvC,2EAA2E;QAC3E,yEAAyE;QACzE,kDAAkD;QAClD,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE;YAC1C,sEAAsE;YACtE,UAAU,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;SAClD;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;OAKG;IACO,wCAAmB,GAA7B,UAA8B,KAAY;QACxC,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,IAAA,iCAAW,EAAE,yBAAO,EAAE,mBAAI,EAAE,2BAAoB,EAApB,yCAAoB,CAAa;QAErE,IAAI,CAAC,CAAC,aAAa,IAAI,KAAK,CAAC,EAAE;YAC7B,KAAK,CAAC,WAAW,GAAG,aAAa,IAAI,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC;SAC3E;QAED,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,SAAS,EAAE;YACxD,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;SACzB;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;YAClD,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;SACnB;QAED,IAAI,KAAK,CAAC,OAAO,EAAE;YACjB,KAAK,CAAC,OAAO,GAAG,gBAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;SACzD;QAED,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACzF,IAAI,SAAS,IAAI,SAAS,CAAC,KAAK,EAAE;YAChC,SAAS,CAAC,KAAK,GAAG,gBAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;SAC7D;QAED,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE;YAC1B,OAAO,CAAC,GAAG,GAAG,gBAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;SACrD;IACH,CAAC;IAED;;;OAGG;IACO,+CAA0B,GAApC,UAAqC,KAAY;QAC/C,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC;QAC1B,IAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1D,IAAI,OAAO,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3C,OAAO,CAAC,YAAY,GAAG,iBAAiB,CAAC;SAC1C;IACH,CAAC;IAED;;;OAGG;IACO,+BAAU,GAApB,UAAqB,KAAY;QAC/B,IAAI,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACO,kCAAa,GAAvB,UAAwB,KAAY,EAAE,IAAgB,EAAE,KAAa;QACnE,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAChD,UAAA,UAAU;YACR,OAAO,UAAU,CAAC,QAAQ,CAAC;QAC7B,CAAC,EACD,UAAA,MAAM;YACJ,cAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACrB,OAAO,SAAS,CAAC;QACnB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACO,kCAAa,GAAvB,UAAwB,KAAY,EAAE,IAAgB,EAAE,KAAa;QAArE,iBAwEC;QAvEC,6DAA6D;QACvD,IAAA,sBAA8C,EAA5C,0BAAU,EAAE,0BAAgC,CAAC;QAErD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACtB,OAAO,mBAAW,CAAC,MAAM,CAAC,IAAI,mBAAW,CAAC,uCAAuC,CAAC,CAAC,CAAC;SACrF;QAED,IAAM,aAAa,GAAG,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC;QACnD,+BAA+B;QAC/B,6BAA6B;QAC7B,kDAAkD;QAClD,IAAI,CAAC,aAAa,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,EAAE;YAClF,OAAO,mBAAW,CAAC,MAAM,CACvB,IAAI,mBAAW,CACb,sFAAoF,UAAU,MAAG,CAClG,CACF,CAAC;SACH;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;aAC1C,IAAI,CAAC,UAAA,QAAQ;YACZ,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACrB,MAAM,IAAI,mBAAW,CAAC,wDAAwD,CAAC,CAAC;aACjF;YAED,IAAM,mBAAmB,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,IAAK,IAAI,CAAC,IAAgC,CAAC,UAAU,KAAK,IAAI,CAAC;YAC5G,IAAI,mBAAmB,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACvD,OAAO,QAAQ,CAAC;aACjB;YAED,IAAM,gBAAgB,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACpD,IAAI,OAAO,gBAAgB,KAAK,WAAW,EAAE;gBAC3C,MAAM,IAAI,mBAAW,CAAC,4DAA4D,CAAC,CAAC;aACrF;iBAAM,IAAI,kBAAU,CAAC,gBAAgB,CAAC,EAAE;gBACvC,OAAQ,gBAA8C,CAAC,IAAI,CACzD,UAAA,KAAK,IAAI,OAAA,KAAK,EAAL,CAAK,EACd,UAAA,CAAC;oBACC,MAAM,IAAI,mBAAW,CAAC,8BAA4B,CAAG,CAAC,CAAC;gBACzD,CAAC,CACF,CAAC;aACH;YACD,OAAO,gBAAgB,CAAC;QAC1B,CAAC,CAAC;aACD,IAAI,CAAC,UAAA,cAAc;YAClB,IAAI,cAAc,KAAK,IAAI,EAAE;gBAC3B,MAAM,IAAI,mBAAW,CAAC,oDAAoD,CAAC,CAAC;aAC7E;YAED,IAAM,OAAO,GAAG,KAAK,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YAChE,IAAI,CAAC,aAAa,IAAI,OAAO,EAAE;gBAC7B,KAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;aACvD;YAED,KAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;YAChC,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,EAAE,UAAA,MAAM;YAChB,IAAI,MAAM,YAAY,mBAAW,EAAE;gBACjC,MAAM,MAAM,CAAC;aACd;YAED,KAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;gBAC5B,IAAI,EAAE;oBACJ,UAAU,EAAE,IAAI;iBACjB;gBACD,iBAAiB,EAAE,MAAe;aACnC,CAAC,CAAC;YACH,MAAM,IAAI,mBAAW,CACnB,gIAA8H,MAAQ,CACvI,CAAC;QACJ,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACO,6BAAQ,GAAlB,UAAsB,OAAuB;QAA7C,iBAYC;QAXC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC;QACtB,OAAO,CAAC,IAAI,CACV,UAAA,KAAK;YACH,KAAI,CAAC,WAAW,IAAI,CAAC,CAAC;YACtB,OAAO,KAAK,CAAC;QACf,CAAC,EACD,UAAA,MAAM;YACJ,KAAI,CAAC,WAAW,IAAI,CAAC,CAAC;YACtB,OAAO,MAAM,CAAC;QAChB,CAAC,CACF,CAAC;IACJ,CAAC;IACH,iBAAC;AAAD,CAAC,AA7fD,IA6fC;AA7fqB,gCAAU", "sourcesContent": ["/* eslint-disable max-lines */\nimport { Scope, Session } from '@sentry/hub';\nimport {\n  Client,\n  Event,\n  EventHint,\n  Integration,\n  IntegrationClass,\n  Options,\n  SessionStatus,\n  Severity,\n} from '@sentry/types';\nimport {\n  dateTimestampInSeconds,\n  Dsn,\n  isPrimitive,\n  isThenable,\n  logger,\n  normalize,\n  SentryError,\n  SyncPromise,\n  truncate,\n  uuid4,\n} from '@sentry/utils';\n\nimport { Backend, BackendClass } from './basebackend';\nimport { IntegrationIndex, setupIntegrations } from './integration';\n\n/**\n * Base implementation for all JavaScript SDK clients.\n *\n * Call the constructor with the corresponding backend constructor and options\n * specific to the client subclass. To access these options later, use\n * {@link Client.getOptions}. Also, the Backend instance is available via\n * {@link Client.getBackend}.\n *\n * If a Dsn is specified in the options, it will be parsed and stored. Use\n * {@link Client.getDsn} to retrieve the Dsn at any moment. In case the Dsn is\n * invalid, the constructor will throw a {@link SentryException}. Note that\n * without a valid Dsn, the SDK will not send any events to Sentry.\n *\n * Before sending an event via the backend, it is passed through\n * {@link BaseClient.prepareEvent} to add SDK information and scope data\n * (breadcrumbs and context). To add more custom information, override this\n * method and extend the resulting prepared event.\n *\n * To issue automatically created events (e.g. via instrumentation), use\n * {@link Client.captureEvent}. It will prepare the event and pass it through\n * the callback lifecycle. To issue auto-breadcrumbs, use\n * {@link Client.addBreadcrumb}.\n *\n * @example\n * class NodeClient extends BaseClient<NodeBackend, NodeOptions> {\n *   public constructor(options: NodeOptions) {\n *     super(NodeBackend, options);\n *   }\n *\n *   // ...\n * }\n */\nexport abstract class BaseClient<B extends Backend, O extends Options> implements Client<O> {\n  /**\n   * The backend used to physically interact in the environment. Usually, this\n   * will correspond to the client. When composing SDKs, however, the Backend\n   * from the root SDK will be used.\n   */\n  protected readonly _backend: B;\n\n  /** Options passed to the SDK. */\n  protected readonly _options: O;\n\n  /** The client Dsn, if specified in options. Without this Dsn, the SDK will be disabled. */\n  protected readonly _dsn?: Dsn;\n\n  /** Array of used integrations. */\n  protected _integrations: IntegrationIndex = {};\n\n  /** Number of call being processed */\n  protected _processing: number = 0;\n\n  /**\n   * Initializes this client instance.\n   *\n   * @param backendClass A constructor function to create the backend.\n   * @param options Options for the client.\n   */\n  protected constructor(backendClass: BackendClass<B, O>, options: O) {\n    this._backend = new backendClass(options);\n    this._options = options;\n\n    if (options.dsn) {\n      this._dsn = new Dsn(options.dsn);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint, scope?: Scope): string | undefined {\n    let eventId: string | undefined = hint && hint.event_id;\n\n    this._process(\n      this._getBackend()\n        .eventFromException(exception, hint)\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(message: string, level?: Severity, hint?: EventHint, scope?: Scope): string | undefined {\n    let eventId: string | undefined = hint && hint.event_id;\n\n    const promisedEvent = isPrimitive(message)\n      ? this._getBackend().eventFromMessage(String(message), level, hint)\n      : this._getBackend().eventFromException(message, hint);\n\n    this._process(\n      promisedEvent\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint, scope?: Scope): string | undefined {\n    let eventId: string | undefined = hint && hint.event_id;\n\n    this._process(\n      this._captureEvent(event, hint, scope).then(result => {\n        eventId = result;\n      }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureSession(session: Session): void {\n    if (!session.release) {\n      logger.warn('Discarded session because of missing release');\n    } else {\n      this._sendSession(session);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getDsn(): Dsn | undefined {\n    return this._dsn;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getOptions(): O {\n    return this._options;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public flush(timeout?: number): PromiseLike<boolean> {\n    return this._isClientProcessing(timeout).then(ready => {\n      return this._getBackend()\n        .getTransport()\n        .close(timeout)\n        .then(transportFlushed => ready && transportFlushed);\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    return this.flush(timeout).then(result => {\n      this.getOptions().enabled = false;\n      return result;\n    });\n  }\n\n  /**\n   * Sets up the integrations\n   */\n  public setupIntegrations(): void {\n    if (this._isEnabled()) {\n      this._integrations = setupIntegrations(this._options);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    try {\n      return (this._integrations[integration.id] as T) || null;\n    } catch (_oO) {\n      logger.warn(`Cannot retrieve integration ${integration.id} from the current Client`);\n      return null;\n    }\n  }\n\n  /** Updates existing session based on the provided event */\n  protected _updateSessionFromEvent(session: Session, event: Event): void {\n    let crashed = false;\n    let errored = false;\n    let userAgent;\n    const exceptions = event.exception && event.exception.values;\n\n    if (exceptions) {\n      errored = true;\n\n      for (const ex of exceptions) {\n        const mechanism = ex.mechanism;\n        if (mechanism && mechanism.handled === false) {\n          crashed = true;\n          break;\n        }\n      }\n    }\n\n    const user = event.user;\n    if (!session.userAgent) {\n      const headers = event.request ? event.request.headers : {};\n      for (const key in headers) {\n        if (key.toLowerCase() === 'user-agent') {\n          userAgent = headers[key];\n          break;\n        }\n      }\n    }\n\n    session.update({\n      ...(crashed && { status: SessionStatus.Crashed }),\n      user,\n      userAgent,\n      errors: session.errors + Number(errored || crashed),\n    });\n  }\n\n  /** Deliver captured session to Sentry */\n  protected _sendSession(session: Session): void {\n    this._getBackend().sendSession(session);\n  }\n\n  /** Waits for the client to be done with processing. */\n  protected _isClientProcessing(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise(resolve => {\n      let ticked: number = 0;\n      const tick: number = 1;\n\n      const interval = setInterval(() => {\n        if (this._processing == 0) {\n          clearInterval(interval);\n          resolve(true);\n        } else {\n          ticked += tick;\n          if (timeout && ticked >= timeout) {\n            clearInterval(interval);\n            resolve(false);\n          }\n        }\n      }, tick);\n    });\n  }\n\n  /** Returns the current backend. */\n  protected _getBackend(): B {\n    return this._backend;\n  }\n\n  /** Determines whether this SDK is enabled and a valid Dsn is present. */\n  protected _isEnabled(): boolean {\n    return this.getOptions().enabled !== false && this._dsn !== undefined;\n  }\n\n  /**\n   * Adds common information to events.\n   *\n   * The information includes release and environment from `options`,\n   * breadcrumbs and context (extra, tags and user) from the scope.\n   *\n   * Information that is already present in the event is never overwritten. For\n   * nested objects, such as the context, keys are merged.\n   *\n   * @param event The original event.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A new event with more information.\n   */\n  protected _prepareEvent(event: Event, scope?: Scope, hint?: EventHint): PromiseLike<Event | null> {\n    const { normalizeDepth = 3 } = this.getOptions();\n    const prepared: Event = {\n      ...event,\n      event_id: event.event_id || (hint && hint.event_id ? hint.event_id : uuid4()),\n      timestamp: event.timestamp || dateTimestampInSeconds(),\n    };\n\n    this._applyClientOptions(prepared);\n    this._applyIntegrationsMetadata(prepared);\n\n    // If we have scope given to us, use it as the base for further modifications.\n    // This allows us to prevent unnecessary copying of data if `captureContext` is not provided.\n    let finalScope = scope;\n    if (hint && hint.captureContext) {\n      finalScope = Scope.clone(finalScope).update(hint.captureContext);\n    }\n\n    // We prepare the result here with a resolved Event.\n    let result = SyncPromise.resolve<Event | null>(prepared);\n\n    // This should be the last thing called, since we want that\n    // {@link Hub.addEventProcessor} gets the finished prepared event.\n    if (finalScope) {\n      // In case we have a hub we reassign it.\n      result = finalScope.applyToEvent(prepared, hint);\n    }\n\n    return result.then(evt => {\n      if (typeof normalizeDepth === 'number' && normalizeDepth > 0) {\n        return this._normalizeEvent(evt, normalizeDepth);\n      }\n      return evt;\n    });\n  }\n\n  /**\n   * Applies `normalize` function on necessary `Event` attributes to make them safe for serialization.\n   * Normalized keys:\n   * - `breadcrumbs.data`\n   * - `user`\n   * - `contexts`\n   * - `extra`\n   * @param event Event\n   * @returns Normalized event\n   */\n  protected _normalizeEvent(event: Event | null, depth: number): Event | null {\n    if (!event) {\n      return null;\n    }\n\n    const normalized = {\n      ...event,\n      ...(event.breadcrumbs && {\n        breadcrumbs: event.breadcrumbs.map(b => ({\n          ...b,\n          ...(b.data && {\n            data: normalize(b.data, depth),\n          }),\n        })),\n      }),\n      ...(event.user && {\n        user: normalize(event.user, depth),\n      }),\n      ...(event.contexts && {\n        contexts: normalize(event.contexts, depth),\n      }),\n      ...(event.extra && {\n        extra: normalize(event.extra, depth),\n      }),\n    };\n    // event.contexts.trace stores information about a Transaction. Similarly,\n    // event.spans[] stores information about child Spans. Given that a\n    // Transaction is conceptually a Span, normalization should apply to both\n    // Transactions and Spans consistently.\n    // For now the decision is to skip normalization of Transactions and Spans,\n    // so this block overwrites the normalized event to add back the original\n    // Transaction information prior to normalization.\n    if (event.contexts && event.contexts.trace) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      normalized.contexts.trace = event.contexts.trace;\n    }\n    return normalized;\n  }\n\n  /**\n   *  Enhances event using the client configuration.\n   *  It takes care of all \"static\" values like environment, release and `dist`,\n   *  as well as truncating overly long values.\n   * @param event event instance to be enhanced\n   */\n  protected _applyClientOptions(event: Event): void {\n    const options = this.getOptions();\n    const { environment, release, dist, maxValueLength = 250 } = options;\n\n    if (!('environment' in event)) {\n      event.environment = 'environment' in options ? environment : 'production';\n    }\n\n    if (event.release === undefined && release !== undefined) {\n      event.release = release;\n    }\n\n    if (event.dist === undefined && dist !== undefined) {\n      event.dist = dist;\n    }\n\n    if (event.message) {\n      event.message = truncate(event.message, maxValueLength);\n    }\n\n    const exception = event.exception && event.exception.values && event.exception.values[0];\n    if (exception && exception.value) {\n      exception.value = truncate(exception.value, maxValueLength);\n    }\n\n    const request = event.request;\n    if (request && request.url) {\n      request.url = truncate(request.url, maxValueLength);\n    }\n  }\n\n  /**\n   * This function adds all used integrations to the SDK info in the event.\n   * @param sdkInfo The sdkInfo of the event that will be filled with all integrations.\n   */\n  protected _applyIntegrationsMetadata(event: Event): void {\n    const sdkInfo = event.sdk;\n    const integrationsArray = Object.keys(this._integrations);\n    if (sdkInfo && integrationsArray.length > 0) {\n      sdkInfo.integrations = integrationsArray;\n    }\n  }\n\n  /**\n   * Tells the backend to send this event\n   * @param event The Sentry event to send\n   */\n  protected _sendEvent(event: Event): void {\n    this._getBackend().sendEvent(event);\n  }\n\n  /**\n   * Processes the event and logs an error in case of rejection\n   * @param event\n   * @param hint\n   * @param scope\n   */\n  protected _captureEvent(event: Event, hint?: EventHint, scope?: Scope): PromiseLike<string | undefined> {\n    return this._processEvent(event, hint, scope).then(\n      finalEvent => {\n        return finalEvent.event_id;\n      },\n      reason => {\n        logger.error(reason);\n        return undefined;\n      },\n    );\n  }\n\n  /**\n   * Processes an event (either error or message) and sends it to Sentry.\n   *\n   * This also adds breadcrumbs and context information to the event. However,\n   * platform specific meta data (such as the User's IP address) must be added\n   * by the SDK implementor.\n   *\n   *\n   * @param event The event to send to Sentry.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A SyncPromise that resolves with the event or rejects in case event was/will not be send.\n   */\n  protected _processEvent(event: Event, hint?: EventHint, scope?: Scope): PromiseLike<Event> {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const { beforeSend, sampleRate } = this.getOptions();\n\n    if (!this._isEnabled()) {\n      return SyncPromise.reject(new SentryError('SDK not enabled, will not send event.'));\n    }\n\n    const isTransaction = event.type === 'transaction';\n    // 1.0 === 100% events are sent\n    // 0.0 === 0% events are sent\n    // Sampling for transaction happens somewhere else\n    if (!isTransaction && typeof sampleRate === 'number' && Math.random() > sampleRate) {\n      return SyncPromise.reject(\n        new SentryError(\n          `Discarding event because it's not included in the random sample (sampling rate = ${sampleRate})`,\n        ),\n      );\n    }\n\n    return this._prepareEvent(event, scope, hint)\n      .then(prepared => {\n        if (prepared === null) {\n          throw new SentryError('An event processor returned null, will not send event.');\n        }\n\n        const isInternalException = hint && hint.data && (hint.data as { __sentry__: boolean }).__sentry__ === true;\n        if (isInternalException || isTransaction || !beforeSend) {\n          return prepared;\n        }\n\n        const beforeSendResult = beforeSend(prepared, hint);\n        if (typeof beforeSendResult === 'undefined') {\n          throw new SentryError('`beforeSend` method has to return `null` or a valid event.');\n        } else if (isThenable(beforeSendResult)) {\n          return (beforeSendResult as PromiseLike<Event | null>).then(\n            event => event,\n            e => {\n              throw new SentryError(`beforeSend rejected with ${e}`);\n            },\n          );\n        }\n        return beforeSendResult;\n      })\n      .then(processedEvent => {\n        if (processedEvent === null) {\n          throw new SentryError('`beforeSend` returned `null`, will not send event.');\n        }\n\n        const session = scope && scope.getSession && scope.getSession();\n        if (!isTransaction && session) {\n          this._updateSessionFromEvent(session, processedEvent);\n        }\n\n        this._sendEvent(processedEvent);\n        return processedEvent;\n      })\n      .then(null, reason => {\n        if (reason instanceof SentryError) {\n          throw reason;\n        }\n\n        this.captureException(reason, {\n          data: {\n            __sentry__: true,\n          },\n          originalException: reason as Error,\n        });\n        throw new SentryError(\n          `Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\\nReason: ${reason}`,\n        );\n      });\n  }\n\n  /**\n   * Occupies the client with processing and event\n   */\n  protected _process<T>(promise: PromiseLike<T>): void {\n    this._processing += 1;\n    promise.then(\n      value => {\n        this._processing -= 1;\n        return value;\n      },\n      reason => {\n        this._processing -= 1;\n        return reason;\n      },\n    );\n  }\n}\n"]}