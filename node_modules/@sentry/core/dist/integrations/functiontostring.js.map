{"version": 3, "file": "functiontostring.js", "sourceRoot": "", "sources": ["../../src/integrations/functiontostring.ts"], "names": [], "mappings": ";AAEA,IAAI,wBAAoC,CAAC;AAEzC,uEAAuE;AACvE;IAAA;QAME;;WAEG;QACI,SAAI,GAAW,gBAAgB,CAAC,EAAE,CAAC;IAe5C,CAAC;IAbC;;OAEG;IACI,oCAAS,GAAhB;QACE,6DAA6D;QAC7D,wBAAwB,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEvD,8DAA8D;QAC9D,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG;YAAgC,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAC1E,IAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC;YACjD,OAAO,wBAAwB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC;IACJ,CAAC;IAtBD;;OAEG;IACW,mBAAE,GAAW,kBAAkB,CAAC;IAoBhD,uBAAC;CAAA,AAxBD,IAwBC;AAxBY,4CAAgB", "sourcesContent": ["import { Integration, WrappedFunction } from '@sentry/types';\n\nlet originalFunctionToString: () => void;\n\n/** Patch toString calls to return proper name for wrapped functions */\nexport class FunctionToString implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'FunctionToString';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = FunctionToString.id;\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    originalFunctionToString = Function.prototype.toString;\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    Function.prototype.toString = function(this: WrappedFunction, ...args: any[]): string {\n      const context = this.__sentry_original__ || this;\n      return originalFunctionToString.apply(context, args);\n    };\n  }\n}\n"]}