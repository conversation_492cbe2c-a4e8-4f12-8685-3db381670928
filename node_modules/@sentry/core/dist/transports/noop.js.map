{"version": 3, "file": "noop.js", "sourceRoot": "", "sources": ["../../src/transports/noop.ts"], "names": [], "mappings": ";AAAA,uCAAmE;AACnE,uCAA4C;AAE5C,qBAAqB;AACrB;IAAA;IAiBA,CAAC;IAhBC;;OAEG;IACI,iCAAS,GAAhB,UAAiB,CAAQ;QACvB,OAAO,mBAAW,CAAC,OAAO,CAAC;YACzB,MAAM,EAAE,qEAAqE;YAC7E,MAAM,EAAE,cAAM,CAAC,OAAO;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,6BAAK,GAAZ,UAAa,CAAU;QACrB,OAAO,mBAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IACH,oBAAC;AAAD,CAAC,AAjBD,IAiBC;AAjBY,sCAAa", "sourcesContent": ["import { Event, Response, Status, Transport } from '@sentry/types';\nimport { SyncPromise } from '@sentry/utils';\n\n/** Noop transport */\nexport class NoopTransport implements Transport {\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(_: Event): PromiseLike<Response> {\n    return SyncPromise.resolve({\n      reason: `NoopTransport: Event has been skipped because no Dsn is configured.`,\n      status: Status.Skipped,\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(_?: number): PromiseLike<boolean> {\n    return SyncPromise.resolve(true);\n  }\n}\n"]}