{"version": 3, "file": "api.js", "sourceRoot": "", "sources": ["../src/api.ts"], "names": [], "mappings": ";AACA,uCAA+C;AAE/C,IAAM,kBAAkB,GAAG,GAAG,CAAC;AAE/B,kEAAkE;AAClE;IAGE,mCAAmC;IACnC,aAA0B,GAAY;QAAZ,QAAG,GAAH,GAAG,CAAS;QACpC,IAAI,CAAC,UAAU,GAAG,IAAI,WAAG,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAED,8BAA8B;IACvB,oBAAM,GAAb;QACE,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,sEAAsE;IAC/D,gCAAkB,GAAzB;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,IAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAI,GAAG,CAAC,QAAQ,MAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,MAAI,GAAG,CAAC,IAAM,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5C,OAAU,QAAQ,UAAK,GAAG,CAAC,IAAI,GAAG,IAAI,IAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,MAAI,GAAG,CAAC,IAAM,CAAC,CAAC,CAAC,EAAE,WAAO,CAAC;IACjF,CAAC;IAED,sCAAsC;IAC/B,8BAAgB,GAAvB;QACE,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,gDAAkC,GAAzC;QACE,OAAU,IAAI,CAAC,gBAAgB,EAAE,SAAI,IAAI,CAAC,YAAY,EAAI,CAAC;IAC7D,CAAC;IAED;;;;OAIG;IACI,mDAAqC,GAA5C;QACE,OAAU,IAAI,CAAC,oBAAoB,EAAE,SAAI,IAAI,CAAC,YAAY,EAAI,CAAC;IACjE,CAAC;IAED,8DAA8D;IACvD,kCAAoB,GAA3B;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO,CAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,MAAI,GAAG,CAAC,IAAM,CAAC,CAAC,CAAC,EAAE,cAAQ,GAAG,CAAC,SAAS,YAAS,CAAC;IACzE,CAAC;IAED;;;OAGG;IACI,+BAAiB,GAAxB,UAAyB,UAAkB,EAAE,aAAqB;QAChE,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,IAAM,MAAM,GAAG,CAAC,2BAAyB,kBAAoB,CAAC,CAAC;QAC/D,MAAM,CAAC,IAAI,CAAC,mBAAiB,UAAU,SAAI,aAAe,CAAC,CAAC;QAC5D,MAAM,CAAC,IAAI,CAAC,gBAAc,GAAG,CAAC,IAAM,CAAC,CAAC;QACtC,IAAI,GAAG,CAAC,IAAI,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,mBAAiB,GAAG,CAAC,IAAM,CAAC,CAAC;SAC1C;QACD,OAAO;YACL,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;SACnC,CAAC;IACJ,CAAC;IAED,qDAAqD;IAC9C,qCAAuB,GAA9B,UACE,aAIM;QAJN,8BAAA,EAAA,kBAIM;QAEN,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,IAAM,QAAQ,GAAM,IAAI,CAAC,kBAAkB,EAAE,sBAAmB,CAAC;QAEjE,IAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,cAAc,CAAC,IAAI,CAAC,SAAO,GAAG,CAAC,QAAQ,EAAI,CAAC,CAAC;QAC7C,KAAK,IAAM,GAAG,IAAI,aAAa,EAAE;YAC/B,IAAI,GAAG,KAAK,KAAK,EAAE;gBACjB,SAAS;aACV;YAED,IAAI,GAAG,KAAK,MAAM,EAAE;gBAClB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;oBACvB,SAAS;iBACV;gBACD,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE;oBAC3B,cAAc,CAAC,IAAI,CAAC,UAAQ,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC,CAAC;iBAC5E;gBACD,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE;oBAC5B,cAAc,CAAC,IAAI,CAAC,WAAS,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC;iBAC9E;aACF;iBAAM;gBACL,cAAc,CAAC,IAAI,CAAI,kBAAkB,CAAC,GAAG,CAAC,SAAI,kBAAkB,CAAC,aAAa,CAAC,GAAG,CAAW,CAAG,CAAC,CAAC;aACvG;SACF;QACD,IAAI,cAAc,CAAC,MAAM,EAAE;YACzB,OAAU,QAAQ,SAAI,cAAc,CAAC,IAAI,CAAC,GAAG,CAAG,CAAC;SAClD;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,yCAAyC;IACjC,kCAAoB,GAA5B;QACE,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED,kDAAkD;IAC1C,gCAAkB,GAA1B,UAA2B,MAA4B;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACvC,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,OAAO,KAAG,IAAI,GAAG,GAAG,CAAC,SAAS,SAAI,MAAM,MAAG,CAAC;IAC9C,CAAC;IAED,iFAAiF;IACzE,0BAAY,GAApB;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,IAAM,IAAI,GAAG;YACX,4DAA4D;YAC5D,8DAA8D;YAC9D,UAAU,EAAE,GAAG,CAAC,IAAI;YACpB,cAAc,EAAE,kBAAkB;SACnC,CAAC;QACF,OAAO,iBAAS,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IACH,UAAC;AAAD,CAAC,AAlID,IAkIC;AAlIY,kBAAG", "sourcesContent": ["import { Dsn<PERSON><PERSON> } from '@sentry/types';\nimport { Dsn, urlEncode } from '@sentry/utils';\n\nconst SENTRY_API_VERSION = '7';\n\n/** Helper class to provide urls to different Sentry endpoints. */\nexport class API {\n  /** The internally used Dsn object. */\n  private readonly _dsnObject: Dsn;\n  /** Create a new instance of API */\n  public constructor(public dsn: DsnLike) {\n    this._dsnObject = new Dsn(dsn);\n  }\n\n  /** Returns the Dsn object. */\n  public getDsn(): Dsn {\n    return this._dsnObject;\n  }\n\n  /** Returns the prefix to construct Sentry ingestion API endpoints. */\n  public getBaseApiEndpoint(): string {\n    const dsn = this._dsnObject;\n    const protocol = dsn.protocol ? `${dsn.protocol}:` : '';\n    const port = dsn.port ? `:${dsn.port}` : '';\n    return `${protocol}//${dsn.host}${port}${dsn.path ? `/${dsn.path}` : ''}/api/`;\n  }\n\n  /** Returns the store endpoint URL. */\n  public getStoreEndpoint(): string {\n    return this._getIngestEndpoint('store');\n  }\n\n  /**\n   * Returns the store endpoint URL with auth in the query string.\n   *\n   * Sending auth as part of the query string and not as custom HTTP headers avoids CORS preflight requests.\n   */\n  public getStoreEndpointWithUrlEncodedAuth(): string {\n    return `${this.getStoreEndpoint()}?${this._encodedAuth()}`;\n  }\n\n  /**\n   * Returns the envelope endpoint URL with auth in the query string.\n   *\n   * Sending auth as part of the query string and not as custom HTTP headers avoids CORS preflight requests.\n   */\n  public getEnvelopeEndpointWithUrlEncodedAuth(): string {\n    return `${this._getEnvelopeEndpoint()}?${this._encodedAuth()}`;\n  }\n\n  /** Returns only the path component for the store endpoint. */\n  public getStoreEndpointPath(): string {\n    const dsn = this._dsnObject;\n    return `${dsn.path ? `/${dsn.path}` : ''}/api/${dsn.projectId}/store/`;\n  }\n\n  /**\n   * Returns an object that can be used in request headers.\n   * This is needed for node and the old /store endpoint in sentry\n   */\n  public getRequestHeaders(clientName: string, clientVersion: string): { [key: string]: string } {\n    const dsn = this._dsnObject;\n    const header = [`Sentry sentry_version=${SENTRY_API_VERSION}`];\n    header.push(`sentry_client=${clientName}/${clientVersion}`);\n    header.push(`sentry_key=${dsn.user}`);\n    if (dsn.pass) {\n      header.push(`sentry_secret=${dsn.pass}`);\n    }\n    return {\n      'Content-Type': 'application/json',\n      'X-Sentry-Auth': header.join(', '),\n    };\n  }\n\n  /** Returns the url to the report dialog endpoint. */\n  public getReportDialogEndpoint(\n    dialogOptions: {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      [key: string]: any;\n      user?: { name?: string; email?: string };\n    } = {},\n  ): string {\n    const dsn = this._dsnObject;\n    const endpoint = `${this.getBaseApiEndpoint()}embed/error-page/`;\n\n    const encodedOptions = [];\n    encodedOptions.push(`dsn=${dsn.toString()}`);\n    for (const key in dialogOptions) {\n      if (key === 'dsn') {\n        continue;\n      }\n\n      if (key === 'user') {\n        if (!dialogOptions.user) {\n          continue;\n        }\n        if (dialogOptions.user.name) {\n          encodedOptions.push(`name=${encodeURIComponent(dialogOptions.user.name)}`);\n        }\n        if (dialogOptions.user.email) {\n          encodedOptions.push(`email=${encodeURIComponent(dialogOptions.user.email)}`);\n        }\n      } else {\n        encodedOptions.push(`${encodeURIComponent(key)}=${encodeURIComponent(dialogOptions[key] as string)}`);\n      }\n    }\n    if (encodedOptions.length) {\n      return `${endpoint}?${encodedOptions.join('&')}`;\n    }\n\n    return endpoint;\n  }\n\n  /** Returns the envelope endpoint URL. */\n  private _getEnvelopeEndpoint(): string {\n    return this._getIngestEndpoint('envelope');\n  }\n\n  /** Returns the ingest API endpoint for target. */\n  private _getIngestEndpoint(target: 'store' | 'envelope'): string {\n    const base = this.getBaseApiEndpoint();\n    const dsn = this._dsnObject;\n    return `${base}${dsn.projectId}/${target}/`;\n  }\n\n  /** Returns a URL-encoded string with auth config suitable for a query string. */\n  private _encodedAuth(): string {\n    const dsn = this._dsnObject;\n    const auth = {\n      // We send only the minimum set of required information. See\n      // https://github.com/getsentry/sentry-javascript/issues/2572.\n      sentry_key: dsn.user,\n      sentry_version: SENTRY_API_VERSION,\n    };\n    return urlEncode(auth);\n  }\n}\n"]}