{"version": 3, "file": "baseclient.d.ts", "sourceRoot": "", "sources": ["../src/baseclient.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AAC7C,OAAO,EACL,MAAM,EACN,KAAK,EACL,SAAS,EACT,WAAW,EACX,gBAAgB,EAChB,OAAO,EAEP,QAAQ,EACT,MAAM,eAAe,CAAC;AACvB,OAAO,EAEL,GAAG,EASJ,MAAM,eAAe,CAAC;AAEvB,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AACtD,OAAO,EAAE,gBAAgB,EAAqB,MAAM,eAAe,CAAC;AAEpE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,8BAAsB,UAAU,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,OAAO,CAAE,YAAW,MAAM,CAAC,CAAC,CAAC;IACzF;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IAE/B,iCAAiC;IACjC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IAE/B,2FAA2F;IAC3F,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAE9B,kCAAkC;IAClC,SAAS,CAAC,aAAa,EAAE,gBAAgB,CAAM;IAE/C,qCAAqC;IACrC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAK;IAElC;;;;;OAKG;IACH,SAAS,aAAa,YAAY,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC;IASlE;;OAEG;IAEI,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS;IAe5F;;OAEG;IACI,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS;IAkB7G;;OAEG;IACI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS;IAYtF;;OAEG;IACI,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAQ7C;;OAEG;IACI,MAAM,IAAI,GAAG,GAAG,SAAS;IAIhC;;OAEG;IACI,UAAU,IAAI,CAAC;IAItB;;OAEG;IACI,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IASpD;;OAEG;IACI,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IAOpD;;OAEG;IACI,iBAAiB,IAAI,IAAI;IAMhC;;OAEG;IACI,cAAc,CAAC,CAAC,SAAS,WAAW,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI;IASxF,2DAA2D;IAC3D,SAAS,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI;IAqCvE,yCAAyC;IACzC,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAI9C,uDAAuD;IACvD,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IAoBrE,mCAAmC;IACnC,SAAS,CAAC,WAAW,IAAI,CAAC;IAI1B,yEAAyE;IACzE,SAAS,CAAC,UAAU,IAAI,OAAO;IAI/B;;;;;;;;;;;;;OAaG;IACH,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;IAoCjG;;;;;;;;;OASG;IACH,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI;IAuC3E;;;;;OAKG;IACH,SAAS,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IA+BjD;;;OAGG;IACH,SAAS,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAQxD;;;OAGG;IACH,SAAS,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAIxC;;;;;OAKG;IACH,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;IAYvG;;;;;;;;;;;;OAYG;IACH,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IA0E1F;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;CAarD"}