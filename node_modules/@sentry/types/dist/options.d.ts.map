{"version": 3, "file": "options.d.ts", "sourceRoot": "", "sources": ["../src/options.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAE1E,gDAAgD;AAChD,MAAM,WAAW,OAAO;IACtB;;OAEG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;IAEhB;;;;OAIG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;;OAGG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IAEb;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,KAAK,GAAG,WAAW,EAAE,CAAC;IAE5C;;;;OAIG;IACH,YAAY,CAAC,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,WAAW,EAAE,CAAC,CAAC;IAEhF;;;OAGG;IACH,YAAY,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAEtC;;OAEG;IACH,SAAS,CAAC,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;IAEtC;;OAEG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IAEpC;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB,uEAAuE;IACvE,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB,2CAA2C;IAC3C,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;;OAGG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB,oDAAoD;IACpD,QAAQ,CAAC,EAAE,QAAQ,CAAC;IAEpB,2DAA2D;IAC3D,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB,sEAAsE;IACtE,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAE3B,kFAAkF;IAClF,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;;;;;;OAQG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;;;;OAMG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB;;OAEG;IACH,YAAY,CAAC,EAAE;QACb,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;KACpB,CAAC;IAEF;;;;;;;;OAQG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAE1B;;;;;;;;;;;OAWG;IACH,aAAa,CAAC,CAAC,eAAe,EAAE,eAAe,GAAG,MAAM,GAAG,OAAO,CAAC;IAEnE;;;;;;;;;;;OAWG;IACH,UAAU,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;IAEtF;;;;;;;;;;OAUG;IACH,gBAAgB,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,cAAc,GAAG,UAAU,GAAG,IAAI,CAAC;CACrF"}