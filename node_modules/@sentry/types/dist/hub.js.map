{"version": 3, "file": "hub.js", "sourceRoot": "", "sources": ["../src/hub.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Breadcrumb, BreadcrumbHint } from './breadcrumb';\nimport { Client } from './client';\nimport { Event, EventHint } from './event';\nimport { Extra, Extras } from './extra';\nimport { Integration, IntegrationClass } from './integration';\nimport { Primitive } from './misc';\nimport { Scope } from './scope';\nimport { Session, SessionContext } from './session';\nimport { Severity } from './severity';\nimport { Span, SpanContext } from './span';\nimport { CustomSamplingContext, Transaction, TransactionContext } from './transaction';\nimport { User } from './user';\n\n/**\n * Internal class used to make sure we always have the latest internal functions\n * working in case we have a version conflict.\n */\nexport interface Hub {\n  /**\n   * Checks if this hub's version is older than the given version.\n   *\n   * @param version A version number to compare to.\n   * @return True if the given version is newer; otherwise false.\n   *\n   * @hidden\n   */\n  isOlderThan(version: number): boolean;\n\n  /**\n   * This binds the given client to the current scope.\n   * @param client An SDK client (client) instance.\n   */\n  bindClient(client?: Client): void;\n\n  /**\n   * Create a new scope to store context information.\n   *\n   * The scope will be layered on top of the current one. It is isolated, i.e. all\n   * breadcrumbs and context information added to this scope will be removed once\n   * the scope ends. Be sure to always remove this scope with {@link this.popScope}\n   * when the operation finishes or throws.\n   *\n   * @returns Scope, the new cloned scope\n   */\n  pushScope(): Scope;\n\n  /**\n   * Removes a previously pushed scope from the stack.\n   *\n   * This restores the state before the scope was pushed. All breadcrumbs and\n   * context information added since the last call to {@link this.pushScope} are\n   * discarded.\n   */\n  popScope(): boolean;\n\n  /**\n   * Creates a new scope with and executes the given operation within.\n   * The scope is automatically removed once the operation\n   * finishes or throws.\n   *\n   * This is essentially a convenience function for:\n   *\n   *     pushScope();\n   *     callback();\n   *     popScope();\n   *\n   * @param callback that will be enclosed into push/popScope.\n   */\n  withScope(callback: (scope: Scope) => void): void;\n\n  /** Returns the client of the top stack. */\n  getClient(): Client | undefined;\n\n  /**\n   * Captures an exception event and sends it to Sentry.\n   *\n   * @param exception An exception-like object.\n   * @param hint May contain additional information about the original exception.\n   * @returns The generated eventId.\n   */\n  captureException(exception: any, hint?: EventHint): string;\n\n  /**\n   * Captures a message event and sends it to Sentry.\n   *\n   * @param message The message to send to Sentry.\n   * @param level Define the level of the message.\n   * @param hint May contain additional information about the original exception.\n   * @returns The generated eventId.\n   */\n  captureMessage(message: string, level?: Severity, hint?: EventHint): string;\n\n  /**\n   * Captures a manually created event and sends it to Sentry.\n   *\n   * @param event The event to send to Sentry.\n   * @param hint May contain additional information about the original exception.\n   */\n  captureEvent(event: Event, hint?: EventHint): string;\n\n  /**\n   * This is the getter for lastEventId.\n   *\n   * @returns The last event id of a captured event.\n   */\n  lastEventId(): string | undefined;\n\n  /**\n   * Records a new breadcrumb which will be attached to future events.\n   *\n   * Breadcrumbs will be added to subsequent events to provide more context on\n   * user's actions prior to an error or crash.\n   *\n   * @param breadcrumb The breadcrumb to record.\n   * @param hint May contain additional information about the original breadcrumb.\n   */\n  addBreadcrumb(breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void;\n\n  /**\n   * Updates user context information for future events.\n   *\n   * @param user User context object to be set in the current context. Pass `null` to unset the user.\n   */\n  setUser(user: User | null): void;\n\n  /**\n   * Set an object that will be merged sent as tags data with the event.\n   *\n   * @param tags Tags context object to merge into current context.\n   */\n  setTags(tags: { [key: string]: Primitive }): void;\n\n  /**\n   * Set key:value that will be sent as tags data with the event.\n   *\n   * Can also be used to unset a tag, by passing `undefined`.\n   *\n   * @param key String key of tag\n   * @param value Value of tag\n   */\n  setTag(key: string, value: Primitive): void;\n\n  /**\n   * Set key:value that will be sent as extra data with the event.\n   * @param key String of extra\n   * @param extra Any kind of data. This data will be normalized.\n   */\n  setExtra(key: string, extra: Extra): void;\n\n  /**\n   * Set an object that will be merged sent as extra data with the event.\n   * @param extras Extras object to merge into current context.\n   */\n  setExtras(extras: Extras): void;\n\n  /**\n   * Sets context data with the given name.\n   * @param name of the context\n   * @param context Any kind of data. This data will be normalized.\n   */\n  setContext(name: string, context: { [key: string]: any } | null): void;\n\n  /**\n   * Callback to set context information onto the scope.\n   *\n   * @param callback Callback function that receives Scope.\n   */\n  configureScope(callback: (scope: Scope) => void): void;\n\n  /**\n   * For the duraction of the callback, this hub will be set as the global current Hub.\n   * This function is useful if you want to run your own client and hook into an already initialized one\n   * e.g.: Reporting issues to your own sentry when running in your component while still using the users configuration.\n   */\n  run(callback: (hub: Hub) => void): void;\n\n  /** Returns the integration if installed on the current client. */\n  getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null;\n\n  /** Returns all trace headers that are currently on the top scope. */\n  traceHeaders(): { [key: string]: string };\n\n  /**\n   * @deprecated No longer does anything. Use use {@link Transaction.startChild} instead.\n   */\n  startSpan(context: SpanContext): Span;\n\n  /**\n   * Starts a new `Transaction` and returns it. This is the entry point to manual tracing instrumentation.\n   *\n   * A tree structure can be built by adding child spans to the transaction, and child spans to other spans. To start a\n   * new child span within the transaction or any span, call the respective `.startChild()` method.\n   *\n   * Every child span must be finished before the transaction is finished, otherwise the unfinished spans are discarded.\n   *\n   * The transaction must be finished with a call to its `.finish()` method, at which point the transaction with all its\n   * finished child spans will be sent to Sentry.\n   *\n   * @param context Properties of the new `Transaction`.\n   * @param customSamplingContext Information given to the transaction sampling function (along with context-dependent\n   * default values). See {@link Options.tracesSampler}.\n   *\n   * @returns The transaction which was just started\n   */\n  startTransaction(context: TransactionContext, customSamplingContext?: CustomSamplingContext): Transaction;\n\n  /**\n   * Starts a new `Session`, sets on the current scope and returns it.\n   *\n   * To finish a `session`, it has to be passed directly to `client.captureSession`, which is done automatically\n   * when using `hub.endSession()` for the session currently stored on the scope.\n   *\n   * When there's already an existing session on the scope, it'll be automatically ended.\n   *\n   * @param context Optional properties of the new `Session`.\n   *\n   * @returns The session which was just started\n   */\n  startSession(context?: SessionContext): Session;\n\n  /**\n   * Ends the session that lives on the current scope and sends it to Sentry\n   */\n  endSession(): void;\n}\n"]}