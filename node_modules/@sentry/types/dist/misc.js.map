{"version": 3, "file": "misc.js", "sourceRoot": "", "sources": ["../src/misc.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Data extracted from an incoming request to a node server\n */\nexport interface ExtractedNodeRequestData {\n  [key: string]: any;\n\n  /** Specific headers from the request */\n  headers?: { [key: string]: string };\n\n  /**  The request's method */\n  method?: string;\n\n  /** The request's URL, including query string */\n  url?: string;\n\n  /** String representing the cookies sent along with the request */\n  cookies?: { [key: string]: string };\n\n  /** The request's query string, without the leading '?' */\n  query_string?: string;\n\n  /** Any data sent in the request's body, as a JSON string */\n  data?: string;\n}\n\n/**\n * Location object on a service worker's `self` object.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/WorkerLocation.\n */\nexport interface WorkerLocation {\n  /** The protocol scheme of the URL of the script executed in the Worker, including the final ':'. */\n  readonly protocol: string;\n\n  /** The host, that is the hostname, a ':', and the port of the URL of the script executed in the Worker. */\n  readonly host: string;\n\n  /** The domain of the URL of the script executed in the Worker. */\n  readonly hostname: string;\n\n  /** The canonical form of the origin of the specific location. */\n  readonly origin: string;\n\n  /** The port number of the URL of the script executed in the Worker. */\n  readonly port: string;\n\n  /** The path of the URL of the script executed in the Worker, beginning with a '/'. */\n  readonly pathname: string;\n\n  /** The parameters (query string) of the URL of the script executed in the Worker, beginning with a '?'. */\n  readonly search: string;\n\n  /** The fragment identifier of the URL of the script executed in the Worker, beginning with a '#'. */\n  readonly hash: string;\n\n  /** Stringifier that returns the whole URL of the script executed in the Worker. */\n  readonly href: string;\n\n  /** Synonym for `href` attribute */\n  toString(): string;\n}\n\nexport type Primitive = number | string | boolean | bigint | symbol | null | undefined;\n"]}