{"version": 3, "file": "hub.d.ts", "sourceRoot": "", "sources": ["../src/hub.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAC3C,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACnC,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,QAAQ,CAAC;AAC3C,OAAO,EAAE,qBAAqB,EAAE,WAAW,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAC;AACvF,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAE9B;;;GAGG;AACH,MAAM,WAAW,GAAG;IAClB;;;;;;;OAOG;IACH,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC;IAEtC;;;OAGG;IACH,UAAU,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElC;;;;;;;;;OASG;IACH,SAAS,IAAI,KAAK,CAAC;IAEnB;;;;;;OAMG;IACH,QAAQ,IAAI,OAAO,CAAC;IAEpB;;;;;;;;;;;;OAYG;IACH,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC;IAElD,2CAA2C;IAC3C,SAAS,IAAI,MAAM,GAAG,SAAS,CAAC;IAEhC;;;;;;OAMG;IACH,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAE3D;;;;;;;OAOG;IACH,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAE5E;;;;;OAKG;IACH,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAErD;;;;OAIG;IACH,WAAW,IAAI,MAAM,GAAG,SAAS,CAAC;IAElC;;;;;;;;OAQG;IACH,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,cAAc,GAAG,IAAI,CAAC;IAEnE;;;;OAIG;IACH,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IAEjC;;;;OAIG;IACH,OAAO,CAAC,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,IAAI,CAAC;IAElD;;;;;;;OAOG;IACH,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI,CAAC;IAE5C;;;;OAIG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC;IAE1C;;;OAGG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC;;;;OAIG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,GAAG,IAAI,GAAG,IAAI,CAAC;IAEvE;;;;OAIG;IACH,cAAc,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC;IAEvD;;;;OAIG;IACH,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,GAAG,IAAI,CAAC;IAExC,kEAAkE;IAClE,cAAc,CAAC,CAAC,SAAS,WAAW,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IAElF,qEAAqE;IACrE,YAAY,IAAI;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IAE1C;;OAEG;IACH,SAAS,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,CAAC;IAEtC;;;;;;;;;;;;;;;;OAgBG;IACH,gBAAgB,CAAC,OAAO,EAAE,kBAAkB,EAAE,qBAAqB,CAAC,EAAE,qBAAqB,GAAG,WAAW,CAAC;IAE1G;;;;;;;;;;;OAWG;IACH,YAAY,CAAC,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC;IAEhD;;OAEG;IACH,UAAU,IAAI,IAAI,CAAC;CACpB"}