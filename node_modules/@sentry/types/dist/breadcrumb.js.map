{"version": 3, "file": "breadcrumb.js", "sourceRoot": "", "sources": ["../src/breadcrumb.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Severity } from './severity';\n\n/** JSDoc */\nexport interface Breadcrumb {\n  type?: string;\n  level?: Severity;\n  event_id?: string;\n  category?: string;\n  message?: string;\n  data?: { [key: string]: any };\n  timestamp?: number;\n}\n\n/** JSDoc */\nexport interface BreadcrumbHint {\n  [key: string]: any;\n}\n"]}