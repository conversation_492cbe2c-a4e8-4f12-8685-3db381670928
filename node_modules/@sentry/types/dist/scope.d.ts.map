{"version": 3, "file": "scope.d.ts", "sourceRoot": "", "sources": ["../src/scope.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;AAC9C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACnC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAE9B,aAAa;AACb,oBAAY,cAAc,GAAG,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,CAAC;AAEvF,aAAa;AACb,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,IAAI,CAAC;IACX,KAAK,EAAE,QAAQ,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,QAAQ,CAAC;IACnB,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IACnC,WAAW,EAAE,MAAM,EAAE,CAAC;CACvB;AAED;;;GAGG;AACH,MAAM,WAAW,KAAK;IACpB,8EAA8E;IAC9E,iBAAiB,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI,CAAC;IAElD;;;;OAIG;IACH,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IAEjC;;OAEG;IACH,OAAO,IAAI,IAAI,GAAG,SAAS,CAAC;IAE5B;;;OAGG;IACH,OAAO,CAAC,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,IAAI,CAAC;IAElD;;;;;;;OAOG;IACH,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI,CAAC;IAE5C;;;OAGG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC;;;;OAIG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC;IAE1C;;;OAGG;IACH,cAAc,CAAC,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAE5C;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC;IAEhC;;OAEG;IACH,kBAAkB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAExC;;;;OAIG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;IAExD;;;OAGG;IACH,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC;IAE3B;;OAEG;IACH,OAAO,IAAI,IAAI,GAAG,SAAS,CAAC;IAE5B;;OAEG;IACH,cAAc,IAAI,WAAW,GAAG,SAAS,CAAC;IAE1C;;OAEG;IACH,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAEpC;;OAEG;IACH,UAAU,IAAI,OAAO,GAAG,SAAS,CAAC;IAElC;;;;;;OAMG;IACH,MAAM,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI,CAAC;IAE9C,0DAA0D;IAC1D,KAAK,IAAI,IAAI,CAAC;IAEd;;;;OAIG;IACH,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAErE;;OAEG;IACH,gBAAgB,IAAI,IAAI,CAAC;CAC1B"}