{"name": "@sentry/types", "version": "5.30.0", "description": "Types for all Sentry JavaScript SDKs", "repository": "git://github.com/getsentry/sentry-javascript.git", "homepage": "https://github.com/getsentry/sentry-javascript/tree/master/packages/types", "author": "Sentry", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=6"}, "main": "dist/index.js", "module": "esm/index.js", "types": "dist/index.d.ts", "publishConfig": {"access": "public"}, "devDependencies": {"@sentry-internal/eslint-config-sdk": "5.30.0", "eslint": "7.6.0", "npm-run-all": "^4.1.2", "prettier": "1.19.0", "typescript": "3.7.5"}, "scripts": {"build": "run-p build:es5 build:esm", "build:es5": "tsc -p tsconfig.build.json", "build:esm": "tsc -p tsconfig.esm.json", "build:watch": "run-p build:watch:es5 build:watch:esm", "build:watch:es5": "tsc -p tsconfig.build.json -w --preserveWatchOutput", "build:watch:esm": "tsc -p tsconfig.esm.json -w --preserveWatchOutput", "link:yarn": "yarn link", "lint": "run-s lint:prettier lint:eslint", "lint:prettier": "prettier --check \"{src,test}/**/*.ts\"", "lint:eslint": "eslint . --cache --cache-location '../../eslintcache/' --format stylish", "fix": "run-s fix:eslint fix:prettier", "fix:prettier": "prettier --write \"{src,test}/**/*.ts\"", "fix:eslint": "eslint . --format stylish --fix", "pack": "npm pack"}, "volta": {"extends": "../../package.json"}, "sideEffects": false}