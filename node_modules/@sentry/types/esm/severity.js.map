{"version": 3, "file": "severity.js", "sourceRoot": "", "sources": ["../src/severity.ts"], "names": [], "mappings": "AAAA,YAAY;AACZ,yCAAyC;AACzC,MAAM,CAAN,IAAY,QAeX;AAfD,WAAY,QAAQ;IAClB,YAAY;IACZ,2BAAe,CAAA;IACf,YAAY;IACZ,2BAAe,CAAA;IACf,YAAY;IACZ,+BAAmB,CAAA;IACnB,YAAY;IACZ,uBAAW,CAAA;IACX,YAAY;IACZ,yBAAa,CAAA;IACb,YAAY;IACZ,2BAAe,CAAA;IACf,YAAY;IACZ,iCAAqB,CAAA;AACvB,CAAC,EAfW,QAAQ,KAAR,QAAQ,QAenB;AAED,0EAA0E;AAC1E,WAAiB,QAAQ;IACvB;;;;;OAKG;IACH,SAAgB,UAAU,CAAC,KAAa;QACtC,QAAQ,KAAK,EAAE;YACb,KAAK,OAAO;gBACV,OAAO,QAAQ,CAAC,KAAK,CAAC;YACxB,KAAK,MAAM;gBACT,OAAO,QAAQ,CAAC,IAAI,CAAC;YACvB,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS;gBACZ,OAAO,QAAQ,CAAC,OAAO,CAAC;YAC1B,KAAK,OAAO;gBACV,OAAO,QAAQ,CAAC,KAAK,CAAC;YACxB,KAAK,OAAO;gBACV,OAAO,QAAQ,CAAC,KAAK,CAAC;YACxB,KAAK,UAAU;gBACb,OAAO,QAAQ,CAAC,QAAQ,CAAC;YAC3B,KAAK,KAAK,CAAC;YACX;gBACE,OAAO,QAAQ,CAAC,GAAG,CAAC;SACvB;IACH,CAAC;IAnBe,mBAAU,aAmBzB,CAAA;AACH,CAAC,EA3BgB,QAAQ,KAAR,QAAQ,QA2BxB", "sourcesContent": ["/** JSDoc */\n// eslint-disable-next-line import/export\nexport enum Severity {\n  /** JSDoc */\n  Fatal = 'fatal',\n  /** JSDoc */\n  Error = 'error',\n  /** JSDoc */\n  Warning = 'warning',\n  /** JSDoc */\n  Log = 'log',\n  /** JSDoc */\n  Info = 'info',\n  /** JSDoc */\n  Debug = 'debug',\n  /** JSDoc */\n  Critical = 'critical',\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace, import/export\nexport namespace Severity {\n  /**\n   * Converts a string-based level into a {@link Severity}.\n   *\n   * @param level string representation of Severity\n   * @returns Severity\n   */\n  export function fromString(level: string): Severity {\n    switch (level) {\n      case 'debug':\n        return Severity.Debug;\n      case 'info':\n        return Severity.Info;\n      case 'warn':\n      case 'warning':\n        return Severity.Warning;\n      case 'error':\n        return Severity.Error;\n      case 'fatal':\n        return Severity.Fatal;\n      case 'critical':\n        return Severity.Critical;\n      case 'log':\n      default:\n        return Severity.Log;\n    }\n  }\n}\n"]}