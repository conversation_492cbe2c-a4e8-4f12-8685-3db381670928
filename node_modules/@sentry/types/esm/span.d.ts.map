{"version": 3, "file": "span.d.ts", "sourceRoot": "", "sources": ["../src/span.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACnC,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,8EAA8E;AAC9E,MAAM,WAAW,WAAW;IAC1B;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,IAAI,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IAEpC;;OAEG;IACH,IAAI,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAC;IAE9B;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,qCAAqC;AACrC,MAAM,WAAW,IAAK,SAAQ,WAAW;IACvC;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,cAAc,EAAE,MAAM,CAAC;IAEvB;;OAEG;IACH,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IAEnC;;OAEG;IACH,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAC;IAE7B;;OAEG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAE1B;;;OAGG;IACH,MAAM,CAAC,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEpC;;;;;;;OAOG;IACH,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI,CAAC;IAE5C;;;;OAIG;IACH,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC;IAEvC;;;;OAIG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;IAExC;;;OAGG;IACH,KAAK,CACH,WAAW,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,WAAW,EAAE,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,cAAc,CAAC,CAAC,GAC7G,IAAI,CAAC;IAER;;;OAGG;IACH,UAAU,CACR,WAAW,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,WAAW,EAAE,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,cAAc,CAAC,CAAC,GAC7G,IAAI,CAAC;IAER;;OAEG;IACH,SAAS,IAAI,OAAO,CAAC;IAErB,oDAAoD;IACpD,aAAa,IAAI,MAAM,CAAC;IAExB,8DAA8D;IAC9D,eAAe,IAAI;QACjB,IAAI,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;SAAE,CAAC;QAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,EAAE,CAAC,EAAE,MAAM,CAAC;QACZ,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;SAAE,CAAC;QACpC,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;IACF,iCAAiC;IACjC,MAAM,IAAI;QACR,IAAI,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;SAAE,CAAC;QAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,EAAE,CAAC,EAAE,MAAM,CAAC;QACZ,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,CAAC;QACxB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;SAAE,CAAC;QACpC,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;CACH"}