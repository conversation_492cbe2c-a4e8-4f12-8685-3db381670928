{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Dsn } from './dsn';\nimport { Event, EventHint } from './event';\nimport { Integration, IntegrationClass } from './integration';\nimport { Options } from './options';\nimport { Scope } from './scope';\nimport { Session } from './session';\nimport { Severity } from './severity';\n\n/**\n * User-Facing Sentry SDK Client.\n *\n * This interface contains all methods to interface with the SDK once it has\n * been installed. It allows to send events to Sentry, record breadcrumbs and\n * set a context included in every event. Since the SDK mutates its environment,\n * there will only be one instance during runtime.\n *\n */\nexport interface Client<O extends Options = Options> {\n  /**\n   * Captures an exception event and sends it to Sentry.\n   *\n   * @param exception An exception-like object.\n   * @param hint May contain additional information about the original exception.\n   * @param scope An optional scope containing event metadata.\n   * @returns The event id\n   */\n  captureException(exception: any, hint?: EventHint, scope?: Scope): string | undefined;\n\n  /**\n   * Captures a message event and sends it to Sentry.\n   *\n   * @param message The message to send to Sentry.\n   * @param level Define the level of the message.\n   * @param hint May contain additional information about the original exception.\n   * @param scope An optional scope containing event metadata.\n   * @returns The event id\n   */\n  captureMessage(message: string, level?: Severity, hint?: EventHint, scope?: Scope): string | undefined;\n\n  /**\n   * Captures a manually created event and sends it to Sentry.\n   *\n   * @param event The event to send to Sentry.\n   * @param hint May contain additional information about the original exception.\n   * @param scope An optional scope containing event metadata.\n   * @returns The event id\n   */\n  captureEvent(event: Event, hint?: EventHint, scope?: Scope): string | undefined;\n\n  /** Captures a session\n   *\n   * @param session Session to be delivered\n   */\n  captureSession?(session: Session): void;\n\n  /** Returns the current Dsn. */\n  getDsn(): Dsn | undefined;\n\n  /** Returns the current options. */\n  getOptions(): O;\n\n  /**\n   * A promise that resolves when all current events have been sent.\n   * If you provide a timeout and the queue takes longer to drain the promise returns false.\n   *\n   * @param timeout Maximum time in ms the client should wait.\n   */\n  close(timeout?: number): PromiseLike<boolean>;\n\n  /**\n   * A promise that resolves when all current events have been sent.\n   * If you provide a timeout and the queue takes longer to drain the promise returns false.\n   *\n   * @param timeout Maximum time in ms the client should wait.\n   */\n  flush(timeout?: number): PromiseLike<boolean>;\n\n  /** Returns an array of installed integrations on the client. */\n  getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null;\n\n  /** This is an internal function to setup all integrations that should run on the client */\n  setupIntegrations(): void;\n}\n"]}