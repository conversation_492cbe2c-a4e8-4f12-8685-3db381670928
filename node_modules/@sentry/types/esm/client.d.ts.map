{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAC5B,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAEtC;;;;;;;;GAQG;AACH,MAAM,WAAW,MAAM,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO;IACjD;;;;;;;OAOG;IACH,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS,CAAC;IAEtF;;;;;;;;OAQG;IACH,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS,CAAC;IAEvG;;;;;;;OAOG;IACH,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS,CAAC;IAEhF;;;OAGG;IACH,cAAc,CAAC,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAExC,+BAA+B;IAC/B,MAAM,IAAI,GAAG,GAAG,SAAS,CAAC;IAE1B,mCAAmC;IACnC,UAAU,IAAI,CAAC,CAAC;IAEhB;;;;;OAKG;IACH,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAE9C;;;;;OAKG;IACH,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAE9C,gEAAgE;IAChE,cAAc,CAAC,CAAC,SAAS,WAAW,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IAElF,2FAA2F;IAC3F,iBAAiB,IAAI,IAAI,CAAC;CAC3B"}