{"version": 3, "file": "dsn.js", "sourceRoot": "", "sources": ["../src/dsn.ts"], "names": [], "mappings": "", "sourcesContent": ["/** Supported Sentry transport protocols in a Dsn. */\nexport type DsnProtocol = 'http' | 'https';\n\n/** Primitive components of a Dsn. */\nexport interface DsnComponents {\n  /** Protocol used to connect to Sentry. */\n  protocol: DsnProtocol;\n  /** Public authorization key. */\n  user: string;\n  /** Private authorization key (deprecated, optional). */\n  pass?: string;\n  /** Hostname of the Sentry instance. */\n  host: string;\n  /** Port of the Sentry instance. */\n  port?: string;\n  /** Sub path/ */\n  path?: string;\n  /** Project ID */\n  projectId: string;\n}\n\n/** Anything that can be parsed into a Dsn. */\nexport type DsnLike = string | DsnComponents;\n\n/** The Sentry Dsn, identifying a Sentry instance and project. */\nexport interface Dsn extends DsnComponents {\n  /**\n   * Renders the string representation of this Dsn.\n   *\n   * By default, this will render the public representation without the password\n   * component. To get the deprecated private representation, set `withPassword`\n   * to true.\n   *\n   * @param withPassword When set to true, the password will be included.\n   */\n  toString(withPassword: boolean): string;\n}\n"]}