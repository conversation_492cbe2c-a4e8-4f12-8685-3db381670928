{"version": 3, "file": "session.js", "sourceRoot": "", "sources": ["../src/session.ts"], "names": [], "mappings": "AAiDA;;GAEG;AACH,MAAM,CAAN,IAAY,aASX;AATD,WAAY,aAAa;IACvB,YAAY;IACZ,0BAAS,CAAA;IACT,YAAY;IACZ,kCAAiB,CAAA;IACjB,YAAY;IACZ,oCAAmB,CAAA;IACnB,YAAY;IACZ,sCAAqB,CAAA;AACvB,CAAC,EATW,aAAa,KAAb,aAAa,QASxB", "sourcesContent": ["import { User } from './user';\n\n/**\n * @inheritdoc\n */\nexport interface Session extends SessionContext {\n  /** JSDoc */\n  update(context?: SessionContext): void;\n\n  /** JSDoc */\n  close(status?: SessionStatus): void;\n\n  /** JSDoc */\n  toJSON(): {\n    init: boolean;\n    sid: string;\n    did?: string;\n    timestamp: string;\n    started: string;\n    duration: number;\n    status: SessionStatus;\n    errors: number;\n    attrs?: {\n      release?: string;\n      environment?: string;\n      user_agent?: string;\n      ip_address?: string;\n    };\n  };\n}\n\n/**\n * Session Context\n */\nexport interface SessionContext {\n  sid?: string;\n  did?: string;\n  timestamp?: number;\n  started?: number;\n  duration?: number;\n  status?: SessionStatus;\n  release?: string;\n  environment?: string;\n  userAgent?: string;\n  ipAddress?: string;\n  errors?: number;\n  user?: User | null;\n}\n\n/**\n * Session Status\n */\nexport enum SessionStatus {\n  /** JSDoc */\n  Ok = 'ok',\n  /** JSDoc */\n  Exited = 'exited',\n  /** JSDoc */\n  Crashed = 'crashed',\n  /** JSDoc */\n  Abnormal = 'abnormal',\n}\n"]}