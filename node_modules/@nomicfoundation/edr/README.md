# EDR - Ethereum Development Runtime

**EDR**, or **Ethereum Development Runtime** in full, is a library for creating developer tooling on top of the Ethereum Virtual Machine (EVM), such as an EVM debugger or state inspector. EDR provides a performant API, written in Rust, with bindings for the Node API (TypeScript).

At the moment, EDR is only meant to be consumed from [Hardhat](https://hardhat.org/), but we plan to have a stable API that can be used from any other tooling. If you are interested in this, please reach out.
