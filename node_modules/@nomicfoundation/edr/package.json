{"name": "@nomicfoundation/edr", "version": "0.11.3", "devDependencies": {"@napi-rs/cli": "^2.18.4", "@types/chai": "^4.2.0", "@types/chai-as-promised": "^7.1.8", "@types/mocha": ">=9.1.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "5.61.0", "@typescript-eslint/parser": "5.61.0", "chai": "^4.3.6", "chai-as-promised": "^7.1.1", "eslint": "^8.44.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-mocha": "10.4.1", "eslint-plugin-prettier": "5.2.1", "ethers": "^6.1.0", "json-stream-stringify": "^3.1.4", "mocha": "^10.0.0", "prettier": "^3.2.5", "ts-node": "^10.8.0", "typescript": "~5.0.0"}, "engines": {"node": ">= 18"}, "files": ["index.js", "index.d.ts", "Cargo.toml", "build.rs", "src/"], "license": "MIT", "main": "index.js", "napi": {"name": "edr", "triples": {"defaults": false, "additional": ["aarch64-apple-darwin", "x86_64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl", "x86_64-pc-windows-msvc"]}}, "repository": "NomicFoundation/edr.git", "types": "index.d.ts", "dependencies": {"@nomicfoundation/edr-darwin-arm64": "0.11.3", "@nomicfoundation/edr-darwin-x64": "0.11.3", "@nomicfoundation/edr-linux-arm64-gnu": "0.11.3", "@nomicfoundation/edr-linux-arm64-musl": "0.11.3", "@nomicfoundation/edr-linux-x64-gnu": "0.11.3", "@nomicfoundation/edr-linux-x64-musl": "0.11.3", "@nomicfoundation/edr-win32-x64-msvc": "0.11.3"}, "scripts": {"artifacts": "napi artifacts", "build": "napi build --platform --release --no-const-enum", "build:debug": "napi build --platform  --no-const-enum", "build:scenarios": "napi build --platform --release --no-const-enum --features scenarios", "build:tracing": "napi build --platform --release --no-const-enum --features tracing", "clean": "rm -rf @nomicfoundation/edr.node", "eslint": "eslint 'test/**/*.ts'", "lint": "pnpm run prettier && pnpm run eslint", "lint:fix": "pnpm run prettier --write", "pretest": "pnpm build", "prettier": "prettier --check \"test/**.ts\"", "test": "pnpm tsc && node --max-old-space-size=8192 node_modules/mocha/bin/_mocha --recursive \"test/**/*.ts\"", "testNoBuild": "pnpm tsc && node --max-old-space-size=8192 node_modules/mocha/bin/_mocha --recursive \"test/**/*.ts\"", "universal": "napi universal", "version": "napi version"}}