{"version": 3, "file": "index.mjs", "sources": ["src/get-innermost-scope.js", "src/find-variable.js", "src/token-predicate.js", "src/get-function-head-location.js", "src/get-static-value.js", "src/get-string-if-constant.js", "src/get-property-name.js", "src/get-function-name-with-kind.js", "src/has-side-effect.js", "src/is-parenthesized.js", "src/pattern-matcher.js", "src/reference-tracker.js", "src/index.js"], "sourcesContent": ["/**\n * Get the innermost scope which contains a given location.\n * @param {Scope} initialScope The initial scope to search.\n * @param {Node} node The location to search.\n * @returns {Scope} The innermost scope.\n */\nexport function getInnermostScope(initialScope, node) {\n    const location = node.range[0]\n\n    let scope = initialScope\n    let found = false\n    do {\n        found = false\n        for (const childScope of scope.childScopes) {\n            const range = childScope.block.range\n\n            if (range[0] <= location && location < range[1]) {\n                scope = childScope\n                found = true\n                break\n            }\n        }\n    } while (found)\n\n    return scope\n}\n", "import { getInnermostScope } from \"./get-innermost-scope\"\n\n/**\n * Find the variable of a given name.\n * @param {Scope} initialScope The scope to start finding.\n * @param {string|Node} nameOrNode The variable name to find. If this is a Node object then it should be an Identifier node.\n * @returns {Variable|null} The found variable or null.\n */\nexport function findVariable(initialScope, nameOrNode) {\n    let name = \"\"\n    let scope = initialScope\n\n    if (typeof nameOrNode === \"string\") {\n        name = nameOrNode\n    } else {\n        name = nameOrNode.name\n        scope = getInnermostScope(scope, nameOrNode)\n    }\n\n    while (scope != null) {\n        const variable = scope.set.get(name)\n        if (variable != null) {\n            return variable\n        }\n        scope = scope.upper\n    }\n\n    return null\n}\n", "/**\n * Negate the result of `this` calling.\n * @param {Token} token The token to check.\n * @returns {boolean} `true` if the result of `this(token)` is `false`.\n */\nfunction negate0(token) {\n    return !this(token) //eslint-disable-line no-invalid-this\n}\n\n/**\n * Creates the negate function of the given function.\n * @param {function(Token):boolean} f - The function to negate.\n * @returns {function(Token):boolean} Negated function.\n */\nfunction negate(f) {\n    return negate0.bind(f)\n}\n\n/**\n * Checks if the given token is an arrow token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an arrow token.\n */\nexport function isArrowToken(token) {\n    return token.value === \"=>\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a comma token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a comma token.\n */\nexport function isCommaToken(token) {\n    return token.value === \",\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a semicolon token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a semicolon token.\n */\nexport function isSemicolonToken(token) {\n    return token.value === \";\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a colon token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a colon token.\n */\nexport function isColonToken(token) {\n    return token.value === \":\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is an opening parenthesis token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an opening parenthesis token.\n */\nexport function isOpeningParenToken(token) {\n    return token.value === \"(\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a closing parenthesis token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a closing parenthesis token.\n */\nexport function isClosingParenToken(token) {\n    return token.value === \")\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is an opening square bracket token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an opening square bracket token.\n */\nexport function isOpeningBracketToken(token) {\n    return token.value === \"[\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a closing square bracket token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a closing square bracket token.\n */\nexport function isClosingBracketToken(token) {\n    return token.value === \"]\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is an opening brace token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an opening brace token.\n */\nexport function isOpeningBraceToken(token) {\n    return token.value === \"{\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a closing brace token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a closing brace token.\n */\nexport function isClosingBraceToken(token) {\n    return token.value === \"}\" && token.type === \"Punctuator\"\n}\n\n/**\n * Checks if the given token is a comment token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a comment token.\n */\nexport function isCommentToken(token) {\n    return (\n        token.type === \"Line\" ||\n        token.type === \"Block\" ||\n        token.type === \"Shebang\"\n    )\n}\n\nexport const isNotArrowToken = negate(isArrowToken)\nexport const isNotCommaToken = negate(isCommaToken)\nexport const isNotSemicolonToken = negate(isSemicolonToken)\nexport const isNotColonToken = negate(isColonToken)\nexport const isNotOpeningParenToken = negate(isOpeningParenToken)\nexport const isNotClosingParenToken = negate(isClosingParenToken)\nexport const isNotOpeningBracketToken = negate(isOpeningBracketToken)\nexport const isNotClosingBracketToken = negate(isClosingBracketToken)\nexport const isNotOpeningBraceToken = negate(isOpeningBraceToken)\nexport const isNotClosingBraceToken = negate(isClosingBraceToken)\nexport const isNotCommentToken = negate(isCommentToken)\n", "import { isArrowToken, isOpeningParenToken } from \"./token-predicate\"\n\n/**\n * Get the `(` token of the given function node.\n * @param {Node} node - The function node to get.\n * @param {SourceCode} sourceCode - The source code object to get tokens.\n * @returns {Token} `(` token.\n */\nfunction getOpeningParenOfParams(node, sourceCode) {\n    return node.id\n        ? sourceCode.getTokenAfter(node.id, isOpeningParenToken)\n        : sourceCode.getFirstToken(node, isOpeningParenToken)\n}\n\n/**\n * Get the location of the given function node for reporting.\n * @param {Node} node - The function node to get.\n * @param {SourceCode} sourceCode - The source code object to get tokens.\n * @returns {string} The location of the function node for reporting.\n */\nexport function getFunctionHeadLocation(node, sourceCode) {\n    const parent = node.parent\n    let start = null\n    let end = null\n\n    if (node.type === \"ArrowFunctionExpression\") {\n        const arrowToken = sourceCode.getTokenBefore(node.body, isArrowToken)\n\n        start = arrowToken.loc.start\n        end = arrowToken.loc.end\n    } else if (\n        parent.type === \"Property\" ||\n        parent.type === \"MethodDefinition\"\n    ) {\n        start = parent.loc.start\n        end = getOpeningParenOfParams(node, sourceCode).loc.start\n    } else {\n        start = node.loc.start\n        end = getOpeningParenOfParams(node, sourceCode).loc.start\n    }\n\n    return {\n        start: Object.assign({}, start),\n        end: Object.assign({}, end),\n    }\n}\n", "/* globals BigInt, globalThis, global, self, window */\n\nimport { findVariable } from \"./find-variable\"\n\nconst globalObject =\n    typeof globalThis !== \"undefined\"\n        ? globalThis\n        : typeof self !== \"undefined\"\n        ? self\n        : typeof window !== \"undefined\"\n        ? window\n        : typeof global !== \"undefined\"\n        ? global\n        : {}\n\nconst builtinNames = Object.freeze(\n    new Set([\n        \"Array\",\n        \"ArrayBuffer\",\n        \"BigInt\",\n        \"BigInt64Array\",\n        \"BigUint64Array\",\n        \"Boolean\",\n        \"DataView\",\n        \"Date\",\n        \"decodeURI\",\n        \"decodeURIComponent\",\n        \"encodeURI\",\n        \"encodeURIComponent\",\n        \"escape\",\n        \"Float32Array\",\n        \"Float64Array\",\n        \"Function\",\n        \"Infinity\",\n        \"Int16Array\",\n        \"Int32Array\",\n        \"Int8Array\",\n        \"isFinite\",\n        \"isNaN\",\n        \"isPrototypeOf\",\n        \"JSON\",\n        \"Map\",\n        \"Math\",\n        \"NaN\",\n        \"Number\",\n        \"Object\",\n        \"parseFloat\",\n        \"parseInt\",\n        \"Promise\",\n        \"Proxy\",\n        \"Reflect\",\n        \"RegExp\",\n        \"Set\",\n        \"String\",\n        \"Symbol\",\n        \"Uint16Array\",\n        \"Uint32Array\",\n        \"Uint8Array\",\n        \"Uint8ClampedArray\",\n        \"undefined\",\n        \"unescape\",\n        \"WeakMap\",\n        \"WeakSet\",\n    ])\n)\nconst callAllowed = new Set(\n    [\n        Array.isArray,\n        typeof BigInt === \"function\" ? BigInt : undefined,\n        Boolean,\n        Date,\n        Date.parse,\n        decodeURI,\n        decodeURIComponent,\n        encodeURI,\n        encodeURIComponent,\n        escape,\n        isFinite,\n        isNaN,\n        isPrototypeOf,\n        ...Object.getOwnPropertyNames(Math)\n            .map(k => Math[k])\n            .filter(f => typeof f === \"function\"),\n        Number,\n        Number.isFinite,\n        Number.isNaN,\n        Number.parseFloat,\n        Number.parseInt,\n        Object,\n        Object.entries,\n        Object.is,\n        Object.isExtensible,\n        Object.isFrozen,\n        Object.isSealed,\n        Object.keys,\n        Object.values,\n        parseFloat,\n        parseInt,\n        RegExp,\n        String,\n        String.fromCharCode,\n        String.fromCodePoint,\n        String.raw,\n        Symbol,\n        Symbol.for,\n        Symbol.keyFor,\n        unescape,\n    ].filter(f => typeof f === \"function\")\n)\nconst callPassThrough = new Set([\n    Object.freeze,\n    Object.preventExtensions,\n    Object.seal,\n])\n\n/**\n * Get the property descriptor.\n * @param {object} object The object to get.\n * @param {string|number|symbol} name The property name to get.\n */\nfunction getPropertyDescriptor(object, name) {\n    let x = object\n    while ((typeof x === \"object\" || typeof x === \"function\") && x !== null) {\n        const d = Object.getOwnPropertyDescriptor(x, name)\n        if (d) {\n            return d\n        }\n        x = Object.getPrototypeOf(x)\n    }\n    return null\n}\n\n/**\n * Check if a property is getter or not.\n * @param {object} object The object to check.\n * @param {string|number|symbol} name The property name to check.\n */\nfunction isGetter(object, name) {\n    const d = getPropertyDescriptor(object, name)\n    return d != null && d.get != null\n}\n\n/**\n * Get the element values of a given node list.\n * @param {Node[]} nodeList The node list to get values.\n * @param {Scope|undefined} initialScope The initial scope to find variables.\n * @returns {any[]|null} The value list if all nodes are constant. Otherwise, null.\n */\nfunction getElementValues(nodeList, initialScope) {\n    const valueList = []\n\n    for (let i = 0; i < nodeList.length; ++i) {\n        const elementNode = nodeList[i]\n\n        if (elementNode == null) {\n            valueList.length = i + 1\n        } else if (elementNode.type === \"SpreadElement\") {\n            const argument = getStaticValueR(elementNode.argument, initialScope)\n            if (argument == null) {\n                return null\n            }\n            valueList.push(...argument.value)\n        } else {\n            const element = getStaticValueR(elementNode, initialScope)\n            if (element == null) {\n                return null\n            }\n            valueList.push(element.value)\n        }\n    }\n\n    return valueList\n}\n\nconst operations = Object.freeze({\n    ArrayExpression(node, initialScope) {\n        const elements = getElementValues(node.elements, initialScope)\n        return elements != null ? { value: elements } : null\n    },\n\n    AssignmentExpression(node, initialScope) {\n        if (node.operator === \"=\") {\n            return getStaticValueR(node.right, initialScope)\n        }\n        return null\n    },\n\n    //eslint-disable-next-line complexity\n    BinaryExpression(node, initialScope) {\n        if (node.operator === \"in\" || node.operator === \"instanceof\") {\n            // Not supported.\n            return null\n        }\n\n        const left = getStaticValueR(node.left, initialScope)\n        const right = getStaticValueR(node.right, initialScope)\n        if (left != null && right != null) {\n            switch (node.operator) {\n                case \"==\":\n                    return { value: left.value == right.value } //eslint-disable-line eqeqeq\n                case \"!=\":\n                    return { value: left.value != right.value } //eslint-disable-line eqeqeq\n                case \"===\":\n                    return { value: left.value === right.value }\n                case \"!==\":\n                    return { value: left.value !== right.value }\n                case \"<\":\n                    return { value: left.value < right.value }\n                case \"<=\":\n                    return { value: left.value <= right.value }\n                case \">\":\n                    return { value: left.value > right.value }\n                case \">=\":\n                    return { value: left.value >= right.value }\n                case \"<<\":\n                    return { value: left.value << right.value }\n                case \">>\":\n                    return { value: left.value >> right.value }\n                case \">>>\":\n                    return { value: left.value >>> right.value }\n                case \"+\":\n                    return { value: left.value + right.value }\n                case \"-\":\n                    return { value: left.value - right.value }\n                case \"*\":\n                    return { value: left.value * right.value }\n                case \"/\":\n                    return { value: left.value / right.value }\n                case \"%\":\n                    return { value: left.value % right.value }\n                case \"**\":\n                    return { value: Math.pow(left.value, right.value) }\n                case \"|\":\n                    return { value: left.value | right.value }\n                case \"^\":\n                    return { value: left.value ^ right.value }\n                case \"&\":\n                    return { value: left.value & right.value }\n\n                // no default\n            }\n        }\n\n        return null\n    },\n\n    CallExpression(node, initialScope) {\n        const calleeNode = node.callee\n        const args = getElementValues(node.arguments, initialScope)\n\n        if (args != null) {\n            if (calleeNode.type === \"MemberExpression\") {\n                const object = getStaticValueR(calleeNode.object, initialScope)\n                if (object != null) {\n                    if (\n                        object.value == null &&\n                        (object.optional || node.optional)\n                    ) {\n                        return { value: undefined, optional: true }\n                    }\n                    const property = calleeNode.computed\n                        ? getStaticValueR(calleeNode.property, initialScope)\n                        : { value: calleeNode.property.name }\n\n                    if (property != null) {\n                        const receiver = object.value\n                        const methodName = property.value\n                        if (callAllowed.has(receiver[methodName])) {\n                            return { value: receiver[methodName](...args) }\n                        }\n                        if (callPassThrough.has(receiver[methodName])) {\n                            return { value: args[0] }\n                        }\n                    }\n                }\n            } else {\n                const callee = getStaticValueR(calleeNode, initialScope)\n                if (callee != null) {\n                    if (callee.value == null && node.optional) {\n                        return { value: undefined, optional: true }\n                    }\n                    const func = callee.value\n                    if (callAllowed.has(func)) {\n                        return { value: func(...args) }\n                    }\n                    if (callPassThrough.has(func)) {\n                        return { value: args[0] }\n                    }\n                }\n            }\n        }\n\n        return null\n    },\n\n    ConditionalExpression(node, initialScope) {\n        const test = getStaticValueR(node.test, initialScope)\n        if (test != null) {\n            return test.value\n                ? getStaticValueR(node.consequent, initialScope)\n                : getStaticValueR(node.alternate, initialScope)\n        }\n        return null\n    },\n\n    ExpressionStatement(node, initialScope) {\n        return getStaticValueR(node.expression, initialScope)\n    },\n\n    Identifier(node, initialScope) {\n        if (initialScope != null) {\n            const variable = findVariable(initialScope, node)\n\n            // Built-in globals.\n            if (\n                variable != null &&\n                variable.defs.length === 0 &&\n                builtinNames.has(variable.name) &&\n                variable.name in globalObject\n            ) {\n                return { value: globalObject[variable.name] }\n            }\n\n            // Constants.\n            if (variable != null && variable.defs.length === 1) {\n                const def = variable.defs[0]\n                if (\n                    def.parent &&\n                    def.parent.kind === \"const\" &&\n                    // TODO(mysticatea): don't support destructuring here.\n                    def.node.id.type === \"Identifier\"\n                ) {\n                    return getStaticValueR(def.node.init, initialScope)\n                }\n            }\n        }\n        return null\n    },\n\n    Literal(node) {\n        //istanbul ignore if : this is implementation-specific behavior.\n        if ((node.regex != null || node.bigint != null) && node.value == null) {\n            // It was a RegExp/BigInt literal, but Node.js didn't support it.\n            return null\n        }\n        return { value: node.value }\n    },\n\n    LogicalExpression(node, initialScope) {\n        const left = getStaticValueR(node.left, initialScope)\n        if (left != null) {\n            if (\n                (node.operator === \"||\" && Boolean(left.value) === true) ||\n                (node.operator === \"&&\" && Boolean(left.value) === false) ||\n                (node.operator === \"??\" && left.value != null)\n            ) {\n                return left\n            }\n\n            const right = getStaticValueR(node.right, initialScope)\n            if (right != null) {\n                return right\n            }\n        }\n\n        return null\n    },\n\n    MemberExpression(node, initialScope) {\n        const object = getStaticValueR(node.object, initialScope)\n        if (object != null) {\n            if (object.value == null && (object.optional || node.optional)) {\n                return { value: undefined, optional: true }\n            }\n            const property = node.computed\n                ? getStaticValueR(node.property, initialScope)\n                : { value: node.property.name }\n\n            if (property != null && !isGetter(object.value, property.value)) {\n                return { value: object.value[property.value] }\n            }\n        }\n        return null\n    },\n\n    ChainExpression(node, initialScope) {\n        const expression = getStaticValueR(node.expression, initialScope)\n        if (expression != null) {\n            return { value: expression.value }\n        }\n        return null\n    },\n\n    NewExpression(node, initialScope) {\n        const callee = getStaticValueR(node.callee, initialScope)\n        const args = getElementValues(node.arguments, initialScope)\n\n        if (callee != null && args != null) {\n            const Func = callee.value\n            if (callAllowed.has(Func)) {\n                return { value: new Func(...args) }\n            }\n        }\n\n        return null\n    },\n\n    ObjectExpression(node, initialScope) {\n        const object = {}\n\n        for (const propertyNode of node.properties) {\n            if (propertyNode.type === \"Property\") {\n                if (propertyNode.kind !== \"init\") {\n                    return null\n                }\n                const key = propertyNode.computed\n                    ? getStaticValueR(propertyNode.key, initialScope)\n                    : { value: propertyNode.key.name }\n                const value = getStaticValueR(propertyNode.value, initialScope)\n                if (key == null || value == null) {\n                    return null\n                }\n                object[key.value] = value.value\n            } else if (\n                propertyNode.type === \"SpreadElement\" ||\n                propertyNode.type === \"ExperimentalSpreadProperty\"\n            ) {\n                const argument = getStaticValueR(\n                    propertyNode.argument,\n                    initialScope\n                )\n                if (argument == null) {\n                    return null\n                }\n                Object.assign(object, argument.value)\n            } else {\n                return null\n            }\n        }\n\n        return { value: object }\n    },\n\n    SequenceExpression(node, initialScope) {\n        const last = node.expressions[node.expressions.length - 1]\n        return getStaticValueR(last, initialScope)\n    },\n\n    TaggedTemplateExpression(node, initialScope) {\n        const tag = getStaticValueR(node.tag, initialScope)\n        const expressions = getElementValues(\n            node.quasi.expressions,\n            initialScope\n        )\n\n        if (tag != null && expressions != null) {\n            const func = tag.value\n            const strings = node.quasi.quasis.map(q => q.value.cooked)\n            strings.raw = node.quasi.quasis.map(q => q.value.raw)\n\n            if (func === String.raw) {\n                return { value: func(strings, ...expressions) }\n            }\n        }\n\n        return null\n    },\n\n    TemplateLiteral(node, initialScope) {\n        const expressions = getElementValues(node.expressions, initialScope)\n        if (expressions != null) {\n            let value = node.quasis[0].value.cooked\n            for (let i = 0; i < expressions.length; ++i) {\n                value += expressions[i]\n                value += node.quasis[i + 1].value.cooked\n            }\n            return { value }\n        }\n        return null\n    },\n\n    UnaryExpression(node, initialScope) {\n        if (node.operator === \"delete\") {\n            // Not supported.\n            return null\n        }\n        if (node.operator === \"void\") {\n            return { value: undefined }\n        }\n\n        const arg = getStaticValueR(node.argument, initialScope)\n        if (arg != null) {\n            switch (node.operator) {\n                case \"-\":\n                    return { value: -arg.value }\n                case \"+\":\n                    return { value: +arg.value } //eslint-disable-line no-implicit-coercion\n                case \"!\":\n                    return { value: !arg.value }\n                case \"~\":\n                    return { value: ~arg.value }\n                case \"typeof\":\n                    return { value: typeof arg.value }\n\n                // no default\n            }\n        }\n\n        return null\n    },\n})\n\n/**\n * Get the value of a given node if it's a static value.\n * @param {Node} node The node to get.\n * @param {Scope|undefined} initialScope The scope to start finding variable.\n * @returns {{value:any}|{value:undefined,optional?:true}|null} The static value of the node, or `null`.\n */\nfunction getStaticValueR(node, initialScope) {\n    if (node != null && Object.hasOwnProperty.call(operations, node.type)) {\n        return operations[node.type](node, initialScope)\n    }\n    return null\n}\n\n/**\n * Get the value of a given node if it's a static value.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If this scope was given, this tries to resolve identifier references which are in the given node as much as possible.\n * @returns {{value:any}|{value:undefined,optional?:true}|null} The static value of the node, or `null`.\n */\nexport function getStaticValue(node, initialScope = null) {\n    try {\n        return getStaticValueR(node, initialScope)\n    } catch (_error) {\n        return null\n    }\n}\n", "import { getStaticValue } from \"./get-static-value\"\n\n/**\n * Get the value of a given node if it's a literal or a template literal.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If the node is an Identifier node and this scope was given, this checks the variable of the identifier, and returns the value of it if the variable is a constant.\n * @returns {string|null} The value of the node, or `null`.\n */\nexport function getStringIfConstant(node, initialScope = null) {\n    // Handle the literals that the platform doesn't support natively.\n    if (node && node.type === \"Literal\" && node.value === null) {\n        if (node.regex) {\n            return `/${node.regex.pattern}/${node.regex.flags}`\n        }\n        if (node.bigint) {\n            return node.bigint\n        }\n    }\n\n    const evaluated = getStaticValue(node, initialScope)\n    return evaluated && String(evaluated.value)\n}\n", "import { getStringIfConstant } from \"./get-string-if-constant\"\n\n/**\n * Get the property name from a MemberExpression node or a Property node.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If the node is a computed property node and this scope was given, this checks the computed property name by the `getStringIfConstant` function with the scope, and returns the value of it.\n * @returns {string|null} The property name of the node.\n */\nexport function getPropertyName(node, initialScope) {\n    switch (node.type) {\n        case \"MemberExpression\":\n            if (node.computed) {\n                return getStringIfConstant(node.property, initialScope)\n            }\n            return node.property.name\n\n        case \"Property\":\n        case \"MethodDefinition\":\n            if (node.computed) {\n                return getStringIfConstant(node.key, initialScope)\n            }\n            if (node.key.type === \"Literal\") {\n                return String(node.key.value)\n            }\n            return node.key.name\n\n        // no default\n    }\n\n    return null\n}\n", "import { getPropertyName } from \"./get-property-name\"\n\n/**\n * Get the name and kind of the given function node.\n * @param {ASTNode} node - The function node to get.\n * @returns {string} The name and kind of the function node.\n */\nexport function getFunctionNameWithKind(node) {\n    const parent = node.parent\n    const tokens = []\n\n    if (parent.type === \"MethodDefinition\" && parent.static) {\n        tokens.push(\"static\")\n    }\n    if (node.async) {\n        tokens.push(\"async\")\n    }\n    if (node.generator) {\n        tokens.push(\"generator\")\n    }\n\n    if (node.type === \"ArrowFunctionExpression\") {\n        tokens.push(\"arrow\", \"function\")\n    } else if (\n        parent.type === \"Property\" ||\n        parent.type === \"MethodDefinition\"\n    ) {\n        if (parent.kind === \"constructor\") {\n            return \"constructor\"\n        }\n        if (parent.kind === \"get\") {\n            tokens.push(\"getter\")\n        } else if (parent.kind === \"set\") {\n            tokens.push(\"setter\")\n        } else {\n            tokens.push(\"method\")\n        }\n    } else {\n        tokens.push(\"function\")\n    }\n\n    if (node.id) {\n        tokens.push(`'${node.id.name}'`)\n    } else {\n        const name = getPropertyName(parent)\n\n        if (name) {\n            tokens.push(`'${name}'`)\n        }\n    }\n\n    if (node.type === \"ArrowFunctionExpression\") {\n        if (\n            parent.type === \"VariableDeclarator\" &&\n            parent.id &&\n            parent.id.type === \"Identifier\"\n        ) {\n            tokens.push(`'${parent.id.name}'`)\n        }\n        if (\n            parent.type === \"AssignmentExpression\" &&\n            parent.left &&\n            parent.left.type === \"Identifier\"\n        ) {\n            tokens.push(`'${parent.left.name}'`)\n        }\n    }\n\n    return tokens.join(\" \")\n}\n", "import evk from \"eslint-visitor-keys\"\n\nconst typeConversionBinaryOps = Object.freeze(\n    new Set([\n        \"==\",\n        \"!=\",\n        \"<\",\n        \"<=\",\n        \">\",\n        \">=\",\n        \"<<\",\n        \">>\",\n        \">>>\",\n        \"+\",\n        \"-\",\n        \"*\",\n        \"/\",\n        \"%\",\n        \"|\",\n        \"^\",\n        \"&\",\n        \"in\",\n    ])\n)\nconst typeConversionUnaryOps = Object.freeze(new Set([\"-\", \"+\", \"!\", \"~\"]))\n\n/**\n * Check whether the given value is an ASTNode or not.\n * @param {any} x The value to check.\n * @returns {boolean} `true` if the value is an ASTNode.\n */\nfunction isNode(x) {\n    return x !== null && typeof x === \"object\" && typeof x.type === \"string\"\n}\n\nconst visitor = Object.freeze(\n    Object.assign(Object.create(null), {\n        $visit(node, options, visitorKeys) {\n            const { type } = node\n\n            if (typeof this[type] === \"function\") {\n                return this[type](node, options, visitorKeys)\n            }\n\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n\n        $visitChildren(node, options, visitorKeys) {\n            const { type } = node\n\n            for (const key of visitorKeys[type] || evk.getKeys(node)) {\n                const value = node[key]\n\n                if (Array.isArray(value)) {\n                    for (const element of value) {\n                        if (\n                            isNode(element) &&\n                            this.$visit(element, options, visitorKeys)\n                        ) {\n                            return true\n                        }\n                    }\n                } else if (\n                    isNode(value) &&\n                    this.$visit(value, options, visitorKeys)\n                ) {\n                    return true\n                }\n            }\n\n            return false\n        },\n\n        ArrowFunctionExpression() {\n            return false\n        },\n        AssignmentExpression() {\n            return true\n        },\n        AwaitExpression() {\n            return true\n        },\n        BinaryExpression(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                typeConversionBinaryOps.has(node.operator) &&\n                (node.left.type !== \"Literal\" || node.right.type !== \"Literal\")\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        CallExpression() {\n            return true\n        },\n        FunctionExpression() {\n            return false\n        },\n        ImportExpression() {\n            return true\n        },\n        MemberExpression(node, options, visitorKeys) {\n            if (options.considerGetters) {\n                return true\n            }\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.property.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        MethodDefinition(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        NewExpression() {\n            return true\n        },\n        Property(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        UnaryExpression(node, options, visitorKeys) {\n            if (node.operator === \"delete\") {\n                return true\n            }\n            if (\n                options.considerImplicitTypeConversion &&\n                typeConversionUnaryOps.has(node.operator) &&\n                node.argument.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        UpdateExpression() {\n            return true\n        },\n        YieldExpression() {\n            return true\n        },\n    })\n)\n\n/**\n * Check whether a given node has any side effect or not.\n * @param {Node} node The node to get.\n * @param {SourceCode} sourceCode The source code object.\n * @param {object} [options] The option object.\n * @param {boolean} [options.considerGetters=false] If `true` then it considers member accesses as the node which has side effects.\n * @param {boolean} [options.considerImplicitTypeConversion=false] If `true` then it considers implicit type conversion as the node which has side effects.\n * @param {object} [options.visitorKeys=evk.KEYS] The keys to traverse nodes. Use `context.getSourceCode().visitorKeys`.\n * @returns {boolean} `true` if the node has a certain side effect.\n */\nexport function hasSideEffect(\n    node,\n    sourceCode,\n    { considerGetters = false, considerImplicitTypeConversion = false } = {}\n) {\n    return visitor.$visit(\n        node,\n        { considerGetters, considerImplicitTypeConversion },\n        sourceCode.visitorKeys || evk.KEYS\n    )\n}\n", "import { isClosingParenToken, isOpeningParenToken } from \"./token-predicate\"\n\n/**\n * Get the left parenthesis of the parent node syntax if it exists.\n * E.g., `if (a) {}` then the `(`.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {Token|null} The left parenthesis of the parent node syntax\n */\nfunction getParentSyntaxParen(node, sourceCode) {\n    const parent = node.parent\n\n    switch (parent.type) {\n        case \"CallExpression\":\n        case \"NewExpression\":\n            if (parent.arguments.length === 1 && parent.arguments[0] === node) {\n                return sourceCode.getTokenAfter(\n                    parent.callee,\n                    isOpeningParenToken\n                )\n            }\n            return null\n\n        case \"DoWhileStatement\":\n            if (parent.test === node) {\n                return sourceCode.getTokenAfter(\n                    parent.body,\n                    isOpeningParenToken\n                )\n            }\n            return null\n\n        case \"IfStatement\":\n        case \"WhileStatement\":\n            if (parent.test === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"ImportExpression\":\n            if (parent.source === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"SwitchStatement\":\n            if (parent.discriminant === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"WithStatement\":\n            if (parent.object === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        default:\n            return null\n    }\n}\n\n/**\n * Check whether a given node is parenthesized or not.\n * @param {number} times The number of parantheses.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {boolean} `true` if the node is parenthesized the given times.\n */\n/**\n * Check whether a given node is parenthesized or not.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {boolean} `true` if the node is parenthesized.\n */\nexport function isParenthesized(\n    timesOrNode,\n    nodeOrSourceCode,\n    optionalSourceCode\n) {\n    let times, node, sourceCode, maybeLeftParen, maybeRightParen\n    if (typeof timesOrNode === \"number\") {\n        times = timesOrNode | 0\n        node = nodeOrSourceCode\n        sourceCode = optionalSourceCode\n        if (!(times >= 1)) {\n            throw new TypeError(\"'times' should be a positive integer.\")\n        }\n    } else {\n        times = 1\n        node = timesOrNode\n        sourceCode = nodeOrSourceCode\n    }\n\n    if (node == null) {\n        return false\n    }\n\n    maybeLeftParen = maybeRightParen = node\n    do {\n        maybeLeftParen = sourceCode.getTokenBefore(maybeLeftParen)\n        maybeRightParen = sourceCode.getTokenAfter(maybeRightParen)\n    } while (\n        maybeLeftParen != null &&\n        maybeRightParen != null &&\n        isOpeningParenToken(maybeLeftParen) &&\n        isClosingParenToken(maybeRightParen) &&\n        // Avoid false positive such as `if (a) {}`\n        maybeLeftParen !== getParentSyntaxParen(node, sourceCode) &&\n        --times > 0\n    )\n\n    return times === 0\n}\n", "/**\n * <AUTHOR> <https://github.com/mysticatea>\n * See LICENSE file in root directory for full license.\n */\n\nconst placeholder = /\\$(?:[$&`']|[1-9][0-9]?)/gu\n\n/** @type {WeakMap<PatternMatcher, {pattern:RegExp,escaped:boolean}>} */\nconst internal = new WeakMap()\n\n/**\n * Check whether a given character is escaped or not.\n * @param {string} str The string to check.\n * @param {number} index The location of the character to check.\n * @returns {boolean} `true` if the character is escaped.\n */\nfunction isEscaped(str, index) {\n    let escaped = false\n    for (let i = index - 1; i >= 0 && str.charCodeAt(i) === 0x5c; --i) {\n        escaped = !escaped\n    }\n    return escaped\n}\n\n/**\n * Replace a given string by a given matcher.\n * @param {PatternMatcher} matcher The pattern matcher.\n * @param {string} str The string to be replaced.\n * @param {string} replacement The new substring to replace each matched part.\n * @returns {string} The replaced string.\n */\nfunction replaceS(matcher, str, replacement) {\n    const chunks = []\n    let index = 0\n\n    /** @type {RegExpExecArray} */\n    let match = null\n\n    /**\n     * @param {string} key The placeholder.\n     * @returns {string} The replaced string.\n     */\n    function replacer(key) {\n        switch (key) {\n            case \"$$\":\n                return \"$\"\n            case \"$&\":\n                return match[0]\n            case \"$`\":\n                return str.slice(0, match.index)\n            case \"$'\":\n                return str.slice(match.index + match[0].length)\n            default: {\n                const i = key.slice(1)\n                if (i in match) {\n                    return match[i]\n                }\n                return key\n            }\n        }\n    }\n\n    for (match of matcher.execAll(str)) {\n        chunks.push(str.slice(index, match.index))\n        chunks.push(replacement.replace(placeholder, replacer))\n        index = match.index + match[0].length\n    }\n    chunks.push(str.slice(index))\n\n    return chunks.join(\"\")\n}\n\n/**\n * Replace a given string by a given matcher.\n * @param {PatternMatcher} matcher The pattern matcher.\n * @param {string} str The string to be replaced.\n * @param {(...strs[])=>string} replace The function to replace each matched part.\n * @returns {string} The replaced string.\n */\nfunction replaceF(matcher, str, replace) {\n    const chunks = []\n    let index = 0\n\n    for (const match of matcher.execAll(str)) {\n        chunks.push(str.slice(index, match.index))\n        chunks.push(String(replace(...match, match.index, match.input)))\n        index = match.index + match[0].length\n    }\n    chunks.push(str.slice(index))\n\n    return chunks.join(\"\")\n}\n\n/**\n * The class to find patterns as considering escape sequences.\n */\nexport class PatternMatcher {\n    /**\n     * Initialize this matcher.\n     * @param {RegExp} pattern The pattern to match.\n     * @param {{escaped:boolean}} options The options.\n     */\n    constructor(pattern, { escaped = false } = {}) {\n        if (!(pattern instanceof RegExp)) {\n            throw new TypeError(\"'pattern' should be a RegExp instance.\")\n        }\n        if (!pattern.flags.includes(\"g\")) {\n            throw new Error(\"'pattern' should contains 'g' flag.\")\n        }\n\n        internal.set(this, {\n            pattern: new RegExp(pattern.source, pattern.flags),\n            escaped: Boolean(escaped),\n        })\n    }\n\n    /**\n     * Find the pattern in a given string.\n     * @param {string} str The string to find.\n     * @returns {IterableIterator<RegExpExecArray>} The iterator which iterate the matched information.\n     */\n    *execAll(str) {\n        const { pattern, escaped } = internal.get(this)\n        let match = null\n        let lastIndex = 0\n\n        pattern.lastIndex = 0\n        while ((match = pattern.exec(str)) != null) {\n            if (escaped || !isEscaped(str, match.index)) {\n                lastIndex = pattern.lastIndex\n                yield match\n                pattern.lastIndex = lastIndex\n            }\n        }\n    }\n\n    /**\n     * Check whether the pattern is found in a given string.\n     * @param {string} str The string to check.\n     * @returns {boolean} `true` if the pattern was found in the string.\n     */\n    test(str) {\n        const it = this.execAll(str)\n        const ret = it.next()\n        return !ret.done\n    }\n\n    /**\n     * Replace a given string.\n     * @param {string} str The string to be replaced.\n     * @param {(string|((...strs:string[])=>string))} replacer The string or function to replace. This is the same as the 2nd argument of `String.prototype.replace`.\n     * @returns {string} The replaced string.\n     */\n    [Symbol.replace](str, replacer) {\n        return typeof replacer === \"function\"\n            ? replaceF(this, String(str), replacer)\n            : replaceS(this, String(str), String(replacer))\n    }\n}\n", "import { findVariable } from \"./find-variable\"\nimport { getPropertyName } from \"./get-property-name\"\nimport { getStringIfConstant } from \"./get-string-if-constant\"\n\nconst IMPORT_TYPE = /^(?:Import|Export(?:All|Default|Named))Declaration$/u\nconst has = Function.call.bind(Object.hasOwnProperty)\n\nexport const READ = Symbol(\"read\")\nexport const CALL = Symbol(\"call\")\nexport const CONSTRUCT = Symbol(\"construct\")\nexport const ESM = Symbol(\"esm\")\n\nconst requireCall = { require: { [CALL]: true } }\n\n/**\n * Check whether a given variable is modified or not.\n * @param {Variable} variable The variable to check.\n * @returns {boolean} `true` if the variable is modified.\n */\nfunction isModifiedGlobal(variable) {\n    return (\n        variable == null ||\n        variable.defs.length !== 0 ||\n        variable.references.some(r => r.isWrite())\n    )\n}\n\n/**\n * Check if the value of a given node is passed through to the parent syntax as-is.\n * For example, `a` and `b` in (`a || b` and `c ? a : b`) are passed through.\n * @param {Node} node A node to check.\n * @returns {boolean} `true` if the node is passed through.\n */\nfunction isPassThrough(node) {\n    const parent = node.parent\n\n    switch (parent && parent.type) {\n        case \"ConditionalExpression\":\n            return parent.consequent === node || parent.alternate === node\n        case \"LogicalExpression\":\n            return true\n        case \"SequenceExpression\":\n            return parent.expressions[parent.expressions.length - 1] === node\n        case \"ChainExpression\":\n            return true\n\n        default:\n            return false\n    }\n}\n\n/**\n * The reference tracker.\n */\nexport class ReferenceTracker {\n    /**\n     * Initialize this tracker.\n     * @param {Scope} globalScope The global scope.\n     * @param {object} [options] The options.\n     * @param {\"legacy\"|\"strict\"} [options.mode=\"strict\"] The mode to determine the ImportDeclaration's behavior for CJS modules.\n     * @param {string[]} [options.globalObjectNames=[\"global\",\"globalThis\",\"self\",\"window\"]] The variable names for Global Object.\n     */\n    constructor(\n        globalScope,\n        {\n            mode = \"strict\",\n            globalObjectNames = [\"global\", \"globalThis\", \"self\", \"window\"],\n        } = {}\n    ) {\n        this.variableStack = []\n        this.globalScope = globalScope\n        this.mode = mode\n        this.globalObjectNames = globalObjectNames.slice(0)\n    }\n\n    /**\n     * Iterate the references of global variables.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *iterateGlobalReferences(traceMap) {\n        for (const key of Object.keys(traceMap)) {\n            const nextTraceMap = traceMap[key]\n            const path = [key]\n            const variable = this.globalScope.set.get(key)\n\n            if (isModifiedGlobal(variable)) {\n                continue\n            }\n\n            yield* this._iterateVariableReferences(\n                variable,\n                path,\n                nextTraceMap,\n                true\n            )\n        }\n\n        for (const key of this.globalObjectNames) {\n            const path = []\n            const variable = this.globalScope.set.get(key)\n\n            if (isModifiedGlobal(variable)) {\n                continue\n            }\n\n            yield* this._iterateVariableReferences(\n                variable,\n                path,\n                traceMap,\n                false\n            )\n        }\n    }\n\n    /**\n     * Iterate the references of CommonJS modules.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *iterateCjsReferences(traceMap) {\n        for (const { node } of this.iterateGlobalReferences(requireCall)) {\n            const key = getStringIfConstant(node.arguments[0])\n            if (key == null || !has(traceMap, key)) {\n                continue\n            }\n\n            const nextTraceMap = traceMap[key]\n            const path = [key]\n\n            if (nextTraceMap[READ]) {\n                yield {\n                    node,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n            yield* this._iteratePropertyReferences(node, path, nextTraceMap)\n        }\n    }\n\n    /**\n     * Iterate the references of ES modules.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *iterateEsmReferences(traceMap) {\n        const programNode = this.globalScope.block\n\n        for (const node of programNode.body) {\n            if (!IMPORT_TYPE.test(node.type) || node.source == null) {\n                continue\n            }\n            const moduleId = node.source.value\n\n            if (!has(traceMap, moduleId)) {\n                continue\n            }\n            const nextTraceMap = traceMap[moduleId]\n            const path = [moduleId]\n\n            if (nextTraceMap[READ]) {\n                yield { node, path, type: READ, info: nextTraceMap[READ] }\n            }\n\n            if (node.type === \"ExportAllDeclaration\") {\n                for (const key of Object.keys(nextTraceMap)) {\n                    const exportTraceMap = nextTraceMap[key]\n                    if (exportTraceMap[READ]) {\n                        yield {\n                            node,\n                            path: path.concat(key),\n                            type: READ,\n                            info: exportTraceMap[READ],\n                        }\n                    }\n                }\n            } else {\n                for (const specifier of node.specifiers) {\n                    const esm = has(nextTraceMap, ESM)\n                    const it = this._iterateImportReferences(\n                        specifier,\n                        path,\n                        esm\n                            ? nextTraceMap\n                            : this.mode === \"legacy\"\n                            ? Object.assign(\n                                  { default: nextTraceMap },\n                                  nextTraceMap\n                              )\n                            : { default: nextTraceMap }\n                    )\n\n                    if (esm) {\n                        yield* it\n                    } else {\n                        for (const report of it) {\n                            report.path = report.path.filter(exceptDefault)\n                            if (\n                                report.path.length >= 2 ||\n                                report.type !== READ\n                            ) {\n                                yield report\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Iterate the references for a given variable.\n     * @param {Variable} variable The variable to iterate that references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @param {boolean} shouldReport = The flag to report those references.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *_iterateVariableReferences(variable, path, traceMap, shouldReport) {\n        if (this.variableStack.includes(variable)) {\n            return\n        }\n        this.variableStack.push(variable)\n        try {\n            for (const reference of variable.references) {\n                if (!reference.isRead()) {\n                    continue\n                }\n                const node = reference.identifier\n\n                if (shouldReport && traceMap[READ]) {\n                    yield { node, path, type: READ, info: traceMap[READ] }\n                }\n                yield* this._iteratePropertyReferences(node, path, traceMap)\n            }\n        } finally {\n            this.variableStack.pop()\n        }\n    }\n\n    /**\n     * Iterate the references for a given AST node.\n     * @param rootNode The AST node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    //eslint-disable-next-line complexity\n    *_iteratePropertyReferences(rootNode, path, traceMap) {\n        let node = rootNode\n        while (isPassThrough(node)) {\n            node = node.parent\n        }\n\n        const parent = node.parent\n        if (parent.type === \"MemberExpression\") {\n            if (parent.object === node) {\n                const key = getPropertyName(parent)\n                if (key == null || !has(traceMap, key)) {\n                    return\n                }\n\n                path = path.concat(key) //eslint-disable-line no-param-reassign\n                const nextTraceMap = traceMap[key]\n                if (nextTraceMap[READ]) {\n                    yield {\n                        node: parent,\n                        path,\n                        type: READ,\n                        info: nextTraceMap[READ],\n                    }\n                }\n                yield* this._iteratePropertyReferences(\n                    parent,\n                    path,\n                    nextTraceMap\n                )\n            }\n            return\n        }\n        if (parent.type === \"CallExpression\") {\n            if (parent.callee === node && traceMap[CALL]) {\n                yield { node: parent, path, type: CALL, info: traceMap[CALL] }\n            }\n            return\n        }\n        if (parent.type === \"NewExpression\") {\n            if (parent.callee === node && traceMap[CONSTRUCT]) {\n                yield {\n                    node: parent,\n                    path,\n                    type: CONSTRUCT,\n                    info: traceMap[CONSTRUCT],\n                }\n            }\n            return\n        }\n        if (parent.type === \"AssignmentExpression\") {\n            if (parent.right === node) {\n                yield* this._iterateLhsReferences(parent.left, path, traceMap)\n                yield* this._iteratePropertyReferences(parent, path, traceMap)\n            }\n            return\n        }\n        if (parent.type === \"AssignmentPattern\") {\n            if (parent.right === node) {\n                yield* this._iterateLhsReferences(parent.left, path, traceMap)\n            }\n            return\n        }\n        if (parent.type === \"VariableDeclarator\") {\n            if (parent.init === node) {\n                yield* this._iterateLhsReferences(parent.id, path, traceMap)\n            }\n        }\n    }\n\n    /**\n     * Iterate the references for a given Pattern node.\n     * @param {Node} patternNode The Pattern node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *_iterateLhsReferences(patternNode, path, traceMap) {\n        if (patternNode.type === \"Identifier\") {\n            const variable = findVariable(this.globalScope, patternNode)\n            if (variable != null) {\n                yield* this._iterateVariableReferences(\n                    variable,\n                    path,\n                    traceMap,\n                    false\n                )\n            }\n            return\n        }\n        if (patternNode.type === \"ObjectPattern\") {\n            for (const property of patternNode.properties) {\n                const key = getPropertyName(property)\n\n                if (key == null || !has(traceMap, key)) {\n                    continue\n                }\n\n                const nextPath = path.concat(key)\n                const nextTraceMap = traceMap[key]\n                if (nextTraceMap[READ]) {\n                    yield {\n                        node: property,\n                        path: nextPath,\n                        type: READ,\n                        info: nextTraceMap[READ],\n                    }\n                }\n                yield* this._iterateLhsReferences(\n                    property.value,\n                    nextPath,\n                    nextTraceMap\n                )\n            }\n            return\n        }\n        if (patternNode.type === \"AssignmentPattern\") {\n            yield* this._iterateLhsReferences(patternNode.left, path, traceMap)\n        }\n    }\n\n    /**\n     * Iterate the references for a given ModuleSpecifier node.\n     * @param {Node} specifierNode The ModuleSpecifier node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *_iterateImportReferences(specifierNode, path, traceMap) {\n        const type = specifierNode.type\n\n        if (type === \"ImportSpecifier\" || type === \"ImportDefaultSpecifier\") {\n            const key =\n                type === \"ImportDefaultSpecifier\"\n                    ? \"default\"\n                    : specifierNode.imported.name\n            if (!has(traceMap, key)) {\n                return\n            }\n\n            path = path.concat(key) //eslint-disable-line no-param-reassign\n            const nextTraceMap = traceMap[key]\n            if (nextTraceMap[READ]) {\n                yield {\n                    node: specifierNode,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n            yield* this._iterateVariableReferences(\n                findVariable(this.globalScope, specifierNode.local),\n                path,\n                nextTraceMap,\n                false\n            )\n\n            return\n        }\n\n        if (type === \"ImportNamespaceSpecifier\") {\n            yield* this._iterateVariableReferences(\n                findVariable(this.globalScope, specifierNode.local),\n                path,\n                traceMap,\n                false\n            )\n            return\n        }\n\n        if (type === \"ExportSpecifier\") {\n            const key = specifierNode.local.name\n            if (!has(traceMap, key)) {\n                return\n            }\n\n            path = path.concat(key) //eslint-disable-line no-param-reassign\n            const nextTraceMap = traceMap[key]\n            if (nextTraceMap[READ]) {\n                yield {\n                    node: specifierNode,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n        }\n    }\n}\n\nReferenceTracker.READ = READ\nReferenceTracker.CALL = CALL\nReferenceTracker.CONSTRUCT = CONSTRUCT\nReferenceTracker.ESM = ESM\n\n/**\n * This is a predicate function for Array#filter.\n * @param {string} name A name part.\n * @param {number} index The index of the name.\n * @returns {boolean} `false` if it's default.\n */\nfunction exceptDefault(name, index) {\n    return !(index === 1 && name === \"default\")\n}\n", "import { findVariable } from \"./find-variable\"\nimport { getFunctionHeadLocation } from \"./get-function-head-location\"\nimport { getFunctionNameWithKind } from \"./get-function-name-with-kind\"\nimport { getInnermostScope } from \"./get-innermost-scope\"\nimport { getPropertyName } from \"./get-property-name\"\nimport { getStaticValue } from \"./get-static-value\"\nimport { getStringIfConstant } from \"./get-string-if-constant\"\nimport { hasSideEffect } from \"./has-side-effect\"\nimport { isParenthesized } from \"./is-parenthesized\"\nimport { PatternMatcher } from \"./pattern-matcher\"\nimport {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    READ,\n    ReferenceTracker,\n} from \"./reference-tracker\"\nimport {\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosing<PERSON>arenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isSemicolonToken,\n} from \"./token-predicate\"\n\nexport default {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    findVariable,\n    getFunctionHeadLocation,\n    getFunctionNameWithKind,\n    getInnermostScope,\n    getPropertyName,\n    getStaticValue,\n    getStringIfConstant,\n    hasSideEffect,\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosingParenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isParenthesized,\n    isSemicolonToken,\n    PatternMatcher,\n    READ,\n    ReferenceTracker,\n}\nexport {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    findVariable,\n    getFunctionHeadLocation,\n    getFunctionNameWithKind,\n    getInnermostScope,\n    getPropertyName,\n    getStaticValue,\n    getStringIfConstant,\n    hasSideEffect,\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosingParenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isParenthesized,\n    isSemicolonToken,\n    PatternMatcher,\n    READ,\n    ReferenceTracker,\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,YAAY,EAAE,IAAI,EAAE;AACtD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAC;AAClC;AACA,IAAI,IAAI,KAAK,GAAG,aAAY;AAC5B,IAAI,IAAI,KAAK,GAAG,MAAK;AACrB,IAAI,GAAG;AACP,QAAQ,KAAK,GAAG,MAAK;AACrB,QAAQ,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,WAAW,EAAE;AACpD,YAAY,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,MAAK;AAChD;AACA,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE;AAC7D,gBAAgB,KAAK,GAAG,WAAU;AAClC,gBAAgB,KAAK,GAAG,KAAI;AAC5B,gBAAgB,KAAK;AACrB,aAAa;AACb,SAAS;AACT,KAAK,QAAQ,KAAK,CAAC;AACnB;AACA,IAAI,OAAO,KAAK;AAChB;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE;AACvD,IAAI,IAAI,IAAI,GAAG,GAAE;AACjB,IAAI,IAAI,KAAK,GAAG,aAAY;AAC5B;AACA,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AACxC,QAAQ,IAAI,GAAG,WAAU;AACzB,KAAK,MAAM;AACX,QAAQ,IAAI,GAAG,UAAU,CAAC,KAAI;AAC9B,QAAQ,KAAK,GAAG,iBAAiB,CAAC,KAAK,EAAE,UAAU,EAAC;AACpD,KAAK;AACL;AACA,IAAI,OAAO,KAAK,IAAI,IAAI,EAAE;AAC1B,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAC;AAC5C,QAAQ,IAAI,QAAQ,IAAI,IAAI,EAAE;AAC9B,YAAY,OAAO,QAAQ;AAC3B,SAAS;AACT,QAAQ,KAAK,GAAG,KAAK,CAAC,MAAK;AAC3B,KAAK;AACL;AACA,IAAI,OAAO,IAAI;AACf,CAAC;;AC5BD;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,KAAK,EAAE;AACxB,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AACvB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,CAAC,EAAE;AACnB,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,YAAY,CAAC,KAAK,EAAE;AACpC,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC9D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,YAAY,CAAC,KAAK,EAAE;AACpC,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACxC,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,YAAY,CAAC,KAAK,EAAE;AACpC,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;AAC3C,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;AAC3C,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,qBAAqB,CAAC,KAAK,EAAE;AAC7C,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,qBAAqB,CAAC,KAAK,EAAE;AAC7C,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;AAC3C,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,mBAAmB,CAAC,KAAK,EAAE;AAC3C,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,cAAc,CAAC,KAAK,EAAE;AACtC,IAAI;AACJ,QAAQ,KAAK,CAAC,IAAI,KAAK,MAAM;AAC7B,QAAQ,KAAK,CAAC,IAAI,KAAK,OAAO;AAC9B,QAAQ,KAAK,CAAC,IAAI,KAAK,SAAS;AAChC,KAAK;AACL,CAAC;AACD;AACA,AAAY,MAAC,eAAe,GAAG,MAAM,CAAC,YAAY,EAAC;AACnD,AAAY,MAAC,eAAe,GAAG,MAAM,CAAC,YAAY,EAAC;AACnD,AAAY,MAAC,mBAAmB,GAAG,MAAM,CAAC,gBAAgB,EAAC;AAC3D,AAAY,MAAC,eAAe,GAAG,MAAM,CAAC,YAAY,EAAC;AACnD,AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACjE,AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACjE,AAAY,MAAC,wBAAwB,GAAG,MAAM,CAAC,qBAAqB,EAAC;AACrE,AAAY,MAAC,wBAAwB,GAAG,MAAM,CAAC,qBAAqB,EAAC;AACrE,AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACjE,AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACjE,AAAY,MAAC,iBAAiB,GAAG,MAAM,CAAC,cAAc,CAAC;;ACjIvD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE;AACnD,IAAI,OAAO,IAAI,CAAC,EAAE;AAClB,UAAU,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,mBAAmB,CAAC;AAChE,UAAU,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,mBAAmB,CAAC;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE;AAC1D,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;AAC9B,IAAI,IAAI,KAAK,GAAG,KAAI;AACpB,IAAI,IAAI,GAAG,GAAG,KAAI;AAClB;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB,EAAE;AACjD,QAAQ,MAAM,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;AAC7E;AACA,QAAQ,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,MAAK;AACpC,QAAQ,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,IAAG;AAChC,KAAK,MAAM;AACX,QAAQ,MAAM,CAAC,IAAI,KAAK,UAAU;AAClC,QAAQ,MAAM,CAAC,IAAI,KAAK,kBAAkB;AAC1C,MAAM;AACN,QAAQ,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,MAAK;AAChC,QAAQ,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,MAAK;AACjE,KAAK,MAAM;AACX,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;AAC9B,QAAQ,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,MAAK;AACjE,KAAK;AACL;AACA,IAAI,OAAO;AACX,QAAQ,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC;AACvC,QAAQ,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC;AACnC,KAAK;AACL,CAAC;;AC7CD;AACA,AAEA;AACA,MAAM,YAAY;AAClB,IAAI,OAAO,UAAU,KAAK,WAAW;AACrC,UAAU,UAAU;AACpB,UAAU,OAAO,IAAI,KAAK,WAAW;AACrC,UAAU,IAAI;AACd,UAAU,OAAO,MAAM,KAAK,WAAW;AACvC,UAAU,MAAM;AAChB,UAAU,OAAO,MAAM,KAAK,WAAW;AACvC,UAAU,MAAM;AAChB,UAAU,GAAE;AACZ;AACA,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM;AAClC,IAAI,IAAI,GAAG,CAAC;AACZ,QAAQ,OAAO;AACf,QAAQ,aAAa;AACrB,QAAQ,QAAQ;AAChB,QAAQ,eAAe;AACvB,QAAQ,gBAAgB;AACxB,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,WAAW;AACnB,QAAQ,oBAAoB;AAC5B,QAAQ,WAAW;AACnB,QAAQ,oBAAoB;AAC5B,QAAQ,QAAQ;AAChB,QAAQ,cAAc;AACtB,QAAQ,cAAc;AACtB,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,YAAY;AACpB,QAAQ,YAAY;AACpB,QAAQ,WAAW;AACnB,QAAQ,UAAU;AAClB,QAAQ,OAAO;AACf,QAAQ,eAAe;AACvB,QAAQ,MAAM;AACd,QAAQ,KAAK;AACb,QAAQ,MAAM;AACd,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ,QAAQ;AAChB,QAAQ,YAAY;AACpB,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,OAAO;AACf,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ,QAAQ;AAChB,QAAQ,aAAa;AACrB,QAAQ,aAAa;AACrB,QAAQ,YAAY;AACpB,QAAQ,mBAAmB;AAC3B,QAAQ,WAAW;AACnB,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,SAAS;AACjB,KAAK,CAAC;AACN,EAAC;AACD,MAAM,WAAW,GAAG,IAAI,GAAG;AAC3B,IAAI;AACJ,QAAQ,KAAK,CAAC,OAAO;AACrB,QAAQ,OAAO,MAAM,KAAK,UAAU,GAAG,MAAM,GAAG,SAAS;AACzD,QAAQ,OAAO;AACf,QAAQ,IAAI;AACZ,QAAQ,IAAI,CAAC,KAAK;AAClB,QAAQ,SAAS;AACjB,QAAQ,kBAAkB;AAC1B,QAAQ,SAAS;AACjB,QAAQ,kBAAkB;AAC1B,QAAQ,MAAM;AACd,QAAQ,QAAQ;AAChB,QAAQ,KAAK;AACb,QAAQ,aAAa;AACrB,QAAQ,GAAG,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC;AAC3C,aAAa,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9B,aAAa,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,UAAU,CAAC;AACjD,QAAQ,MAAM;AACd,QAAQ,MAAM,CAAC,QAAQ;AACvB,QAAQ,MAAM,CAAC,KAAK;AACpB,QAAQ,MAAM,CAAC,UAAU;AACzB,QAAQ,MAAM,CAAC,QAAQ;AACvB,QAAQ,MAAM;AACd,QAAQ,MAAM,CAAC,OAAO;AACtB,QAAQ,MAAM,CAAC,EAAE;AACjB,QAAQ,MAAM,CAAC,YAAY;AAC3B,QAAQ,MAAM,CAAC,QAAQ;AACvB,QAAQ,MAAM,CAAC,QAAQ;AACvB,QAAQ,MAAM,CAAC,IAAI;AACnB,QAAQ,MAAM,CAAC,MAAM;AACrB,QAAQ,UAAU;AAClB,QAAQ,QAAQ;AAChB,QAAQ,MAAM;AACd,QAAQ,MAAM;AACd,QAAQ,MAAM,CAAC,YAAY;AAC3B,QAAQ,MAAM,CAAC,aAAa;AAC5B,QAAQ,MAAM,CAAC,GAAG;AAClB,QAAQ,MAAM;AACd,QAAQ,MAAM,CAAC,GAAG;AAClB,QAAQ,MAAM,CAAC,MAAM;AACrB,QAAQ,QAAQ;AAChB,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,UAAU,CAAC;AAC1C,EAAC;AACD,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC;AAChC,IAAI,MAAM,CAAC,MAAM;AACjB,IAAI,MAAM,CAAC,iBAAiB;AAC5B,IAAI,MAAM,CAAC,IAAI;AACf,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE;AAC7C,IAAI,IAAI,CAAC,GAAG,OAAM;AAClB,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,UAAU,KAAK,CAAC,KAAK,IAAI,EAAE;AAC7E,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,wBAAwB,CAAC,CAAC,EAAE,IAAI,EAAC;AAC1D,QAAQ,IAAI,CAAC,EAAE;AACf,YAAY,OAAO,CAAC;AACpB,SAAS;AACT,QAAQ,CAAC,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,EAAC;AACpC,KAAK;AACL,IAAI,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE;AAChC,IAAI,MAAM,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAC;AACjD,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI;AACrC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,QAAQ,EAAE,YAAY,EAAE;AAClD,IAAI,MAAM,SAAS,GAAG,GAAE;AACxB;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AAC9C,QAAQ,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,EAAC;AACvC;AACA,QAAQ,IAAI,WAAW,IAAI,IAAI,EAAE;AACjC,YAAY,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,EAAC;AACpC,SAAS,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,eAAe,EAAE;AACzD,YAAY,MAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,EAAC;AAChF,YAAY,IAAI,QAAQ,IAAI,IAAI,EAAE;AAClC,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,SAAS,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAC;AAC7C,SAAS,MAAM;AACf,YAAY,MAAM,OAAO,GAAG,eAAe,CAAC,WAAW,EAAE,YAAY,EAAC;AACtE,YAAY,IAAI,OAAO,IAAI,IAAI,EAAE;AACjC,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC;AACzC,SAAS;AACT,KAAK;AACL;AACA,IAAI,OAAO,SAAS;AACpB,CAAC;AACD;AACA,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;AACjC,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACxC,QAAQ,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAC;AACtE,QAAQ,OAAO,QAAQ,IAAI,IAAI,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI;AAC5D,KAAK;AACL;AACA,IAAI,oBAAoB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC7C,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,EAAE;AACnC,YAAY,OAAO,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;AAC5D,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA;AACA,IAAI,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE;AACzC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE;AACtE;AACA,YAAY,OAAO,IAAI;AACvB,SAAS;AACT;AACA,QAAQ,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;AAC7D,QAAQ,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAC;AAC/D,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AAC3C,YAAY,QAAQ,IAAI,CAAC,QAAQ;AACjC,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;AAC/D,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;AAC/D,gBAAgB,KAAK,KAAK;AAC1B,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;AAChE,gBAAgB,KAAK,KAAK;AAC1B,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;AAChE,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;AAC/D,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;AAC/D,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;AAC/D,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;AAC/D,gBAAgB,KAAK,KAAK;AAC1B,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;AAChE,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;AACvE,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC9D;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE;AACvC,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,OAAM;AACtC,QAAQ,MAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAC;AACnE;AACA,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1B,YAAY,IAAI,UAAU,CAAC,IAAI,KAAK,kBAAkB,EAAE;AACxD,gBAAgB,MAAM,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,EAAC;AAC/E,gBAAgB,IAAI,MAAM,IAAI,IAAI,EAAE;AACpC,oBAAoB;AACpB,wBAAwB,MAAM,CAAC,KAAK,IAAI,IAAI;AAC5C,yBAAyB,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC;AAC1D,sBAAsB;AACtB,wBAAwB,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE;AACnE,qBAAqB;AACrB,oBAAoB,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;AACxD,0BAA0B,eAAe,CAAC,UAAU,CAAC,QAAQ,EAAE,YAAY,CAAC;AAC5E,0BAA0B,EAAE,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC,IAAI,GAAE;AAC7D;AACA,oBAAoB,IAAI,QAAQ,IAAI,IAAI,EAAE;AAC1C,wBAAwB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAK;AACrD,wBAAwB,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAK;AACzD,wBAAwB,IAAI,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;AACnE,4BAA4B,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE;AAC3E,yBAAyB;AACzB,wBAAwB,IAAI,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;AACvE,4BAA4B,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;AACrD,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa,MAAM;AACnB,gBAAgB,MAAM,MAAM,GAAG,eAAe,CAAC,UAAU,EAAE,YAAY,EAAC;AACxE,gBAAgB,IAAI,MAAM,IAAI,IAAI,EAAE;AACpC,oBAAoB,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/D,wBAAwB,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE;AACnE,qBAAqB;AACrB,oBAAoB,MAAM,IAAI,GAAG,MAAM,CAAC,MAAK;AAC7C,oBAAoB,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC/C,wBAAwB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE;AACvD,qBAAqB;AACrB,oBAAoB,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACnD,wBAAwB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;AACjD,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,qBAAqB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC9C,QAAQ,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;AAC7D,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1B,YAAY,OAAO,IAAI,CAAC,KAAK;AAC7B,kBAAkB,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;AAChE,kBAAkB,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/D,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,mBAAmB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC5C,QAAQ,OAAO,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;AAC7D,KAAK;AACL;AACA,IAAI,UAAU,CAAC,IAAI,EAAE,YAAY,EAAE;AACnC,QAAQ,IAAI,YAAY,IAAI,IAAI,EAAE;AAClC,YAAY,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,EAAE,IAAI,EAAC;AAC7D;AACA;AACA,YAAY;AACZ,gBAAgB,QAAQ,IAAI,IAAI;AAChC,gBAAgB,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAC1C,gBAAgB,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC/C,gBAAgB,QAAQ,CAAC,IAAI,IAAI,YAAY;AAC7C,cAAc;AACd,gBAAgB,OAAO,EAAE,KAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC7D,aAAa;AACb;AACA;AACA,YAAY,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAChE,gBAAgB,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAC;AAC5C,gBAAgB;AAChB,oBAAoB,GAAG,CAAC,MAAM;AAC9B,oBAAoB,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,OAAO;AAC/C;AACA,oBAAoB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY;AACrD,kBAAkB;AAClB,oBAAoB,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;AACvE,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,OAAO,CAAC,IAAI,EAAE;AAClB;AACA,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;AAC/E;AACA,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;AACpC,KAAK;AACL;AACA,IAAI,iBAAiB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC1C,QAAQ,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;AAC7D,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1B,YAAY;AACZ,gBAAgB,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI;AACvE,iBAAiB,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;AACzE,iBAAiB,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;AAC9D,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb;AACA,YAAY,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAC;AACnE,YAAY,IAAI,KAAK,IAAI,IAAI,EAAE;AAC/B,gBAAgB,OAAO,KAAK;AAC5B,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE;AACzC,QAAQ,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAC;AACjE,QAAQ,IAAI,MAAM,IAAI,IAAI,EAAE;AAC5B,YAAY,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC5E,gBAAgB,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE;AAC3D,aAAa;AACb,YAAY,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC1C,kBAAkB,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC;AAC9D,kBAAkB,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAE;AAC/C;AACA,YAAY,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC7E,gBAAgB,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC9D,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACxC,QAAQ,MAAM,UAAU,GAAG,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAC;AACzE,QAAQ,IAAI,UAAU,IAAI,IAAI,EAAE;AAChC,YAAY,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE;AAC9C,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE;AACtC,QAAQ,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAC;AACjE,QAAQ,MAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAC;AACnE;AACA,QAAQ,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC5C,YAAY,MAAM,IAAI,GAAG,MAAM,CAAC,MAAK;AACrC,YAAY,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACvC,gBAAgB,OAAO,EAAE,KAAK,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE;AACnD,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE;AACzC,QAAQ,MAAM,MAAM,GAAG,GAAE;AACzB;AACA,QAAQ,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;AACpD,YAAY,IAAI,YAAY,CAAC,IAAI,KAAK,UAAU,EAAE;AAClD,gBAAgB,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,EAAE;AAClD,oBAAoB,OAAO,IAAI;AAC/B,iBAAiB;AACjB,gBAAgB,MAAM,GAAG,GAAG,YAAY,CAAC,QAAQ;AACjD,sBAAsB,eAAe,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC;AACrE,sBAAsB,EAAE,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,GAAE;AACtD,gBAAgB,MAAM,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,EAAC;AAC/E,gBAAgB,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AAClD,oBAAoB,OAAO,IAAI;AAC/B,iBAAiB;AACjB,gBAAgB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAK;AAC/C,aAAa,MAAM;AACnB,gBAAgB,YAAY,CAAC,IAAI,KAAK,eAAe;AACrD,gBAAgB,YAAY,CAAC,IAAI,KAAK,4BAA4B;AAClE,cAAc;AACd,gBAAgB,MAAM,QAAQ,GAAG,eAAe;AAChD,oBAAoB,YAAY,CAAC,QAAQ;AACzC,oBAAoB,YAAY;AAChC,kBAAiB;AACjB,gBAAgB,IAAI,QAAQ,IAAI,IAAI,EAAE;AACtC,oBAAoB,OAAO,IAAI;AAC/B,iBAAiB;AACjB,gBAAgB,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAC;AACrD,aAAa,MAAM;AACnB,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE;AAChC,KAAK;AACL;AACA,IAAI,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC3C,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAC;AAClE,QAAQ,OAAO,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC;AAClD,KAAK;AACL;AACA,IAAI,wBAAwB,CAAC,IAAI,EAAE,YAAY,EAAE;AACjD,QAAQ,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,EAAC;AAC3D,QAAQ,MAAM,WAAW,GAAG,gBAAgB;AAC5C,YAAY,IAAI,CAAC,KAAK,CAAC,WAAW;AAClC,YAAY,YAAY;AACxB,UAAS;AACT;AACA,QAAQ,IAAI,GAAG,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI,EAAE;AAChD,YAAY,MAAM,IAAI,GAAG,GAAG,CAAC,MAAK;AAClC,YAAY,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,EAAC;AACtE,YAAY,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAC;AACjE;AACA,YAAY,IAAI,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE;AACrC,gBAAgB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,EAAE;AAC/D,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACxC,QAAQ,MAAM,WAAW,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,EAAC;AAC5E,QAAQ,IAAI,WAAW,IAAI,IAAI,EAAE;AACjC,YAAY,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAM;AACnD,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACzD,gBAAgB,KAAK,IAAI,WAAW,CAAC,CAAC,EAAC;AACvC,gBAAgB,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,OAAM;AACxD,aAAa;AACb,YAAY,OAAO,EAAE,KAAK,EAAE;AAC5B,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACxC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;AACxC;AACA,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;AACtC,YAAY,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;AACvC,SAAS;AACT;AACA,QAAQ,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAC;AAChE,QAAQ,IAAI,GAAG,IAAI,IAAI,EAAE;AACzB,YAAY,QAAQ,IAAI,CAAC,QAAQ;AACjC,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE;AAChD,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE;AAChD,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE;AAChD,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE;AAChD,gBAAgB,KAAK,QAAQ;AAC7B,oBAAoB,OAAO,EAAE,KAAK,EAAE,OAAO,GAAG,CAAC,KAAK,EAAE;AACtD;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AAC7C,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AAC3E,QAAQ,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC;AACxD,KAAK;AACL,IAAI,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,cAAc,CAAC,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE;AAC1D,IAAI,IAAI;AACR,QAAQ,OAAO,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC;AAClD,KAAK,CAAC,OAAO,MAAM,EAAE;AACrB,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL,CAAC;;ACvhBD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,mBAAmB,CAAC,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE;AAC/D;AACA,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;AAChE,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;AACxB,YAAY,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/D,SAAS;AACT,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE;AACzB,YAAY,OAAO,IAAI,CAAC,MAAM;AAC9B,SAAS;AACT,KAAK;AACL;AACA,IAAI,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,EAAE,YAAY,EAAC;AACxD,IAAI,OAAO,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;AAC/C,CAAC;;ACnBD;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACpD,IAAI,QAAQ,IAAI,CAAC,IAAI;AACrB,QAAQ,KAAK,kBAAkB;AAC/B,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/B,gBAAgB,OAAO,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC;AACvE,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI;AACrC;AACA,QAAQ,KAAK,UAAU,CAAC;AACxB,QAAQ,KAAK,kBAAkB;AAC/B,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/B,gBAAgB,OAAO,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC;AAClE,aAAa;AACb,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE;AAC7C,gBAAgB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;AAC7C,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI;AAChC;AACA;AACA,KAAK;AACL;AACA,IAAI,OAAO,IAAI;AACf,CAAC;;AC5BD;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,uBAAuB,CAAC,IAAI,EAAE;AAC9C,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;AAC9B,IAAI,MAAM,MAAM,GAAG,GAAE;AACrB;AACA,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,IAAI,MAAM,CAAC,MAAM,EAAE;AAC7D,QAAQ,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC7B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACpB,QAAQ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAC;AAC5B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxB,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,EAAC;AAChC,KAAK;AACL;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB,EAAE;AACjD,QAAQ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAC;AACxC,KAAK,MAAM;AACX,QAAQ,MAAM,CAAC,IAAI,KAAK,UAAU;AAClC,QAAQ,MAAM,CAAC,IAAI,KAAK,kBAAkB;AAC1C,MAAM;AACN,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE;AAC3C,YAAY,OAAO,aAAa;AAChC,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;AACnC,YAAY,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AACjC,SAAS,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;AAC1C,YAAY,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AACjC,SAAS,MAAM;AACf,YAAY,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AACjC,SAAS;AACT,KAAK,MAAM;AACX,QAAQ,MAAM,CAAC,IAAI,CAAC,UAAU,EAAC;AAC/B,KAAK;AACL;AACA,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE;AACjB,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC;AACxC,KAAK,MAAM;AACX,QAAQ,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,EAAC;AAC5C;AACA,QAAQ,IAAI,IAAI,EAAE;AAClB,YAAY,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAC;AACpC,SAAS;AACT,KAAK;AACL;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB,EAAE;AACjD,QAAQ;AACR,YAAY,MAAM,CAAC,IAAI,KAAK,oBAAoB;AAChD,YAAY,MAAM,CAAC,EAAE;AACrB,YAAY,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY;AAC3C,UAAU;AACV,YAAY,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC;AAC9C,SAAS;AACT,QAAQ;AACR,YAAY,MAAM,CAAC,IAAI,KAAK,sBAAsB;AAClD,YAAY,MAAM,CAAC,IAAI;AACvB,YAAY,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY;AAC7C,UAAU;AACV,YAAY,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC;AAChD,SAAS;AACT,KAAK;AACL;AACA,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;AAC3B,CAAC;;ACnED,MAAM,uBAAuB,GAAG,MAAM,CAAC,MAAM;AAC7C,IAAI,IAAI,GAAG,CAAC;AACZ,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,GAAG;AACX,QAAQ,IAAI;AACZ,QAAQ,GAAG;AACX,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,IAAI;AACZ,KAAK,CAAC;AACN,EAAC;AACD,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,CAAC,EAAE;AACnB,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ;AAC5E,CAAC;AACD;AACA,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM;AAC7B,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AACvC,QAAQ,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AAC3C,YAAY,MAAM,EAAE,IAAI,EAAE,GAAG,KAAI;AACjC;AACA,YAAY,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,UAAU,EAAE;AAClD,gBAAgB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAC7D,aAAa;AACb;AACA,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT;AACA,QAAQ,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACnD,YAAY,MAAM,EAAE,IAAI,EAAE,GAAG,KAAI;AACjC;AACA,YAAY,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACtE,gBAAgB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAC;AACvC;AACA,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC1C,oBAAoB,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE;AACjD,wBAAwB;AACxB,4BAA4B,MAAM,CAAC,OAAO,CAAC;AAC3C,4BAA4B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;AACtE,0BAA0B;AAC1B,4BAA4B,OAAO,IAAI;AACvC,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,MAAM;AACvB,oBAAoB,MAAM,CAAC,KAAK,CAAC;AACjC,oBAAoB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC;AAC5D,kBAAkB;AAClB,oBAAoB,OAAO,IAAI;AAC/B,iBAAiB;AACjB,aAAa;AACb;AACA,YAAY,OAAO,KAAK;AACxB,SAAS;AACT;AACA,QAAQ,uBAAuB,GAAG;AAClC,YAAY,OAAO,KAAK;AACxB,SAAS;AACT,QAAQ,oBAAoB,GAAG;AAC/B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,eAAe,GAAG;AAC1B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACrD,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC1D,iBAAiB,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC;AAC/E,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT,QAAQ,cAAc,GAAG;AACzB,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,kBAAkB,GAAG;AAC7B,YAAY,OAAO,KAAK;AACxB,SAAS;AACT,QAAQ,gBAAgB,GAAG;AAC3B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACrD,YAAY,IAAI,OAAO,CAAC,eAAe,EAAE;AACzC,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,IAAI,CAAC,QAAQ;AAC7B,gBAAgB,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;AAChD,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT,QAAQ,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACrD,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,IAAI,CAAC,QAAQ;AAC7B,gBAAgB,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS;AAC3C,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT,QAAQ,aAAa,GAAG;AACxB,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AAC7C,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,IAAI,CAAC,QAAQ;AAC7B,gBAAgB,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS;AAC3C,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT,QAAQ,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACpD,YAAY,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAC5C,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;AACzD,gBAAgB,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;AAChD,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT,QAAQ,gBAAgB,GAAG;AAC3B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,eAAe,GAAG;AAC1B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,KAAK,CAAC;AACN,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,aAAa;AAC7B,IAAI,IAAI;AACR,IAAI,UAAU;AACd,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,8BAA8B,GAAG,KAAK,EAAE,GAAG,EAAE;AAC5E,EAAE;AACF,IAAI,OAAO,OAAO,CAAC,MAAM;AACzB,QAAQ,IAAI;AACZ,QAAQ,EAAE,eAAe,EAAE,8BAA8B,EAAE;AAC3D,QAAQ,UAAU,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI;AAC1C,KAAK;AACL,CAAC;;ACjLD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE;AAChD,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;AAC9B;AACA,IAAI,QAAQ,MAAM,CAAC,IAAI;AACvB,QAAQ,KAAK,gBAAgB,CAAC;AAC9B,QAAQ,KAAK,eAAe;AAC5B,YAAY,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;AAC/E,gBAAgB,OAAO,UAAU,CAAC,aAAa;AAC/C,oBAAoB,MAAM,CAAC,MAAM;AACjC,oBAAoB,mBAAmB;AACvC,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,kBAAkB;AAC/B,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;AACtC,gBAAgB,OAAO,UAAU,CAAC,aAAa;AAC/C,oBAAoB,MAAM,CAAC,IAAI;AAC/B,oBAAoB,mBAAmB;AACvC,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,aAAa,CAAC;AAC3B,QAAQ,KAAK,gBAAgB;AAC7B,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;AACtC,gBAAgB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1D,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,kBAAkB;AAC/B,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;AACxC,gBAAgB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1D,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,iBAAiB;AAC9B,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,IAAI,EAAE;AAC9C,gBAAgB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1D,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,eAAe;AAC5B,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;AACxC,gBAAgB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1D,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ;AACR,YAAY,OAAO,IAAI;AACvB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,eAAe;AAC/B,IAAI,WAAW;AACf,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,EAAE;AACF,IAAI,IAAI,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAe;AAChE,IAAI,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AACzC,QAAQ,KAAK,GAAG,WAAW,GAAG,EAAC;AAC/B,QAAQ,IAAI,GAAG,iBAAgB;AAC/B,QAAQ,UAAU,GAAG,mBAAkB;AACvC,QAAQ,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE;AAC3B,YAAY,MAAM,IAAI,SAAS,CAAC,uCAAuC,CAAC;AACxE,SAAS;AACT,KAAK,MAAM;AACX,QAAQ,KAAK,GAAG,EAAC;AACjB,QAAQ,IAAI,GAAG,YAAW;AAC1B,QAAQ,UAAU,GAAG,iBAAgB;AACrC,KAAK;AACL;AACA,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACtB,QAAQ,OAAO,KAAK;AACpB,KAAK;AACL;AACA,IAAI,cAAc,GAAG,eAAe,GAAG,KAAI;AAC3C,IAAI,GAAG;AACP,QAAQ,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC,cAAc,EAAC;AAClE,QAAQ,eAAe,GAAG,UAAU,CAAC,aAAa,CAAC,eAAe,EAAC;AACnE,KAAK;AACL,QAAQ,cAAc,IAAI,IAAI;AAC9B,QAAQ,eAAe,IAAI,IAAI;AAC/B,QAAQ,mBAAmB,CAAC,cAAc,CAAC;AAC3C,QAAQ,mBAAmB,CAAC,eAAe,CAAC;AAC5C;AACA,QAAQ,cAAc,KAAK,oBAAoB,CAAC,IAAI,EAAE,UAAU,CAAC;AACjE,QAAQ,EAAE,KAAK,GAAG,CAAC;AACnB,KAAK;AACL;AACA,IAAI,OAAO,KAAK,KAAK,CAAC;AACtB,CAAC;;ACjHD;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,GAAG,6BAA4B;AAChD;AACA;AACA,MAAM,QAAQ,GAAG,IAAI,OAAO,GAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE;AAC/B,IAAI,IAAI,OAAO,GAAG,MAAK;AACvB,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC,EAAE;AACvE,QAAQ,OAAO,GAAG,CAAC,QAAO;AAC1B,KAAK;AACL,IAAI,OAAO,OAAO;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE;AAC7C,IAAI,MAAM,MAAM,GAAG,GAAE;AACrB,IAAI,IAAI,KAAK,GAAG,EAAC;AACjB;AACA;AACA,IAAI,IAAI,KAAK,GAAG,KAAI;AACpB;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,QAAQ,CAAC,GAAG,EAAE;AAC3B,QAAQ,QAAQ,GAAG;AACnB,YAAY,KAAK,IAAI;AACrB,gBAAgB,OAAO,GAAG;AAC1B,YAAY,KAAK,IAAI;AACrB,gBAAgB,OAAO,KAAK,CAAC,CAAC,CAAC;AAC/B,YAAY,KAAK,IAAI;AACrB,gBAAgB,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC;AAChD,YAAY,KAAK,IAAI;AACrB,gBAAgB,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC/D,YAAY,SAAS;AACrB,gBAAgB,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC;AACtC,gBAAgB,IAAI,CAAC,IAAI,KAAK,EAAE;AAChC,oBAAoB,OAAO,KAAK,CAAC,CAAC,CAAC;AACnC,iBAAiB;AACjB,gBAAgB,OAAO,GAAG;AAC1B,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA,IAAI,KAAK,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACxC,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAC;AAClD,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAC;AAC/D,QAAQ,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;AAC7C,KAAK;AACL,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAC;AACjC;AACA,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE;AACzC,IAAI,MAAM,MAAM,GAAG,GAAE;AACrB,IAAI,IAAI,KAAK,GAAG,EAAC;AACjB;AACA,IAAI,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AAC9C,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAC;AAClD,QAAQ,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAC;AACxE,QAAQ,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;AAC7C,KAAK;AACL,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAC;AACjC;AACA,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA,AAAO,MAAM,cAAc,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;AACnD,QAAQ,IAAI,EAAE,OAAO,YAAY,MAAM,CAAC,EAAE;AAC1C,YAAY,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC;AACzE,SAAS;AACT,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAY,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC;AAClE,SAAS;AACT;AACA,QAAQ,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE;AAC3B,YAAY,OAAO,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC;AAC9D,YAAY,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC;AACrC,SAAS,EAAC;AACV,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;AAClB,QAAQ,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAC;AACvD,QAAQ,IAAI,KAAK,GAAG,KAAI;AACxB,QAAQ,IAAI,SAAS,GAAG,EAAC;AACzB;AACA,QAAQ,OAAO,CAAC,SAAS,GAAG,EAAC;AAC7B,QAAQ,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;AACpD,YAAY,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;AACzD,gBAAgB,SAAS,GAAG,OAAO,CAAC,UAAS;AAC7C,gBAAgB,MAAM,MAAK;AAC3B,gBAAgB,OAAO,CAAC,SAAS,GAAG,UAAS;AAC7C,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAC;AACpC,QAAQ,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,GAAE;AAC7B,QAAQ,OAAO,CAAC,GAAG,CAAC,IAAI;AACxB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE;AACpC,QAAQ,OAAO,OAAO,QAAQ,KAAK,UAAU;AAC7C,cAAc,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;AACnD,cAAc,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC3D,KAAK;AACL,CAAC;;AC1JD,MAAM,WAAW,GAAG,uDAAsD;AAC1E,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAC;AACrD;AACA,AAAY,MAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAC;AAClC,AAAY,MAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAC;AAClC,AAAY,MAAC,SAAS,GAAG,MAAM,CAAC,WAAW,EAAC;AAC5C,AAAY,MAAC,GAAG,GAAG,MAAM,CAAC,KAAK,EAAC;AAChC;AACA,MAAM,WAAW,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,GAAE;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AACpC,IAAI;AACJ,QAAQ,QAAQ,IAAI,IAAI;AACxB,QAAQ,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAClC,QAAQ,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;AAClD,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,IAAI,EAAE;AAC7B,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;AAC9B;AACA,IAAI,QAAQ,MAAM,IAAI,MAAM,CAAC,IAAI;AACjC,QAAQ,KAAK,uBAAuB;AACpC,YAAY,OAAO,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI;AAC1E,QAAQ,KAAK,mBAAmB;AAChC,YAAY,OAAO,IAAI;AACvB,QAAQ,KAAK,oBAAoB;AACjC,YAAY,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI;AAC7E,QAAQ,KAAK,iBAAiB;AAC9B,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ;AACR,YAAY,OAAO,KAAK;AACxB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,AAAO,MAAM,gBAAgB,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW;AACf,QAAQ,WAAW;AACnB,QAAQ;AACR,YAAY,IAAI,GAAG,QAAQ;AAC3B,YAAY,iBAAiB,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC1E,SAAS,GAAG,EAAE;AACd,MAAM;AACN,QAAQ,IAAI,CAAC,aAAa,GAAG,GAAE;AAC/B,QAAQ,IAAI,CAAC,WAAW,GAAG,YAAW;AACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,KAAI;AACxB,QAAQ,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAC;AAC3D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE;AACvC,QAAQ,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACjD,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAC9C,YAAY,MAAM,IAAI,GAAG,CAAC,GAAG,EAAC;AAC9B,YAAY,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAC;AAC1D;AACA,YAAY,IAAI,gBAAgB,CAAC,QAAQ,CAAC,EAAE;AAC5C,gBAAgB,QAAQ;AACxB,aAAa;AACb;AACA,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD,gBAAgB,QAAQ;AACxB,gBAAgB,IAAI;AACpB,gBAAgB,YAAY;AAC5B,gBAAgB,IAAI;AACpB,cAAa;AACb,SAAS;AACT;AACA,QAAQ,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAClD,YAAY,MAAM,IAAI,GAAG,GAAE;AAC3B,YAAY,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAC;AAC1D;AACA,YAAY,IAAI,gBAAgB,CAAC,QAAQ,CAAC,EAAE;AAC5C,gBAAgB,QAAQ;AACxB,aAAa;AACb;AACA,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD,gBAAgB,QAAQ;AACxB,gBAAgB,IAAI;AACpB,gBAAgB,QAAQ;AACxB,gBAAgB,KAAK;AACrB,cAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE;AACpC,QAAQ,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;AAC1E,YAAY,MAAM,GAAG,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAC;AAC9D,YAAY,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACpD,gBAAgB,QAAQ;AACxB,aAAa;AACb;AACA,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAC9C,YAAY,MAAM,IAAI,GAAG,CAAC,GAAG,EAAC;AAC9B;AACA,YAAY,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACpC,gBAAgB,MAAM;AACtB,oBAAoB,IAAI;AACxB,oBAAoB,IAAI;AACxB,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAC5C,kBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAC;AAC5E,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE;AACpC,QAAQ,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAK;AAClD;AACA,QAAQ,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE;AAC7C,YAAY,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;AACrE,gBAAgB,QAAQ;AACxB,aAAa;AACb,YAAY,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAK;AAC9C;AACA,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAC1C,gBAAgB,QAAQ;AACxB,aAAa;AACb,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAC;AACnD,YAAY,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAC;AACnC;AACA,YAAY,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACpC,gBAAgB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,GAAE;AAC1E,aAAa;AACb;AACA,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAsB,EAAE;AACtD,gBAAgB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AAC7D,oBAAoB,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,EAAC;AAC5D,oBAAoB,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;AAC9C,wBAAwB,MAAM;AAC9B,4BAA4B,IAAI;AAChC,4BAA4B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;AAClD,4BAA4B,IAAI,EAAE,IAAI;AACtC,4BAA4B,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC;AACtD,0BAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa,MAAM;AACnB,gBAAgB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;AACzD,oBAAoB,MAAM,GAAG,GAAG,GAAG,CAAC,YAAY,EAAE,GAAG,EAAC;AACtD,oBAAoB,MAAM,EAAE,GAAG,IAAI,CAAC,wBAAwB;AAC5D,wBAAwB,SAAS;AACjC,wBAAwB,IAAI;AAC5B,wBAAwB,GAAG;AAC3B,8BAA8B,YAAY;AAC1C,8BAA8B,IAAI,CAAC,IAAI,KAAK,QAAQ;AACpD,8BAA8B,MAAM,CAAC,MAAM;AAC3C,kCAAkC,EAAE,OAAO,EAAE,YAAY,EAAE;AAC3D,kCAAkC,YAAY;AAC9C,+BAA+B;AAC/B,8BAA8B,EAAE,OAAO,EAAE,YAAY,EAAE;AACvD,sBAAqB;AACrB;AACA,oBAAoB,IAAI,GAAG,EAAE;AAC7B,wBAAwB,OAAO,GAAE;AACjC,qBAAqB,MAAM;AAC3B,wBAAwB,KAAK,MAAM,MAAM,IAAI,EAAE,EAAE;AACjD,4BAA4B,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAC;AAC3E,4BAA4B;AAC5B,gCAAgC,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AACvD,gCAAgC,MAAM,CAAC,IAAI,KAAK,IAAI;AACpD,8BAA8B;AAC9B,gCAAgC,MAAM,OAAM;AAC5C,6BAA6B;AAC7B,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE;AACxE,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACnD,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAC;AACzC,QAAQ,IAAI;AACZ,YAAY,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;AACzD,gBAAgB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;AACzC,oBAAoB,QAAQ;AAC5B,iBAAiB;AACjB,gBAAgB,MAAM,IAAI,GAAG,SAAS,CAAC,WAAU;AACjD;AACA,gBAAgB,IAAI,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;AACpD,oBAAoB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAE;AAC1E,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC5E,aAAa;AACb,SAAS,SAAS;AAClB,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,GAAE;AACpC,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC1D,QAAQ,IAAI,IAAI,GAAG,SAAQ;AAC3B,QAAQ,OAAO,aAAa,CAAC,IAAI,CAAC,EAAE;AACpC,YAAY,IAAI,GAAG,IAAI,CAAC,OAAM;AAC9B,SAAS;AACT;AACA,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAM;AAClC,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;AAChD,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;AACxC,gBAAgB,MAAM,GAAG,GAAG,eAAe,CAAC,MAAM,EAAC;AACnD,gBAAgB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACxD,oBAAoB,MAAM;AAC1B,iBAAiB;AACjB;AACA,gBAAgB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;AACvC,gBAAgB,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAClD,gBAAgB,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACxC,oBAAoB,MAAM;AAC1B,wBAAwB,IAAI,EAAE,MAAM;AACpC,wBAAwB,IAAI;AAC5B,wBAAwB,IAAI,EAAE,IAAI;AAClC,wBAAwB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAChD,sBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC,0BAA0B;AACtD,oBAAoB,MAAM;AAC1B,oBAAoB,IAAI;AACxB,oBAAoB,YAAY;AAChC,kBAAiB;AACjB,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAAE;AAC9C,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC1D,gBAAgB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAE;AAC9E,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE;AAC7C,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC/D,gBAAgB,MAAM;AACtB,oBAAoB,IAAI,EAAE,MAAM;AAChC,oBAAoB,IAAI;AACxB,oBAAoB,IAAI,EAAE,SAAS;AACnC,oBAAoB,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC;AAC7C,kBAAiB;AACjB,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAsB,EAAE;AACpD,YAAY,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;AACvC,gBAAgB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC9E,gBAAgB,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC9E,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACjD,YAAY,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;AACvC,gBAAgB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC9E,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,oBAAoB,EAAE;AAClD,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;AACtC,gBAAgB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC5E,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE;AACxD,QAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,YAAY,EAAE;AAC/C,YAAY,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,EAAC;AACxE,YAAY,IAAI,QAAQ,IAAI,IAAI,EAAE;AAClC,gBAAgB,OAAO,IAAI,CAAC,0BAA0B;AACtD,oBAAoB,QAAQ;AAC5B,oBAAoB,IAAI;AACxB,oBAAoB,QAAQ;AAC5B,oBAAoB,KAAK;AACzB,kBAAiB;AACjB,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,eAAe,EAAE;AAClD,YAAY,KAAK,MAAM,QAAQ,IAAI,WAAW,CAAC,UAAU,EAAE;AAC3D,gBAAgB,MAAM,GAAG,GAAG,eAAe,CAAC,QAAQ,EAAC;AACrD;AACA,gBAAgB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACxD,oBAAoB,QAAQ;AAC5B,iBAAiB;AACjB;AACA,gBAAgB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;AACjD,gBAAgB,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAClD,gBAAgB,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACxC,oBAAoB,MAAM;AAC1B,wBAAwB,IAAI,EAAE,QAAQ;AACtC,wBAAwB,IAAI,EAAE,QAAQ;AACtC,wBAAwB,IAAI,EAAE,IAAI;AAClC,wBAAwB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAChD,sBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC,qBAAqB;AACjD,oBAAoB,QAAQ,CAAC,KAAK;AAClC,oBAAoB,QAAQ;AAC5B,oBAAoB,YAAY;AAChC,kBAAiB;AACjB,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACtD,YAAY,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC/E,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC7D,QAAQ,MAAM,IAAI,GAAG,aAAa,CAAC,KAAI;AACvC;AACA,QAAQ,IAAI,IAAI,KAAK,iBAAiB,IAAI,IAAI,KAAK,wBAAwB,EAAE;AAC7E,YAAY,MAAM,GAAG;AACrB,gBAAgB,IAAI,KAAK,wBAAwB;AACjD,sBAAsB,SAAS;AAC/B,sBAAsB,aAAa,CAAC,QAAQ,CAAC,KAAI;AACjD,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACrC,gBAAgB,MAAM;AACtB,aAAa;AACb;AACA,YAAY,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;AACnC,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAC9C,YAAY,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACpC,gBAAgB,MAAM;AACtB,oBAAoB,IAAI,EAAE,aAAa;AACvC,oBAAoB,IAAI;AACxB,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAC5C,kBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD,gBAAgB,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC;AACnE,gBAAgB,IAAI;AACpB,gBAAgB,YAAY;AAC5B,gBAAgB,KAAK;AACrB,cAAa;AACb;AACA,YAAY,MAAM;AAClB,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,KAAK,0BAA0B,EAAE;AACjD,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD,gBAAgB,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC;AACnE,gBAAgB,IAAI;AACpB,gBAAgB,QAAQ;AACxB,gBAAgB,KAAK;AACrB,cAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,KAAK,iBAAiB,EAAE;AACxC,YAAY,MAAM,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,KAAI;AAChD,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACrC,gBAAgB,MAAM;AACtB,aAAa;AACb;AACA,YAAY,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;AACnC,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAC9C,YAAY,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACpC,gBAAgB,MAAM;AACtB,oBAAoB,IAAI,EAAE,aAAa;AACvC,oBAAoB,IAAI;AACxB,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAC5C,kBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK;AACL,CAAC;AACD;AACA,gBAAgB,CAAC,IAAI,GAAG,KAAI;AAC5B,gBAAgB,CAAC,IAAI,GAAG,KAAI;AAC5B,gBAAgB,CAAC,SAAS,GAAG,UAAS;AACtC,gBAAgB,CAAC,GAAG,GAAG,IAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE;AACpC,IAAI,OAAO,EAAE,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,SAAS,CAAC;AAC/C,CAAC;;AC1ZD,YAAe;AACf,IAAI,IAAI;AACR,IAAI,SAAS;AACb,IAAI,GAAG;AACP,IAAI,YAAY;AAChB,IAAI,uBAAuB;AAC3B,IAAI,uBAAuB;AAC3B,IAAI,iBAAiB;AACrB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AACvB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,sBAAsB;AAC1B,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,sBAAsB;AAC1B,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AACvB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,IAAI;AACR,IAAI,gBAAgB;AACpB,CAAC;;;;;"}