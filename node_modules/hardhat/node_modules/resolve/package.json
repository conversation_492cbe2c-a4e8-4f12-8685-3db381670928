{"name": "resolve", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "version": "1.17.0", "repository": {"type": "git", "url": "git://github.com/browserify/resolve.git"}, "main": "index.js", "keywords": ["resolve", "require", "node", "module"], "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "tests-only": "tape test/*.js", "pretest": "npm run lint", "test": "npm run --silent tests-only", "posttest": "npm run test:multirepo", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "devDependencies": {"@ljharb/eslint-config": "^16.0.0", "array.prototype.map": "^1.0.2", "eslint": "^6.8.0", "object-keys": "^1.1.1", "safe-publish-latest": "^1.1.4", "tap": "0.4.13", "tape": "^5.0.0-next.5"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "dependencies": {"path-parse": "^1.0.6"}}