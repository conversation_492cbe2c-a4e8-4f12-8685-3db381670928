{"version": 3, "file": "autocomplete.js", "sourceRoot": "", "sources": ["../../src/internal/cli/autocomplete.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sDAA6B;AAC7B,6CAA+B;AAC/B,2CAA6B;AAG7B,kEAA0E;AAC1E,mDAAiD;AACjD,uCAAyE;AACzE,uCAAyC;AAEzC,uDAAoD;AAoDvC,QAAA,sBAAsB,GAAG,4BAA4B,CAAC;AAEtD,QAAA,yBAAyB,GAAG,QAAQ,CAAC;AAE3C,KAAK,UAAU,QAAQ,CAAC,EAC7B,IAAI,EACJ,KAAK,GACS;IACd,MAAM,cAAc,GAAG,MAAM,iBAAiB,EAAE,CAAC;IAEjD,IAAI,cAAc,KAAK,SAAS,EAAE;QAChC,OAAO,EAAE,CAAC;KACX;IAED,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;IAEnD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE5D,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC5D,wCAAwC;IACxC,+DAA+D;IAC/D,yDAAyD;IACzD,6DAA6D;IAC7D,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjD,MAAM,cAAc,GAAG,CAAC,UAAkB,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAE3E,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,0CAAyB,CAAC;SACxD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACf,IAAI,EAAE,iCAAe,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC;QAChD,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;KACrC,CAAC,CAAC;SACF,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAE1C,oDAAoD;IACpD,IAAI,QAA4B,CAAC;IACjC,IAAI,SAA6B,CAAC;IAElC,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,OAAO,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE;QAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;YACtB,KAAK,IAAI,CAAC,CAAC;SACZ;aAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;YAC9B,KAAK,IAAI,CAAC,CAAC;SACZ;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YAChC,KAAK,IAAI,CAAC,CAAC;SACZ;aAAM;YACL,sBAAsB;YACtB,4BAA4B;YAC5B,4BAA4B;YAC5B,8BAA8B;YAC9B,iFAAiF;YACjF,uFAAuF;YACvF,IAAI,SAAS,KAAK,SAAS,EAAE;gBAC3B,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;oBAC7B,QAAQ,GAAG,IAAI,CAAC;oBAChB,MAAM;iBACP;qBAAM,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;oBACrC,SAAS,GAAG,IAAI,CAAC;iBAClB;aACF;iBAAM;gBACL,QAAQ,GAAG,IAAI,CAAC;gBAChB,MAAM;aACP;YAED,KAAK,IAAI,CAAC,CAAC;SACZ;KACF;IAED,kEAAkE;IAClE,wEAAwE;IACxE,mHAAmH;IACnH,kFAAkF;IAClF,sBAAsB;IACtB,gEAAgE;IAChE,8EAA8E;IAC9E,gFAAgF;IAChF,+HAA+H;IAC/H,IAAI,QAAQ,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;QAC3C,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,EAAE;YACrD,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SAChD;aAAM;YACL,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SAChD;KACF;IAED,IAAI,IAAI,KAAK,WAAW,EAAE;QACxB,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YACvD,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,EAAE;SAChB,CAAC,CAAC,CAAC;KACL;IAED,MAAM,eAAe,GACnB,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAE1D,MAAM,cAAc,GAClB,QAAQ,KAAK,SAAS;QACpB,CAAC,CAAC,SAAS;QACX,CAAC,CAAC,eAAe,KAAK,SAAS;YAC/B,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;YACjB,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAEtC,4DAA4D;IAC5D,kCAAkC;IAClC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACxB,MAAM,SAAS,GAAG,iCAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAEvD,MAAM,WAAW,GAAG,0CAAyB,CAAC,SAAwB,CAAC,CAAC;QACxE,IAAI,WAAW,KAAK,SAAS,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACpD,OAAO,8BAAsB,CAAC;SAC/B;QAED,MAAM,WAAW,GACf,cAAc,EAAE,gBAAgB,CAAC,SAAS,CAAC,EAAE,MAAM,KAAK,KAAK,CAAC;QAEhE,IAAI,WAAW,EAAE;YACf,OAAO,8BAAsB,CAAC;SAC/B;KACF;IAED,6EAA6E;IAC7E,IAAI,cAAc,KAAK,SAAS,IAAI,eAAe,KAAK,SAAS,EAAE;QACjE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACxB,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;SACjE;QAED,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;aACzC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aAC3B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACX,IAAI,EAAE,CAAC,CAAC,IAAI;YACZ,WAAW,EAAE,CAAC,CAAC,WAAW;SAC3B,CAAC,CAAC,CAAC;QAEN,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACzD,IAAI,EAAE,CAAC,CAAC,IAAI;YACZ,WAAW,EAAE,CAAC,CAAC,WAAW;SAC3B,CAAC,CAAC,CAAC;QAEJ,OAAO,eAAe;aACnB,MAAM,CAAC,gBAAgB,CAAC;aACxB,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;KAC1C;IAED,uEAAuE;IACvE,IAAI,cAAc,KAAK,SAAS,IAAI,eAAe,KAAK,SAAS,EAAE;QACjE,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAU,CAAC,CAAC,KAAK,CAAC;aAC3C,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aAC3B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACX,IAAI,EAAE,CAAC,CAAC,IAAI;YACZ,WAAW,EAAE,CAAC,CAAC,WAAW;SAC3B,CAAC,CAAC;aACF,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;KAC1C;IAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACzB,OAAO,8BAAsB,CAAC;KAC/B;IAED,MAAM,UAAU,GACd,cAAc,KAAK,SAAS;QAC1B,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;aAC3C,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACf,IAAI,EAAE,iCAAe,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC;YAChD,WAAW,EAAE,KAAK,CAAC,WAAW;SAC/B,CAAC,CAAC;aACF,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAEhD,OAAO,CAAC,GAAG,UAAU,EAAE,GAAG,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE,CAC1D,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAChC,CAAC;AACJ,CAAC;AA1KD,4BA0KC;AAED,KAAK,UAAU,iBAAiB;IAC9B,MAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IAEjC,IAAI,SAAS,KAAK,SAAS,EAAE;QAC3B,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,oBAAoB,GAAG,MAAM,uBAAuB,CAAC,SAAS,CAAC,CAAC;IAEtE,IAAI,oBAAoB,KAAK,SAAS,EAAE;QACtC,IAAI,wBAAwB,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;YACzD,OAAO,oBAAoB,CAAC,cAAc,CAAC;SAC5C;KACF;IAED,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACtD,IAAI,GAA8B,CAAC;IACnC,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,GAAG,CAAC;QACzC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC1B,GAAG,GAAI,MAAc,CAAC,GAAG,CAAC;KAC3B;IAAC,MAAM;QACN,OAAO,SAAS,CAAC;KAClB;IACD,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACrD,MAAM,MAAM,GAAG,SAAS,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;IAEhE,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAElD,+DAA+D;IAC/D,sEAAsE;IACtE,MAAM,KAAK,GAA4B,IAAA,gBAAS,EAAC,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CACnE,yBAAyB,CAAC,IAAI,CAAC,CAChC,CAAC;IAEF,MAAM,MAAM,GAA6B,IAAA,gBAAS,EAAC,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACzE,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;QACpC,KAAK,EAAE,IAAA,gBAAS,EAAC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;KACzE,CAAC,CAAC,CAAC;IAEJ,MAAM,cAAc,GAAmB;QACrC,QAAQ;QACR,KAAK;QACL,MAAM;KACP,CAAC;IAEF,MAAM,wBAAwB,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;IAElE,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,yBAAyB,CAAC,OAAuB;IACxD,OAAO;QACL,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;QACtC,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,gBAAgB,EAAE,IAAA,gBAAS,EACzB,OAAO,CAAC,gBAAgB,EACxB,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;YACpB,IAAI,EAAE,eAAe,CAAC,IAAI;YAC1B,WAAW,EAAE,eAAe,CAAC,WAAW,IAAI,EAAE;YAC9C,MAAM,EAAE,eAAe,CAAC,MAAM;SAC/B,CAAC,CACH;KACF,CAAC;AACJ,CAAC;AAED,SAAS,YAAY;IACnB,MAAM,eAAe,GAAG,iBAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAEpD,IAAI,eAAe,KAAK,SAAS,EAAE;QACjC,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,IAAA,gDAAyC,EAC9C,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAC7B,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpB,CAAC;AAED,SAAS,wBAAwB,CAAC,MAAc;IAC9C,IAAI;QACF,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CACjC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,KAAK,CAC/D,CAAC;KACH;IAAC,MAAM;QACN,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,SAAS,SAAS,CAAC,iBAA2B,EAAE,gBAA0B;IACxE,MAAM,eAAe,GAAG,gBAAgB,CAAC,MAAM,CAC7C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CACtC,CAAC;IACF,MAAM,KAAK,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAEzD,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE;KAC9B,CAAC,CAAC,CAAC;IAEJ,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,EAAE,CAAC;KACX;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,KAAK,UAAU,uBAAuB,CACpC,SAAiB;IAEjB,MAAM,wBAAwB,GAAG,MAAM,2BAA2B,CAAC,SAAS,CAAC,CAAC;IAE9E,IAAI,EAAE,CAAC,UAAU,CAAC,wBAAwB,CAAC,EAAE;QAC3C,IAAI;YACF,MAAM,oBAAoB,GAAG,EAAE,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;YACvE,OAAO,oBAAoB,CAAC;SAC7B;QAAC,MAAM;YACN,sCAAsC;YACtC,EAAE,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC;YACxC,OAAO,SAAS,CAAC;SAClB;KACF;AACH,CAAC;AAED,KAAK,UAAU,wBAAwB,CACrC,SAAiB,EACjB,cAA8B,EAC9B,MAAc;IAEd,MAAM,wBAAwB,GAAG,MAAM,2BAA2B,CAAC,SAAS,CAAC,CAAC;IAE9E,MAAM,EAAE,CAAC,UAAU,CAAC,wBAAwB,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC;AAC5E,CAAC;AAED,KAAK,UAAU,2BAA2B,CAAC,SAAiB;IAC1D,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAW,GAAE,CAAC;IAErC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,EAAE,GAAG,SAAS,OAAO,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,YAAY,CAAC,KAAa;IACjC,MAAM,SAAS,GAAG,iCAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IACxD,OAAO,0CAAyB,CAAC,SAAwB,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC;AAC9E,CAAC;AAED,SAAS,aAAa,CAAC,KAAa;IAClC,MAAM,SAAS,GAAG,iCAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IACxD,OAAO,0CAAyB,CAAC,SAAwB,CAAC,EAAE,MAAM,KAAK,KAAK,CAAC;AAC/E,CAAC"}