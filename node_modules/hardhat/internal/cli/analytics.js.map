{"version": 3, "file": "analytics.js", "sourceRoot": "", "sources": ["../../src/internal/cli/analytics.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,kDAA0B;AAC1B,sDAAyB;AACzB,yCAAiC;AACjC,2DAA2C;AAE3C,2DAAoD;AACpD,uDAA2D;AAC3D,mDAM4B;AAC5B,qDAAqD;AACrD,qCAAmD;AAEnD,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,wBAAwB,CAAC,CAAC;AA0D5C,MAAa,SAAS;IACb,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,gBAAqC;QACnE,MAAM,SAAS,GAAc,IAAI,SAAS,CACxC,MAAM,WAAW,EAAE,EACnB,gBAAgB,EAChB,WAAW,EAAE,CACd,CAAC;QAEF,OAAO,SAAS,CAAC;IACnB,CAAC;IAWD,YACE,QAAgB,EAChB,gBAAqC,EACrC,QAAgB;QATD,kBAAa,GAC5B,6CAA6C,CAAC;QAC/B,eAAU,GAAW,wBAAwB,CAAC;QAC9C,mBAAc,GAAW,cAAc,CAAC;QAQvD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,QAAQ;YACX,CAAC,IAAA,2BAAU,GAAE,IAAI,CAAC,IAAA,kCAAmB,GAAE,IAAI,gBAAgB,KAAK,IAAI,CAAC;QACvE,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,WAAW,CACtB,SAA6B,EAC7B,QAAgB;QAEhB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO,CAAC,GAAG,EAAE,GAAE,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;SACtC;QAED,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IACE,CAAC,SAAS,KAAK,UAAU,IAAI,QAAQ,KAAK,QAAQ,CAAC;YACnD,CAAC,SAAS,KAAK,SAAS,IAAI,QAAQ,KAAK,QAAQ,CAAC,EAClD;YACA,WAAW,GAAG;gBACZ,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,QAAQ;aACf,CAAC;SACH;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;IACrE,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAClC,WAAyB;QAEzB,MAAM,0BAA0B,GAA+B;YAC7D,SAAS,EAAE,2BAA2B;YACtC,OAAO,EAAE,2BAA2B;YACpC,eAAe,EAAE,EAAE;YACnB,MAAM,EAAE;gBACN;oBACE,IAAI,EAAE,0BAA0B;oBAChC,MAAM,EAAE;wBACN,WAAW;qBACZ;iBACF;aACF;SACF,CAAC;QACF,OAAO,IAAI,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,cAGI,EAAE;QAEN,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,SAAS;YACvB,eAAe,EAAE;gBACf,SAAS,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE;gBACvC,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE;gBACnC,cAAc,EAAE,EAAE,KAAK,EAAE,MAAM,iBAAiB,EAAE,EAAE;gBACpD,eAAe,EAAE,EAAE,KAAK,EAAE,iBAAE,CAAC,QAAQ,EAAE,EAAE;gBACzC,WAAW,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE;aACxC;YACD,MAAM,EAAE;gBACN;oBACE,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE;wBACN,gEAAgE;wBAChE,gDAAgD;wBAChD,mEAAmE;wBACnE,iEAAiE;wBACjE,oBAAoB,EAAE,OAAO;wBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;wBAC3B,GAAG,WAAW;qBACf;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IAEO,QAAQ,CAAC,OAAyB;QACxC,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAiC,CAAC;QAEtE,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzC,GAAG,CAAC,mBAAmB,SAAS,EAAE,CAAC,CAAC;QAEpC,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QAEzC,MAAM,cAAc,GAAG,GAAG,EAAE;YAC1B,GAAG,CAAC,oBAAoB,SAAS,EAAE,CAAC,CAAC;YAErC,UAAU,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC,CAAC;QAEF,GAAG,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAE/C,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;YAC7C,KAAK,EAAE;gBACL,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;aACpC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC7B,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,UAAU,CAAC,MAAM;SAC1B,CAAC;aACC,IAAI,CAAC,GAAG,EAAE;YACT,GAAG,CAAC,WAAW,SAAS,oBAAoB,CAAC,CAAC;QAChD,CAAC,CAAC;aACD,KAAK,CAAC,GAAG,EAAE;YACV,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEL,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;CACF;AAtJD,8BAsJC;AAED,KAAK,UAAU,WAAW;IACxB,IAAI,QAAQ,GAAG,MAAM,IAAA,4BAAe,GAAE,CAAC;IAEvC,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,QAAQ;YACN,CAAC,MAAM,IAAA,wCAA2B,GAAE,CAAC;gBACrC,CAAC,MAAM,IAAA,uCAA0B,GAAE,CAAC,CAAC;QAEvC,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,wDAAa,MAAM,GAAC,CAAC;YAC1C,GAAG,CAAC,2CAA2C,CAAC,CAAC;YACjD,QAAQ,GAAG,IAAI,EAAE,CAAC;SACnB;QAED,MAAM,IAAA,6BAAgB,EAAC,QAAQ,CAAC,CAAC;KAClC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,WAAW;IAClB,OAAO,IAAA,kCAAmB,GAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC;AACpD,CAAC;AAED,KAAK,UAAU,iBAAiB;IAC9B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,IAAA,4BAAc,GAAE,CAAC;IAE3C,OAAO,WAAW,OAAO,EAAE,CAAC;AAC9B,CAAC;AAEM,KAAK,UAAU,uBAAuB;IAC3C,MAAM,gBAAgB,GAAG,MAAM,IAAA,gCAAuB,GAAE,CAAC;IAEzD,IAAI,gBAAgB,KAAK,SAAS,EAAE;QAClC,OAAO;KACR;IAED,IAAA,kCAAqB,EAAC,gBAAgB,CAAC,CAAC;IAExC,MAAM,0BAA0B,GAAG,IAAA,gBAAI,EACrC,SAAS,EACT,IAAI,EACJ,MAAM,EACN,6BAA6B,CAC9B,CAAC;IAEF,MAAM,UAAU,GAAG,IAAA,0BAAK,EACtB,OAAO,CAAC,QAAQ,EAChB,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAC7D;QACE,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,QAAQ;KAChB,CACF,CAAC;IAEF,UAAU,CAAC,KAAK,EAAE,CAAC;IAEnB,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AA5BD,0DA4BC"}