{"version": 3, "file": "ArgumentsParser.js", "sourceRoot": "", "sources": ["../../src/internal/cli/ArgumentsParser.ts"], "names": [], "mappings": ";;;AAAA,+DAA2D;AAY3D,2CAA8C;AAC9C,qDAA6C;AAE7C,MAAa,eAAe;IAGnB,MAAM,CAAC,cAAc,CAAC,SAAiB;QAC5C,OAAO,CACL,eAAe,CAAC,YAAY;YAC5B,SAAS;iBACN,KAAK,CAAC,YAAY,CAAC;iBACnB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;iBAC3B,IAAI,CAAC,GAAG,CAAC,CACb,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,cAAc,CAAC,GAAW;QACtC,IAAI,GAAG,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;YAC7B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,yBAAyB,EAAE;gBACjE,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;SACJ;QAED,MAAM,KAAK,GAAG,GAAG;aACd,KAAK,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC;aAC1C,KAAK,CAAC,GAAG,CAAC;aACV,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE/B,OAAO,CACL,KAAK,CAAC,CAAC,CAAC;YACR,KAAK;iBACF,KAAK,CAAC,CAAC,CAAC;iBACR,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBAC3C,IAAI,CAAC,EAAE,CAAC,CACZ,CAAC;IACJ,CAAC;IAEM,qBAAqB,CAC1B,uBAAgD,EAChD,oBAAsC,EACtC,OAAiB;QAMjB,MAAM,gBAAgB,GAA8B,EAAE,CAAC;QACvD,IAAI,eAAmC,CAAC;QACxC,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAEvB,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,EAAE;oBACrC,eAAe,GAAG,GAAG,CAAC;oBACtB,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC1B,SAAS;iBACV;gBAED,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,uBAAuB,CAAC,EAAE;oBACvD,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,SAAS,CAAC,6BAA6B,EAC9C,EAAE,QAAQ,EAAE,GAAG,EAAE,CAClB,CAAC;iBACH;gBAED,CAAC,GAAG,IAAI,CAAC,gBAAgB,CACvB,OAAO,EACP,CAAC,EACD,uBAAuB,EACvB,gBAAgB,EAChB,eAAe,CAChB,CAAC;aACH;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,uBAAuB,CAAC,EAAE;oBACvD,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC1B,SAAS;iBACV;gBAED,CAAC,GAAG,IAAI,CAAC,gBAAgB,CACvB,OAAO,EACP,CAAC,EACD,uBAAuB,EACvB,gBAAgB,EAChB,eAAe,CAChB,CAAC;aACH;SACF;QAED,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,CAChD,uBAAuB,EACvB,oBAAoB,EACpB,gBAAgB,CACjB;YACD,eAAe;YACf,eAAe;SAChB,CAAC;IACJ,CAAC;IAEM,sBAAsB,CAC3B,eAAyB,EACzB,eAAyB,EACzB,gBAA2B;QAM3B,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,eAAe,CAAC;QAE9C,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,OAAO;gBACL,QAAQ,EAAE,sBAAS;gBACnB,YAAY,EAAE,EAAE;aACjB,CAAC;SACH;aAAM,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,IAAI,gBAAgB,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE;gBAC5C,6DAA6D;gBAC7D,gDAAgD;gBAChD,OAAO;oBACL,QAAQ,EAAE,sBAAS;oBACnB,YAAY,EAAE,CAAC,QAAQ,CAAC;iBACzB,CAAC;aACH;iBAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE;gBAClD,OAAO;oBACL,QAAQ,EAAE,QAAQ;oBAClB,YAAY,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;iBACvC,CAAC;aACH;iBAAM;gBACL,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE;oBACzD,IAAI,EAAE,QAAQ;iBACf,CAAC,CAAC;aACJ;SACF;aAAM;YACL,MAAM,eAAe,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,IAAI,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,SAAS,EAAE;oBAClD,OAAO;wBACL,SAAS,EAAE,QAAQ;wBACnB,QAAQ,EAAE,SAAS;wBACnB,YAAY,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;qBACvC,CAAC;iBACH;qBAAM;oBACL,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,wBAAwB,EAAE;wBAChE,KAAK,EAAE,QAAQ;wBACf,IAAI,EAAE,SAAS;qBAChB,CAAC,CAAC;iBACJ;aACF;iBAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE;gBAClD,OAAO;oBACL,QAAQ,EAAE,QAAQ;oBAClB,YAAY,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;iBACvC,CAAC;aACH;iBAAM;gBACL,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE;oBACzD,IAAI,EAAE,QAAQ;iBACf,CAAC,CAAC;aACJ;SACF;IACH,CAAC;IAEM,kBAAkB,CACvB,cAA8B,EAC9B,OAAiB;QAEjB,MAAM,EAAE,cAAc,EAAE,sBAAsB,EAAE,GAC9C,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAEzD,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CACxD,sBAAsB,EACtB,cAAc,CAAC,0BAA0B,CAC1C,CAAC;QAEF,OAAO,EAAE,GAAG,cAAc,EAAE,GAAG,mBAAmB,EAAE,CAAC;IACvD,CAAC;IAEO,wBAAwB,CAC9B,cAA8B,EAC9B,OAAiB;QAEjB,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,MAAM,sBAAsB,GAAa,EAAE,CAAC;QAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAEvB,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,EAAE;gBACrC,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACjC,SAAS;aACV;YAED,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,gBAAgB,CAAC,EAAE;gBAC/D,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,uBAAuB,EAAE;oBAC/D,KAAK,EAAE,GAAG;iBACX,CAAC,CAAC;aACJ;YAED,CAAC,GAAG,IAAI,CAAC,gBAAgB,CACvB,OAAO,EACP,CAAC,EACD,cAAc,CAAC,gBAAgB,EAC/B,cAAc,EACd,cAAc,CAAC,IAAI,CACpB,CAAC;SACH;QAED,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAE9D,OAAO,EAAE,cAAc,EAAE,sBAAsB,EAAE,CAAC;IACpD,CAAC;IAEO,2BAA2B,CACjC,uBAAgD,EAChD,oBAAsC,EACtC,gBAA2C;QAE3C,OAAO;YACL,GAAG,oBAAoB;YACvB,GAAG,gBAAgB;SACpB,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAC9B,cAA8B,EAC9B,aAA4B;QAE5B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE;YACpE,MAAM,UAAU,GAAG,cAAc,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAE9D,IAAI,aAAa,CAAC,SAAS,CAAC,KAAK,SAAS,EAAE;gBAC1C,SAAS;aACV;YACD,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;gBAC1B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE;oBAC7D,KAAK,EAAE,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC;oBAChD,IAAI,EAAE,cAAc,CAAC,IAAI;iBAC1B,CAAC,CAAC;aACJ;YAED,aAAa,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC;SACpD;IACH,CAAC;IAEO,eAAe,CAAC,GAAW,EAAE,gBAAqC;QACxE,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,EAAE;YACrC,OAAO,KAAK,CAAC;SACd;QAED,MAAM,IAAI,GAAG,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QACjD,OAAO,gBAAgB,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;IAC9C,CAAC;IAEO,sBAAsB,CAAC,GAAW;QACxC,OAAO,GAAG,CAAC,UAAU,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;IACtD,CAAC;IAEO,gBAAgB,CACtB,OAAiB,EACjB,KAAa,EACb,gBAAqC,EACrC,eAA8B,EAC9B,eAAwB;QAExB,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9B,MAAM,SAAS,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACzD,MAAM,UAAU,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAE/C,IAAI,eAAe,CAAC,SAAS,CAAC,KAAK,SAAS,EAAE;YAC5C,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,cAAc,EAAE;gBACtD,KAAK,EAAE,MAAM;aACd,CAAC,CAAC;SACJ;QAED,IAAI,UAAU,CAAC,MAAM,EAAE;YACrB,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;SACnC;aAAM;YACL,KAAK,EAAE,CAAC;YACR,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAE7B,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE;oBAC7D,KAAK,EAAE,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC;oBAChD,IAAI,EAAE,eAAe,IAAI,MAAM;iBAChC,CAAC,CAAC;aACJ;YAED,8DAA8D;YAC9D,2BAA2B;YAC3B,MAAM,IAAI,GAAG,UAAU,CAAC,IAA4B,CAAC;YACrD,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SAC3D;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,yBAAyB,CAC/B,sBAAgC,EAChC,0BAAuD;QAEvD,MAAM,IAAI,GAAkB,EAAE,CAAC;QAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,0BAA0B,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1D,MAAM,UAAU,GAAG,0BAA0B,CAAC,CAAC,CAAC,CAAC;YACjD,8DAA8D;YAC9D,2BAA2B;YAC3B,MAAM,IAAI,GAAG,UAAU,CAAC,IAA4B,CAAC;YAErD,MAAM,MAAM,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAEzC,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;oBAC1B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;wBAC9D,KAAK,EAAE,UAAU,CAAC,IAAI;qBACvB,CAAC,CAAC;iBACJ;gBAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC;aACjD;iBAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aAC7D;iBAAM;gBACL,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,sBAAsB;qBAC3C,KAAK,CAAC,CAAC,CAAC;qBACR,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;aACnD;SACF;QAED,MAAM,cAAc,GAClB,0BAA0B,CAAC,0BAA0B,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEpE,MAAM,gBAAgB,GACpB,cAAc,KAAK,SAAS,IAAI,cAAc,CAAC,UAAU,CAAC;QAE5D,IACE,CAAC,gBAAgB;YACjB,sBAAsB,CAAC,MAAM,GAAG,0BAA0B,CAAC,MAAM,EACjE;YACA,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,SAAS,CAAC,2BAA2B,EAAE;gBACnE,QAAQ,EAAE,sBAAsB,CAAC,0BAA0B,CAAC,MAAM,CAAC;aACpE,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC;IACd,CAAC;;AApVsB,4BAAY,GAAG,IAAI,CAAC;AADhC,0CAAe"}