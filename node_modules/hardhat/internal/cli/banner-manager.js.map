{"version": 3, "file": "banner-manager.js", "sourceRoot": "", "sources": ["../../src/internal/cli/banner-manager.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAA6B;AAC7B,gEAAkC;AAClC,kDAA0B;AAC1B,mDAAiD;AACjD,6CAA8C;AAE9C,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,6BAA6B,CAAC,CAAC;AASjD,MAAM,iBAAiB,GACrB,8FAA8F,CAAC;AAEjG,MAAM,sBAAsB,GAAG,oBAAoB,CAAC;AAEpD,MAAa,aAAa;IAGxB,YACU,aAAuC,EACvC,gBAAwB,EACxB,gBAAwB;QAFxB,kBAAa,GAAb,aAAa,CAA0B;QACvC,qBAAgB,GAAhB,gBAAgB,CAAQ;QACxB,qBAAgB,GAAhB,gBAAgB,CAAQ;IAC/B,CAAC;IAEG,MAAM,CAAC,KAAK,CAAC,WAAW;QAC7B,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;YAChC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAClC,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,GACtD,MAAM,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,IAAI,aAAa,CAChC,YAAY,EACZ,eAAe,EACf,eAAe,CAChB,CAAC;SACH;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAgB;QACjD,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;YACpC,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAChE,IACE,oBAAoB;gBACpB,IAAI,CAAC,aAAa,CAAC,yBAAyB,GAAG,IAAI,EACnD;gBACA,GAAG,CACD,4DAA4D,oBAAoB,IAAI,CACrF,CAAC;gBACF,OAAO;aACR;SACF;QAED,IAAI;YACF,MAAM,YAAY,GAAG,MAAM,IAAA,qBAAW,EAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YAEnE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE;gBACvC,GAAG,CAAC,iCAAiC,EAAE,YAAY,CAAC,CAAC;gBACrD,OAAO;aACR;YAED,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEnC,MAAM,UAAU,CAAC;gBACf,YAAY,EAAE,IAAI,CAAC,aAAa;gBAChC,eAAe,EAAE,IAAI,CAAC,gBAAgB;gBACtC,eAAe,EAAE,IAAI,CAAC,gBAAgB;aACvC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,GAAG,CACD,mCACE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAC/D,EAAE,CACH,CAAC;SACH;IACH,CAAC;IAEO,eAAe,CAAC,KAAc;QACpC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACvE,OAAO,KAAK,CAAC;SACd;QAED,OAAO,CACL,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC;YAC9C,SAAS,IAAI,KAAK;YAClB,OAAO,KAAK,CAAC,OAAO,KAAK,SAAS;YAClC,mBAAmB,IAAI,KAAK;YAC5B,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC;YACtC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC;YACvE,2BAA2B,IAAI,KAAK;YACpC,OAAO,KAAK,CAAC,yBAAyB,KAAK,QAAQ;YACnD,2BAA2B,IAAI,KAAK;YACpC,OAAO,KAAK,CAAC,yBAAyB,KAAK,QAAQ,CACpD,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,OAAgB;QACtC,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAEzC,IACE,IAAI,CAAC,aAAa,KAAK,SAAS;YAChC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO;YAC3B,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EACjD;YACA,GAAG,CAAC,8CAA8C,CAAC,CAAC;YACpD,OAAO;SACR;QAED,MAAM,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC;QAE5E,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAChE,IAAI,oBAAoB,GAAG,yBAAyB,GAAG,IAAI,EAAE;YAC3D,GAAG,CACD,qDAAqD,oBAAoB,IAAI,CAC9E,CAAC;YACF,OAAO;SACR;QAED,2DAA2D;QAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAE/C,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACnC,MAAM,UAAU,CAAC;YACf,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,eAAe,EAAE,IAAI,CAAC,gBAAgB;SACvC,CAAC,CAAC;IACL,CAAC;CACF;AApHD,sCAoHC;AAQD,KAAK,UAAU,SAAS;IACtB,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAW,GAAE,CAAC;IACrC,MAAM,mBAAmB,GAAG,mBAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,sBAAsB,CAAC,CAAC;IAExE,IAAI,KAAK,GAAgB;QACvB,YAAY,EAAE,SAAS;QACvB,eAAe,EAAE,CAAC;QAClB,eAAe,EAAE,CAAC;KACnB,CAAC;IACF,IAAI;QACF,MAAM,YAAY,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;QACrE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;KAClC;IAAC,OAAO,KAAK,EAAE;QACd,GAAG,CACD,6BACE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAC/D,EAAE,CACH,CAAC;KACH;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,KAAkB;IAC1C,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAW,GAAE,CAAC;IACrC,MAAM,mBAAmB,GAAG,mBAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,sBAAsB,CAAC,CAAC;IAExE,IAAI;QACF,MAAM,kBAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9C,MAAM,kBAAE,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;KACzE;IAAC,OAAO,KAAK,EAAE;QACd,GAAG,CACD,8BACE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAC/D,EAAE,CACH,CAAC;KACH;AACH,CAAC"}