{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/internal/vendor/await-semaphore/index.ts"], "names": [], "mappings": ";AAAA,oHAAoH;AACpH,kJAAkJ;;;AAElJ,+EAA+E;AAE/E,MAAa,SAAS;IAIpB,YAAY,KAAa;QAFjB,WAAM,GAAsB,EAAE,CAAC;QAGrC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,OAAO,CAAa,CAAC,GAAG,EAAE,EAAE;YACrC,MAAM,IAAI,GAAG,GAAG,EAAE;gBAChB,IAAI,QAAQ,GAAG,KAAK,CAAC;gBACrB,GAAG,CAAC,GAAG,EAAE;oBACP,IAAI,CAAC,QAAQ,EAAE;wBACb,QAAQ,GAAG,IAAI,CAAC;wBAChB,IAAI,CAAC,KAAK,EAAE,CAAC;wBACb,IAAI,CAAC,MAAM,EAAE,CAAC;qBACf;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAC3D,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aAC1C;iBAAM;gBACL,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aACtC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,GAAG,CAAI,CAAmB;QAC/B,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YACrC,OAAO,CAAC,EAAE;iBACP,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;gBACZ,OAAO,EAAE,CAAC;gBACV,OAAO,GAAG,CAAC;YACb,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBACb,OAAO,EAAE,CAAC;gBACV,MAAM,GAAG,CAAC;YACZ,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,MAAM;QACZ,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5C,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACjC,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;aAC7D;YAED,IAAI,EAAE,CAAC;SACR;IACH,CAAC;CACF;AAtDD,8BAsDC;AAED,MAAa,KAAM,SAAQ,SAAS;IAClC;QACE,KAAK,CAAC,CAAC,CAAC,CAAC;IACX,CAAC;CACF;AAJD,sBAIC"}