{"version": 3, "file": "lazy.js", "sourceRoot": "", "sources": ["../../src/internal/util/lazy.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAA4C;AAE5C,2CAA8C;AAC9C,qDAA6C;AAE7C,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;AAEzD;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AAEH,SAAgB,UAAU,CAAmB,aAAsB;IACjE,OAAO,eAAe,CACpB,aAAa,EACb,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC,OAAO,CAAC,CACP,KAAa,EACb,OAAuB,EACvB,YAGc,cAAI,CAAC,OAAO;YAE1B,MAAM,UAAU,GAAG,aAAa,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC;YACzC,OAAO,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAC3C,CAAC;KACF,CAAC,EACF,CAAC,MAAM,EAAE,EAAE;QACT,IAAI,MAAM,YAAY,QAAQ,EAAE;YAC9B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBAC3D,SAAS,EAAE,oDAAoD;aAChE,CAAC,CAAC;SACJ;QAED,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE;YACjD,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBAC3D,SAAS,EAAE,mDAAmD;aAC/D,CAAC,CAAC;SACJ;IACH,CAAC,CACF,CAAC;AACJ,CAAC;AA/BD,gCA+BC;AAED,wDAAwD;AACxD,SAAgB,YAAY,CAAqB,eAAwB;IACvE,OAAO,eAAe,CACpB,eAAe,EACf,CAAC,aAAa,EAAE,EAAE;QAChB,SAAS,WAAW,KAAI,CAAC;QAExB,WAAmB,CAAC,OAAO,CAAC,GAAG,UAC9B,KAAa,EACb,OAAuB,EACvB,YAGc,cAAI,CAAC,OAAO;YAE1B,MAAM,UAAU,GAAG,aAAa,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC;YACzC,OAAO,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAC3C,CAAC,CAAC;QAEF,OAAO,WAAW,CAAC;IACrB,CAAC,EACD,CAAC,MAAM,EAAE,EAAE;QACT,IAAI,CAAC,CAAC,MAAM,YAAY,QAAQ,CAAC,EAAE;YACjC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBAC3D,SAAS,EACP,kEAAkE;aACrE,CAAC,CAAC;SACJ;IACH,CAAC,CACF,CAAC;AACJ,CAAC;AA9BD,oCA8BC;AAED,SAAS,eAAe,CACtB,aAA4B,EAC5B,kBAA4D,EAC5D,SAAgC;IAEhC,IAAI,UAA+B,CAAC;IAEpC,MAAM,WAAW,GAAY,kBAAkB,CAAC,aAAa,CAAQ,CAAC;IAEtE,SAAS,aAAa;QACpB,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,MAAM,MAAM,GAAG,aAAa,EAAE,CAAC;YAC/B,SAAS,CAAC,MAAM,CAAC,CAAC;YAElB,qEAAqE;YACrE,uBAAuB;YACvB,MAAM,UAAU,GAAG,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACtD,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;gBACjC,MAAM,UAAU,GAAG,MAAM,CAAC,wBAAwB,CAAC,MAAM,EAAE,QAAQ,CAAE,CAAC;gBACtE,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;aAC1D;YAED,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YAElE,oEAAoE;YACpE,mDAAmD;YACnD,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE;gBAC1C,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBAC3D,SAAS,EACP,qFAAqF;iBACxF,CAAC,CAAC;aACJ;YAED,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;gBAChC,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;aACvC;YAED,UAAU,GAAG,MAAM,CAAC;SACrB;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,MAAM,OAAO,GAA0B;QACrC,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU;YACzC,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC1D,OAAO,OAAO,CAAC,cAAc,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QACvE,CAAC;QAED,cAAc,CAAC,MAAM,EAAE,QAAQ;YAC7B,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAC9C,OAAO,OAAO,CAAC,cAAc,CAAC,aAAa,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC3D,CAAC;QAED,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ;YAC5B,uEAAuE;YACvE,mBAAmB;YACnB,EAAE;YACF,sEAAsE;YACtE,sEAAsE;YACtE,0CAA0C;YAC1C,EAAE;YACF,mEAAmE;YACnE,6DAA6D;YAC7D,yEAAyE;YACzE,uJAAuJ;YACvJ,EAAE;YACF,uEAAuE;YACvE,EAAE;YACF,yEAAyE;YACzE,mEAAmE;YACnE,sEAAsE;YACtE,mEAAmE;YACnE,0EAA0E;YAC1E,kEAAkE;YAClE,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC;YAChC,IACE,KAAK,KAAK,SAAS;gBACnB,KAAK,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gBAClC,UAAU,KAAK,SAAS,EACxB;gBACA,OAAO,SAAS,CAAC;aAClB;YAED,OAAO,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC1D,CAAC;QAED,wBAAwB,CAAC,MAAM,EAAE,QAAQ;YACvC,MAAM,UAAU,GAAG,OAAO,CAAC,wBAAwB,CACjD,aAAa,EAAE,EACf,QAAQ,CACT,CAAC;YAEF,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;aAC1D;YAED,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,cAAc,CAAC,OAAO;YACpB,OAAO,OAAO,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,GAAG,CAAC,MAAM,EAAE,QAAQ;YAClB,OAAO,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChD,CAAC;QAED,YAAY,CAAC,OAAO;YAClB,OAAO,OAAO,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,CAAC,OAAO;YACb,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;QAC1C,CAAC;QAED,iBAAiB,CAAC,OAAO;YACvB,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACtC,OAAO,OAAO,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ;YACnC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACpD,OAAO,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACjE,CAAC;QAED,cAAc,CAAC,MAAM,EAAE,SAAS;YAC9B,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC/C,OAAO,OAAO,CAAC,cAAc,CAAC,aAAa,EAAE,EAAE,SAAS,CAAC,CAAC;QAC5D,CAAC;KACF,CAAC;IAEF,IAAI,WAAW,YAAY,QAAQ,EAAE;QACnC,2EAA2E;QAC3E,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,EAAE,OAAY,EAAE,QAAc,EAAE,EAAE;YACvD,wDAAwD;YACxD,OAAO,OAAO,CAAC,KAAK,CAAC,aAAa,EAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACvE,CAAC,CAAC;QAEF,OAAO,CAAC,SAAS,GAAG,CAAC,MAAM,EAAE,QAAa,EAAE,UAAgB,EAAE,EAAE;YAC9D,wDAAwD;YACxD,OAAO,OAAO,CAAC,SAAS,CAAC,aAAa,EAAc,EAAE,QAAQ,CAAC,CAAC;QAClE,CAAC,CAAC;KACH;IAED,OAAO,IAAI,KAAK,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AACzC,CAAC"}