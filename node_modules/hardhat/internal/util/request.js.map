{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../../src/internal/util/request.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAE1B,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,sBAAsB,CAAC,CAAC;AAEnC,KAAK,UAAU,WAAW,CAC/B,GAAW,EACX,OAAgB;IAEhB,MAAM,EAAE,OAAO,EAAE,GAAG,wDAAa,QAAQ,GAAC,CAAC;IAE3C,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;IACzC,MAAM,cAAc,GAAG,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IAErE,IAAI,SAAoD,CAAC;IACzD,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAC1B,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACnC,CAAC,EAAE,OAAO,CAAC,CAAC;KACb;IAED,IAAI;QACF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,EAAE;YAClC,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,UAAU,CAAC,MAAM;SAC1B,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;YAC/B;4EACgE;YAChE,MAAM,IAAI,KAAK,CAAC,mCAAmC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;SAC3E;QAED,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEhD,OAAO,YAAY,CAAC;KACrB;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,KAAK,cAAc,EAAE;YAC5B,GAAG,CAAC,cAAc,GAAG,kBAAkB,OAAQ,IAAI,CAAC,CAAC;SACtD;aAAM;YACL,GAAG,CACD,cAAc,GAAG,YACf,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAC/D,EAAE,CACH,CAAC;SACH;KACF;YAAS;QACR,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,YAAY,CAAC,SAAS,CAAC,CAAC;SACzB;KACF;AACH,CAAC;AA9CD,kCA8CC"}