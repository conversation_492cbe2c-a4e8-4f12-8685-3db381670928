{"version": 3, "file": "multi-process-mutex.js", "sourceRoot": "", "sources": ["../../src/internal/util/multi-process-mutex.ts"], "names": [], "mappings": ";;;;;;AAAA,+EAA+E;AAC/E,kDAA0B;AAC1B,sDAAyB;AACzB,0DAA6B;AAC7B,sDAAyB;AAEzB,4HAA4H;AAC5H,2FAA2F;AAC3F,mDAAmD;AACnD,0GAA0G;AAC1G,oFAAoF;AACpF,yGAAyG;AACzG,+GAA+G;AAC/G,uJAAuJ;AACvJ,6DAA6D;AAE7D,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,kCAAkC,CAAC,CAAC;AACtD,MAAM,gCAAgC,GAAG,KAAK,CAAC;AAC/C,MAAM,6BAA6B,GAAG,GAAG,CAAC;AAE1C,MAAa,iBAAiB;IAI5B,YAAY,SAAiB,EAAE,oBAA6B;QAC1D,GAAG,CAAC,6BAA6B,SAAS,GAAG,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,GAAG,mBAAI,CAAC,IAAI,CAAC,iBAAE,CAAC,MAAM,EAAE,EAAE,GAAG,SAAS,MAAM,CAAC,CAAC;QACjE,IAAI,CAAC,kBAAkB;YACrB,oBAAoB,IAAI,gCAAgC,CAAC;IAC7D,CAAC;IAEM,KAAK,CAAC,GAAG,CAAI,CAAmB;QACrC,GAAG,CAAC,2CAA2C,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QAEvE,OAAO,IAAI,EAAE;YACX,IAAI,MAAM,IAAI,CAAC,kBAAkB,EAAE,EAAE;gBACnC,0BAA0B;gBAC1B,OAAO,IAAI,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;aAChD;YAED,qBAAqB;YACrB,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;gBAC7B,gGAAgG;gBAChG,GAAG,CACD,uDAAuD,IAAI,CAAC,cAAc,GAAG,CAC9E,CAAC;gBACF,IAAI,CAAC,gBAAgB,EAAE,CAAC;aACzB;iBAAM;gBACL,OAAO;gBACP,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;aACtB;SACF;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI;YACF,0CAA0C;YAC1C,iBAAE,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC3B,wDAAwD;gBACxD,OAAO,KAAK,CAAC;aACd;YAED,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,KAAK,CAAC,+BAA+B,CAC3C,CAAmB;QAEnB,GAAG,CAAC,2BAA2B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QAEvD,IAAI;YACF,MAAM,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC;YAEtB,oBAAoB;YACpB,GAAG,CAAC,2BAA2B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;YACvD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,GAAG,CAAC,2BAA2B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;YAEvD,OAAO,GAAG,CAAC;SACZ;QAAC,OAAO,KAAU,EAAE;YACnB,wCAAwC;YACxC,+CAA+C;YAC/C,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,kBAAkB;QACxB,IAAI,QAAQ,CAAC;QACb,IAAI;YACF,QAAQ,GAAG,iBAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC7C;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC3B,mGAAmG;gBACnG,OAAO,KAAK,CAAC;aACd;YAED,MAAM,KAAK,CAAC;SACb;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAEhD,OAAO,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC;IACxC,CAAC;IAEO,gBAAgB;QACtB,IAAI;YACF,GAAG,CAAC,gCAAgC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;YAC5D,iBAAE,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACpC;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC3B,mGAAmG;gBACnG,OAAO;aACR;YAED,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,KAAK,CAAC,OAAO;QACnB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAC7B,UAAU,CAAC,OAAO,EAAE,6BAA6B,CAAC,CACnD,CAAC;IACJ,CAAC;CACF;AAhHD,8CAgHC"}