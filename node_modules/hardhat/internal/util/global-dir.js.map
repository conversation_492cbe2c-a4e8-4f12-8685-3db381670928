{"version": 3, "file": "global-dir.js", "sourceRoot": "", "sources": ["../../src/internal/util/global-dir.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,kDAA0B;AAC1B,wDAA0B;AAC1B,4CAAoB;AACpB,gDAAwB;AAExB,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,yBAAyB,CAAC,CAAC;AAE7C,KAAK,UAAU,aAAa,CAAC,WAAW,GAAG,SAAS;IAClD,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,wDAAa,WAAW,GAAC,CAAC;IACxD,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,iBAAiB,CAAC,WAAW,GAAG,SAAS;IAChD,MAAM,QAAQ,GAAqB,OAAO,CAAC,WAAW,CAAC,CAAC;IACxD,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,gBAAgB;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG,iBAAiB,EAAE,CAAC;IACvC,kBAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACzB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,WAAoB;IAC5C,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,aAAa,CAAC,WAAW,CAAC,CAAC;IAClD,MAAM,kBAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACzB,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,WAAW;IAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,aAAa,EAAE,CAAC;IACxC,MAAM,kBAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC1B,OAAO,KAAK,CAAC;AACf,CAAC;AAJD,kCAIC;AAEM,KAAK,UAAU,eAAe;IACnC,MAAM,aAAa,GAAG,MAAM,UAAU,EAAE,CAAC;IACzC,MAAM,MAAM,GAAG,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAC1D,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;AACxB,CAAC;AAJD,0CAIC;AAED;;GAEG;AACH,SAAgB,0BAA0B;IACxC,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,YAAE,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IACrE,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC;AAC3B,CAAC;AAHD,gEAGC;AAED;;;GAGG;AACI,KAAK,UAAU,2BAA2B;IAC/C,MAAM,aAAa,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC;IAClD,MAAM,MAAM,GAAG,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAC1D,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;AACxB,CAAC;AAJD,kEAIC;AAED,KAAK,UAAU,MAAM,CAAC,MAAc;IAClC,GAAG,CAAC,2BAA2B,MAAM,EAAE,CAAC,CAAC;IACzC,IAAI,QAAgB,CAAC;IACrB,IAAI;QACF,MAAM,IAAI,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;QAC7D,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KACpC;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,SAAS,CAAC;KAClB;IAED,GAAG,CAAC,oBAAoB,QAAQ,EAAE,CAAC,CAAC;IACpC,OAAO,QAAQ,CAAC;AAClB,CAAC;AAEM,KAAK,UAAU,gBAAgB,CAAC,QAAgB;IACrD,MAAM,aAAa,GAAG,MAAM,UAAU,EAAE,CAAC;IACzC,MAAM,MAAM,GAAG,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAC1D,MAAM,kBAAE,CAAC,SAAS,CAChB,MAAM,EACN;QACE,SAAS,EAAE;YACT,QAAQ;SACT;KACF,EACD,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CACjC,CAAC;IACF,GAAG,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;AACrC,CAAC;AAbD,4CAaC;AAEM,KAAK,UAAU,eAAe;IACnC,MAAM,KAAK,GAAG,MAAM,WAAW,EAAE,CAAC;IAClC,4EAA4E;IAC5E,MAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IACxD,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;IACnC,OAAO,cAAc,CAAC;AACxB,CAAC;AAND,0CAMC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB;IACnC,MAAM,SAAS,GAAG,gBAAgB,EAAE,CAAC;IACrC,MAAM,oBAAoB,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;IAE5E,MAAM,UAAU,GAAG,kBAAE,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;IAE3D,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,EAAE,OAAO,EAAE,GAAG,kBAAE,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IAC1D,OAAO,OAAO,CAAC;AACjB,CAAC;AAZD,sDAYC;AAED,SAAgB,qBAAqB,CAAC,OAAgB;IACpD,MAAM,SAAS,GAAG,gBAAgB,EAAE,CAAC;IACrC,MAAM,oBAAoB,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;IAE5E,kBAAE,CAAC,aAAa,CAAC,oBAAoB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACrE,CAAC;AALD,sDAKC;AAED;;GAEG;AACH,SAAgB,sBAAsB;IACpC,MAAM,SAAS,GAAG,gBAAgB,EAAE,CAAC;IACrC,MAAM,qBAAqB,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;IAE5E,MAAM,UAAU,GAAG,kBAAE,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;IAE5D,OAAO,UAAU,CAAC;AACpB,CAAC;AAPD,wDAOC;AAED,SAAgB,wBAAwB;IACtC,MAAM,SAAS,GAAG,gBAAgB,EAAE,CAAC;IACrC,MAAM,qBAAqB,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;IAE5E,kBAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;AAChD,CAAC;AALD,4DAKC;AAED,SAAgB,eAAe;IAC7B,OAAO,cAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,WAAW,CAAC,CAAC;AACpD,CAAC;AAFD,0CAEC"}