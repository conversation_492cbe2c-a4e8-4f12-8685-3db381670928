{"version": 3, "file": "scripts-runner.js", "sourceRoot": "", "sources": ["../../src/internal/util/scripts-runner.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,gDAAwB;AAGxB,2DAAmE;AACnE,gEAAkE;AAElE,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,6BAA6B,CAAC,CAAC;AAE1C,KAAK,UAAU,SAAS,CAC7B,UAAkB,EAClB,aAAuB,EAAE,EACzB,gBAA0B,EAAE,EAC5B,eAA2C,EAAE;IAE7C,MAAM,EAAE,IAAI,EAAE,GAAG,wDAAa,eAAe,GAAC,CAAC;IAE/C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,eAAe,GAAG,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE9D,MAAM,QAAQ,GAAG;YACf,GAAG,eAAe;YAClB,GAAG,qBAAqB,CACtB,UAAU,EACV,YAAY,CAAC,iBAAiB,KAAK,MAAM,CAC1C;YACD,GAAG,aAAa;SACjB,CAAC;QAEF,MAAM,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;QAEpD,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE;YAChD,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,QAAQ;YAClB,GAAG,EAAE,OAAO;SACb,CAAC,CAAC;QAEH,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE;YACpC,GAAG,CAAC,UAAU,UAAU,4BAA4B,MAAM,IAAI,MAAM,EAAE,CAAC,CAAC;YAExE,OAAO,CAAC,MAAgB,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACL,CAAC;AAnCD,8BAmCC;AAEM,KAAK,UAAU,oBAAoB,CACxC,gBAAkC,EAClC,UAAkB,EAClB,aAAuB,EAAE,EACzB,gBAA0B,EAAE,EAC5B,eAA2C,EAAE;IAE7C,GAAG,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;IAExD,OAAO,SAAS,CACd,UAAU,EACV,UAAU,EACV;QACE,GAAG,aAAa;QAChB,WAAW;QACX,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC;KAC7C,EACD;QACE,GAAG,IAAA,kCAAkB,EAAC,gBAAgB,CAAC;QACvC,GAAG,YAAY;KAChB,CACF,CAAC;AACJ,CAAC;AAtBD,oDAsBC;AAED;;;;;;;;;;;;;;GAcG;AACH,SAAS,mBAAmB,CAAC,IAAc;IACzC,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,EAAE;QACtC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YAChD,OAAO,WAAW,CAAC;SACpB;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IACF,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AACnC,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAkB,EAClB,eAAwB;IAExB,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QACjD,OAAO,EAAE,CAAC;KACX;IAED,wEAAwE;IACxE,eAAe;IACf,IAAI,IAAA,0CAAyB,GAAE,EAAE;QAC/B,OAAO,CAAC,WAAW,EAAE,iCAAiC,CAAC,CAAC;KACzD;IAED,2DAA2D;IAC3D,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;QAC/B,OAAO;YACL,WAAW;YACX,mBAAmB,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE;SAC9D,CAAC;KACH;IAED,OAAO,EAAE,CAAC;AACZ,CAAC"}