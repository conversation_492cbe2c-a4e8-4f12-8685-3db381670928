{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/internal/solidity/compiler/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAyC;AACzC,uCAAyB;AACzB,sDAAyB;AACzB,0DAA6B;AAC7B,+CAAiC;AAGjC,8CAAiD;AACjD,wDAAgD;AAMhD,MAAa,QAAQ;IACnB,YAAoB,aAAqB;QAArB,kBAAa,GAAb,aAAa,CAAQ;IAAG,CAAC;IAEtC,KAAK,CAAC,OAAO,CAAC,KAAoB;QACvC,MAAM,UAAU,GAAG,mBAAI,CAAC,IAAI,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;QAE9D,IAAI,MAAc,CAAC;QACnB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,iBAAiB,CACxC,OAAO,CAAC,QAAQ,EAChB,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,EAChC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EACrB;gBACE,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG;aAC7B,CACF,CAAC;YAEF,MAAM,GAAG,MAAM,CAAC;SACjB;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;SACzD;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;CACF;AAxBD,4BAwBC;AAED,MAAa,cAAc;IACzB,YAAoB,WAAmB,EAAU,YAAqB;QAAlD,gBAAW,GAAX,WAAW,CAAQ;QAAU,iBAAY,GAAZ,YAAY,CAAS;IAAG,CAAC;IAEnE,KAAK,CAAC,OAAO,CAAC,KAAoB;QACvC,MAAM,IAAI,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEjC,0EAA0E;QAC1E,oEAAoE;QACpE,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;YACnC,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE;gBAC3C,oBAAoB;gBACpB,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aACnC;iBAAM,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE;gBACjD,mBAAmB;gBACnB,MAAM,SAAS,GAAG,mBAAI,CAAC,IAAI,CAAC,iBAAE,CAAC,MAAM,EAAE,EAAE,cAAc,CAAC,CAAC;gBACzD,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACzB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACtB;SACF;QAED,IAAI,MAAc,CAAC;QACnB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,iBAAiB,CACxC,IAAI,CAAC,WAAW,EAChB,IAAI,EACJ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EACrB;gBACE,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG;aAC7B,CACF,CAAC;YAEF,MAAM,GAAG,MAAM,CAAC;SACjB;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;SACrE;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;CACF;AAvCD,wCAuCC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,iBAAiB,CACrC,IAAY,EACZ,IAAuB,EACvB,KAAa,EACb,UAA2B,EAAE;IAE7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,KAAK,GAAG,IAAA,wBAAQ,EAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;YACpE,mFAAmF;YACnF,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,MAAM,CAAC,KAAK,CAAC,CAAC;aACf;iBAAM;gBACL,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;aAC7B;QACH,CAAC,CAAC,CAAC;QAEH,mEAAmE;QACnE,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACxB,MAAM,CAAC,GAAG,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAE1B,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACxB,4BAA4B;gBAC5B,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;gBACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,EAAE;oBACnC,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC,CAAC;iBACjE;gBAED,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC3B,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;wBACzC,MAAM,CAAC,KAAK,CAAC,CAAC;qBACf;oBACD,KAAK,CAAC,GAAG,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;SAChD;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AA7CD,8CA6CC"}