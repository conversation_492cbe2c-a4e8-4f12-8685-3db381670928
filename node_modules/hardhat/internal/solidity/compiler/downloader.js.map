{"version": 3, "file": "downloader.js", "sourceRoot": "", "sources": ["../../../src/internal/solidity/compiler/downloader.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAwB;AACxB,wDAA+B;AAC/B,kDAA0B;AAC1B,4CAAoB;AACpB,iDAAyC;AACzC,+BAAiC;AAEjC,kDAA+C;AAC/C,8CAAyE;AACzE,wDAAgD;AAChD,wEAAmE;AAEnE,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,kCAAkC,CAAC,CAAC;AAEtD,MAAM,uBAAuB,GAAG,mCAAmC,CAAC;AAEpE,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,yCAAqB,CAAA;IACrB,6CAAyB,CAAA;IACzB,0CAAsB,CAAA;IACtB,iCAAa,CAAA;AACf,CAAC,EALW,gBAAgB,GAAhB,wBAAgB,KAAhB,wBAAgB,QAK3B;AAyDD;;;;;;;;;GASG;AACH,MAAa,kBAAkB;IACtB,MAAM,CAAC,mBAAmB;QAC/B,oEAAoE;QACpE,wEAAwE;QACxE,gEAAgE;QAChE,EAAE;QACF,qEAAqE;QACrE,0BAA0B;QAC1B,QAAQ,YAAE,CAAC,QAAQ,EAAE,EAAE;YACrB,KAAK,OAAO;gBACV,OAAO,gBAAgB,CAAC,OAAO,CAAC;YAClC,KAAK,OAAO;gBACV,OAAO,gBAAgB,CAAC,KAAK,CAAC;YAChC,KAAK,QAAQ;gBACX,OAAO,gBAAgB,CAAC,KAAK,CAAC;YAChC;gBACE,OAAO,gBAAgB,CAAC,IAAI,CAAC;SAChC;IACH,CAAC;IAKM,MAAM,CAAC,4BAA4B,CACxC,QAA0B,EAC1B,YAAoB;QAEpB,MAAM,GAAG,GAAG,QAAQ,GAAG,YAAY,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACzC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAC7B,GAAG,EACH,IAAI,kBAAkB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAC/C,CAAC;SACH;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;IAC/C,CAAC;IAKD;;OAEG;IACH,YACmB,SAA2B,EAC3B,aAAqB,EACrB,6BAA6B,kBAAkB,CAAC,8BAA8B,EAC9E,oBAAqC,mBAAQ;QAH7C,cAAS,GAAT,SAAS,CAAkB;QAC3B,kBAAa,GAAb,aAAa,CAAQ;QACrB,+BAA0B,GAA1B,0BAA0B,CAAoD;QAC9E,sBAAiB,GAAjB,iBAAiB,CAA4B;QAT/C,WAAM,GAAG,IAAI,uCAAiB,CAAC,mBAAmB,CAAC,CAAC;IAUlE,CAAC;IAEG,KAAK,CAAC,oBAAoB,CAAC,OAAe;QAC/C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAEpD,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,OAAO,KAAK,CAAC;SACd;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;QAEjE,OAAO,kBAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,OAAe,EACf,iBAAkE,EAClE,eAAgE;QAEhE,6HAA6H;QAC7H,iHAAiH;QACjH,gGAAgG;QAChG,0GAA0G;QAC1G,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAEtE,IAAI,oBAAoB,KAAK,IAAI,EAAE;gBACjC,OAAO;aACR;YAED,MAAM,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAE9C,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAElD,IAAI,KAAK,KAAK,SAAS,IAAI,CAAC,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC,EAAE;gBACrE,IAAI;oBACF,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;iBACpC;gBAAC,OAAO,CAAM,EAAE;oBACf,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,IAAI,CAAC,4BAA4B,EACxC,EAAE,EACF,CAAC,CACF,CAAC;iBACH;gBAED,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;aAC/C;YAED,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;aAClE;YAED,IAAI,YAAoB,CAAC;YACzB,IAAI;gBACF,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;aACpD;YAAC,OAAO,CAAM,EAAE;gBACf,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,IAAI,CAAC,eAAe,EAC3B;oBACE,aAAa,EAAE,KAAK,CAAC,WAAW;iBACjC,EACD,CAAC,CACF,CAAC;aACH;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YACzE,IAAI,CAAC,QAAQ,EAAE;gBACb,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;oBACnD,aAAa,EAAE,KAAK,CAAC,WAAW;iBACjC,CAAC,CAAC;aACJ;YAED,MAAM,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YAE7D,MAAM,eAAe,CAAC,oBAAoB,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAAe;QACtC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAEpD,IAAA,+BAAsB,EACpB,KAAK,KAAK,SAAS,EACnB,mDAAmD,CACpD,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;QAEjE,IAAA,+BAAsB,EACpB,MAAM,kBAAO,CAAC,UAAU,CAAC,YAAY,CAAC,EACtC,mDAAmD,CACpD,CAAC;QAEF,IAAI,MAAM,kBAAO,CAAC,UAAU,CAAC,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC,EAAE;YACrE,OAAO,SAAS,CAAC;SAClB;QAED,OAAO;YACL,OAAO;YACP,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,YAAY;YACZ,QAAQ,EAAE,IAAI,CAAC,SAAS,KAAK,gBAAgB,CAAC,IAAI;SACnD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,OAAe;QAEf,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC7C,IAAI,CAAC,CAAC,MAAM,kBAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE;YACzC,OAAO,SAAS,CAAC;SAClB;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;IACxD,CAAC;IAEO,oBAAoB;QAC1B,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IACpE,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC9C,OAAO,kBAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAEO,iCAAiC,CAAC,KAAoB;QAC5D,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IACnE,CAAC;IAEO,+BAA+B,CAAC,KAAoB;QAC1D,MAAM,YAAY,GAAG,IAAI,CAAC,iCAAiC,CAAC,KAAK,CAAC,CAAC;QAEnE,IACE,IAAI,CAAC,SAAS,KAAK,gBAAgB,CAAC,OAAO;YAC3C,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAC9B;YACA,OAAO,YAAY,CAAC;SACrB;QAED,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAClE,CAAC;IAEO,2BAA2B,CAAC,KAAoB;QACtD,OAAO,GAAG,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACxE,CAAC;IAEO,KAAK,CAAC,2BAA2B;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC7C,IAAI,CAAC,CAAC,MAAM,kBAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE;YACzC,OAAO,IAAI,CAAC;SACb;QAED,MAAM,KAAK,GAAG,MAAM,kBAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC;QAEjD,OAAO,GAAG,GAAG,IAAI,CAAC,0BAA0B,CAAC;IAC/C,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,GAAG,CAAC,0CAA0C,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAChE,MAAM,GAAG,GAAG,GAAG,uBAAuB,IAAI,IAAI,CAAC,SAAS,YAAY,CAAC;QACrE,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAEjD,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;IAClD,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAoB;QAClD,GAAG,CAAC,wBAAwB,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;QACjD,MAAM,GAAG,GAAG,GAAG,uBAAuB,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACzE,MAAM,YAAY,GAAG,IAAI,CAAC,iCAAiC,CAAC,KAAK,CAAC,CAAC;QAEnE,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;QAEhD,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,KAAoB,EACpB,YAAoB;QAEpB,MAAM,EAAE,UAAU,EAAE,GAClB,OAAO,CAAC,kBAAkB,CAAsC,CAAC;QACnE,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,mBAAmB,GAAC,CAAC;QAExD,MAAM,iBAAiB,GAAG,KAAK,CAAC,SAAS,CAAC;QAC1C,MAAM,QAAQ,GAAG,MAAM,kBAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAEtD,MAAM,iBAAiB,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE1D,IAAI,iBAAiB,KAAK,iBAAiB,EAAE;YAC3C,MAAM,kBAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACnC,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,4BAA4B,CACxC,KAAoB,EACpB,YAAoB;QAEpB,IAAI,IAAI,CAAC,SAAS,KAAK,gBAAgB,CAAC,IAAI,EAAE;YAC5C,OAAO;SACR;QAED,IACE,IAAI,CAAC,SAAS,KAAK,gBAAgB,CAAC,KAAK;YACzC,IAAI,CAAC,SAAS,KAAK,gBAAgB,CAAC,KAAK,EACzC;YACA,kBAAO,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SACxC;aAAM,IACL,IAAI,CAAC,SAAS,KAAK,gBAAgB,CAAC,OAAO;YAC3C,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAC7B;YACA,8CAA8C;YAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;YAElC,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAChE,MAAM,kBAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAEpC,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC;YACrC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SAC9B;QAED,GAAG,CAAC,6BAA6B,CAAC,CAAC;QACnC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE3D,IAAI,eAAe,EAAE;YACnB,OAAO;SACR;QAED,MAAM,kBAAO,CAAC,UAAU,CAAC,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC,CAAC;IACpE,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,KAAoB;QACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;QAC7D,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,wBAAQ,CAAC,CAAC;QAEtC,IAAI;YACF,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;SACb;QAAC,MAAM;YACN,OAAO,KAAK,CAAC;SACd;IACH,CAAC;;AAlRc,yCAAsB,GACnC,IAAI,GAAG,EAAE,AAD0B,CACzB;AAkBE,iDAA8B,GAAG,MAAQ,AAAX,CAAY;AAvC7C,gDAAkB"}