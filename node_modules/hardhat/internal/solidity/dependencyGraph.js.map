{"version": 3, "file": "dependencyGraph.js", "sourceRoot": "", "sources": ["../../src/internal/solidity/dependencyGraph.ts"], "names": [], "mappings": ";;;AACA,2CAA8C;AAC9C,qDAA6C;AAI7C,MAAa,eAAe;IACnB,MAAM,CAAC,KAAK,CAAC,uBAAuB,CACzC,QAAkB,EAClB,aAA6B;QAE7B,MAAM,KAAK,GAAG,IAAI,eAAe,EAAE,CAAC;QAEpC,uDAAuD;QACvD,MAAM,OAAO,CAAC,GAAG,CACf,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CACjC,KAAK,CAAC,oBAAoB,CAAC,QAAQ,EAAE,YAAY,CAAC,CACnD,CACF,CAAC;QAEF,OAAO,KAAK,CAAC;IACf,CAAC;IAQD;QANQ,mBAAc,GAAG,IAAI,GAAG,EAAwB,CAAC;QACjD,yBAAoB,GAAG,IAAI,GAAG,EAA6B,CAAC;QAEpE,qCAAqC;QACpB,kBAAa,GAAG,IAAI,GAAG,EAAkB,CAAC;IAEpC,CAAC;IAEjB,gBAAgB;QACrB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IAClD,CAAC;IAEM,GAAG,CAAC,IAAkB;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAClD,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,CAAC;IACxC,CAAC;IAEM,OAAO;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CACxD,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAE,EAAE,KAAK,CAAC,CACzD,CAAC;IACJ,CAAC;IAEM,eAAe,CAAC,IAAkB;QACvC,MAAM,YAAY,GAChB,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;QAE9D,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC;IAC3B,CAAC;IAEM,yBAAyB,CAC9B,IAAkB;QAElB,MAAM,OAAO,GAAG,IAAI,GAAG,EAAgB,CAAC;QAExC,MAAM,sBAAsB,GAAG,IAAI,CAAC,0BAA0B,CAC5D,IAAI,EACJ,OAAO,EACP,EAAE,CACH,CAAC;QAEF,OAAO,CAAC,GAAG,sBAAsB,CAAC,CAAC;IACrC,CAAC;IAEM,sBAAsB;QAC3B,MAAM,eAAe,GAAgC,EAAE,CAAC;QAExD,KAAK,MAAM,CACT,UAAU,EACV,YAAY,EACb,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,EAAE;YACxC,eAAe,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC,UAAU,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;YACvE,KAAK,MAAM,UAAU,IAAI,YAAY,EAAE;gBACrC,eAAe,CAAC,UAAU,CAAC,UAAU,CAAC;oBACpC,eAAe,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;gBACtD,eAAe,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gBACvD,eAAe,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;aACxD;SACF;QAED,MAAM,UAAU,GAAuB,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAElC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;YAC/C,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACrB,SAAS;aACV;YACD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAClB,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAClC,MAAM,KAAK,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;YACzC,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;gBAC7B,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBACxB,SAAS;iBACV;gBACD,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACrB,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACvB,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;oBACjD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;wBAC1B,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;qBACtB;gBACH,CAAC,CAAC,CAAC;aACJ;YAED,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5B;QAED,MAAM,mBAAmB,GAAsB,EAAE,CAAC;QAClD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;YAE9C,KAAK,MAAM,UAAU,IAAI,SAAS,EAAE;gBAClC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC;gBAClD,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC;gBAEhE,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;gBACrD,eAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;aACpE;YACD,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC3C;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAEO,0BAA0B,CAChC,IAAkB,EAClB,OAA0B,EAC1B,IAAoB;QAEpB,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACrB,OAAO,IAAI,GAAG,EAAE,CAAC;SAClB;QACD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElB,MAAM,kBAAkB,GACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC9C,UAAU;YACV,IAAI;SACL,CAAC,CAAC,CAAC;QAEN,MAAM,sBAAsB,GAAG,IAAI,GAAG,CACpC,kBAAkB,CACnB,CAAC;QAEF,KAAK,MAAM,EAAE,UAAU,EAAE,IAAI,sBAAsB,EAAE;YACnD,IAAI,CAAC,0BAA0B,CAC7B,UAAU,EACV,OAAO,EACP,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CACxB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACjD;QAED,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,QAAkB,EAClB,IAAkB;QAElB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE7D,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,IAAI,UAAU,KAAK,IAAI,CAAC,UAAU,EAAE;gBAClC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,QAAQ,CAAC,sBAAsB,EAAE;oBAC7D,WAAW,EAAE,IAAI,UAAU,UAAU,IAAI,CAAC,UAAU,GAAG;oBACvD,IAAI,EAAE,IAAI,CAAC,YAAY;iBACxB,CAAC,CAAC;aACJ;YAED,OAAO;SACR;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3D,MAAM,YAAY,GAAG,IAAI,GAAG,EAAgB,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAE7D,uDAAuD;QACvD,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACrC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC3D,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAE7B,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACxD,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF;AA5LD,0CA4LC"}