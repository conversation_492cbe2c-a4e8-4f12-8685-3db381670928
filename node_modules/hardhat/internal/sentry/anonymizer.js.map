{"version": 3, "file": "anonymizer.js", "sourceRoot": "", "sources": ["../../src/internal/sentry/anonymizer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,sDAA6B;AAC7B,iCAA+B;AAC/B,2CAA6B;AAO7B,MAAM,eAAe,GAAG,aAAa,CAAC;AACtC,MAAM,mBAAmB,GAAG,YAAY,CAAC;AACzC,MAAM,gCAAgC,GAAG,CAAC,CAAC;AAC3C,MAAM,oCAAoC,GAAG,CAAC,CAAC;AAE/C,MAAa,UAAU;IACrB,YAAoB,WAAoB;QAApB,gBAAW,GAAX,WAAW,CAAS;IAAG,CAAC;IAE5C;;;;OAIG;IACI,SAAS,CAAC,KAAU;QACzB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;YACzC,OAAO,cAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAClD;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,cAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SAC9C;QAED,MAAM,MAAM,GAAU;YACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC;QAEF,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,EAAE;YACzE,MAAM,sBAAsB,GAAG,IAAI,CAAC,oBAAoB,CACtD,KAAK,CAAC,SAAS,CAAC,MAAM,CACvB,CAAC;YACF,MAAM,CAAC,SAAS,GAAG;gBACjB,MAAM,EAAE,sBAAsB;aAC/B,CAAC;SACH;QAED,OAAO,cAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,QAAgB;QAIvC,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,EAAE;YACjC,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAE/D,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,+DAA+D;gBAC/D,OAAO;oBACL,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAC3C,gBAAgB,EAAE,IAAI;iBACvB,CAAC;aACH;YAED,OAAO;gBACL,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAC/B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAC7B,QAAQ,CACT;gBACD,gBAAgB,EAAE,IAAI;aACvB,CAAC;SACH;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAEvD,IAAI,gBAAgB,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBACnC,yCAAyC;gBACzC,OAAO;oBACL,kBAAkB,EAAE,QAAQ;oBAC5B,gBAAgB,EAAE,KAAK;iBACxB,CAAC;aACH;YAED,oFAAoF;YACpF,OAAO;gBACL,kBAAkB,EAAE,eAAe;gBACnC,gBAAgB,EAAE,IAAI;aACvB,CAAC;SACH;QAED,OAAO;YACL,kBAAkB,EAAE,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAChE,gBAAgB,EAAE,KAAK;SACxB,CAAC;IACJ,CAAC;IAEM,qBAAqB,CAAC,YAAoB;QAC/C,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAErD,kEAAkE;QAClE,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,CAAC,CAAC;QAE3D,6CAA6C;QAC7C,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAExD,uCAAuC;QACvC,MAAM,QAAQ,GAAG,wBAAwB,CAAC;QAE1C,OAAO,YAAY;aAChB,OAAO,CAAC,SAAS,EAAE,eAAe,CAAC;aACnC,OAAO,CAAC,SAAS,EAAE,eAAe,CAAC;aACnC,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEM,eAAe,CAAC,KAAY;QACjC,MAAM,UAAU,GAAG,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;QAE5C,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,kEAAkE;YAClE,4CAA4C;YAC5C,OAAO,IAAI,CAAC;SACb;QAED,MAAM,iBAAiB,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE5D,MAAM,MAAM,GAAG,iBAAiB,EAAE,UAAU,EAAE,MAAM,CAAC;QAErD,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,IAAI,CAAC;SACb;QAED,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,EAAE;YAC5C,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAChC,SAAS;aACV;YAED,wEAAwE;YACxE,UAAU;YACV,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;gBACvC,OAAO,IAAI,CAAC;aACb;YAED,IAAI,KAAK,CAAC,QAAQ,KAAK,eAAe,EAAE;gBACtC,OAAO,KAAK,CAAC;aACd;YAED,IACE,IAAI,CAAC,WAAW,KAAK,SAAS;gBAC9B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EACzC;gBACA,OAAO,KAAK,CAAC;aACd;SACF;QAED,iEAAiE;QACjE,OAAO,KAAK,CAAC;IACf,CAAC;IAES,uBAAuB,CAAC,QAAgB;QAChD,OAAO,iBAAM,CAAC,IAAI,CAAC,cAAc,EAAE;YACjC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;SAC5B,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,QAAgB;QACrC,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAC9D,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;QAC1E,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;QACzE,MAAM,aAAa,GACjB,CAAC,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC;YACjC,QAAQ,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;YAC3C,CAAC,QAAQ,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;QAE5C,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,oBAAoB,CAAC,UAAuB;QAClD,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC;IAC5E,CAAC;IAEO,mBAAmB,CAAC,KAAgB;QAC1C,MAAM,MAAM,GAAc;YACxB,IAAI,EAAE,KAAK,CAAC,IAAI;SACjB,CAAC;QAEF,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE;YAC7B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACxD;QAED,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE;YAClC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;SACjE;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,UAAsB;QACjD,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE;YACnC,MAAM,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACpE,OAAO;gBACL,MAAM,EAAE,kBAAkB;aAC3B,CAAC;SACH;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,gBAAgB,CAAC,MAAoB;QAC3C,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,eAAe,CAAC,KAAiB;QACvC,MAAM,MAAM,GAAe;YACzB,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,QAAQ,EAAE,KAAK,CAAC,QAAQ;SACzB,CAAC;QAEF,IAAI,gBAAgB,GAAG,IAAI,CAAC;QAC5B,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;YAChC,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,CAAC,QAAQ,GAAG,mBAAmB,CAAC,kBAAkB,CAAC;YACzD,gBAAgB,GAAG,mBAAmB,CAAC,gBAAgB,CAAC;SACzD;QAED,IAAI,CAAC,gBAAgB,EAAE;YACrB,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;YACzC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;YACvC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;YACzC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;SAC1B;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,kBAAkB,CAAC,YAAoB;QAC7C,MAAM,OAAO,GAAG,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEhD,gGAAgG;QAChG,IAAI,OAAO,CAAC,MAAM,GAAG,gCAAgC,EAAE;YACrD,OAAO,YAAY,CAAC;SACrB;QAED,MAAM,gBAAgB,GAAI,EAAe,CAAC,MAAM,CAC9C,GAAG;YACD,OAAO,CAAC,6CAA6C,CAAC;YACtD,OAAO,CAAC,+CAA+C,CAAC;YACxD,OAAO,CAAC,8CAA8C,CAAC;YACvD,OAAO,CAAC,+CAA+C,CAAC;YACxD,OAAO,CAAC,gDAAgD,CAAC;YACzD,OAAO,CAAC,8CAA8C,CAAC;YACvD,OAAO,CAAC,0DAA0D,CAAC;YACnE,OAAO,CAAC,+CAA+C,CAAC;YACxD,OAAO,CAAC,2DAA2D,CAAC;SACrE,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,CACnD,CAAC;QACF,IAAI,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAEhE,mDAAmD;QACnD,yEAAyE;QACzE,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,OAAO,SAAS,GAAG,OAAO,CAAC,MAAM,EAAE;YACjC,MAAM,aAAa,GAAG,wBAAwB,CAC5C,OAAO,EACP,YAAY,EACZ,SAAS,EACT,gBAAgB,CACjB,CAAC;YAEF,IAAI,aAAa,CAAC,MAAM,IAAI,oCAAoC,EAAE;gBAChE,MAAM,kBAAkB,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACnE,MAAM,aAAa,GACjB,SAAS,GAAG,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;oBAC/C,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK;oBACjD,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC1B,MAAM,kBAAkB,GAAG,YAAY,CAAC,KAAK,CAC3C,kBAAkB,CAAC,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAM,EACzD,aAAa,CACd,CAAC;gBACF,iBAAiB,IAAI,GAAG,mBAAmB,GAAG,kBAAkB,EAAE,CAAC;gBACnE,SAAS,IAAI,aAAa,CAAC,MAAM,CAAC;aACnC;iBAAM;gBACL,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;gBACpC,MAAM,aAAa,GACjB,SAAS,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM;oBAC5B,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK;oBAC9B,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC1B,MAAM,kBAAkB,GAAG,YAAY,CAAC,KAAK,CAC3C,QAAQ,CAAC,KAAK,EACd,aAAa,CACd,CAAC;gBACF,iBAAiB,IAAI,kBAAkB,CAAC;gBACxC,SAAS,EAAE,CAAC;aACb;SACF;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;CACF;AAlSD,gCAkSC;AAED,SAAS,wBAAwB,CAC/B,OAAoB,EACpB,eAAuB,EACvB,UAAkB,EAClB,gBAA0B;IAE1B,MAAM,aAAa,GAAgB,EAAE,CAAC;IACtC,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAChD,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YAC9C,MAAM;SACP;QAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,sDAAsD;YACtD,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC1D,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;YAC1D,MAAM,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAC7C,SAAS,EACT,SAAS,CAAC,KAAK,CAChB,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;gBACnC,MAAM;aACP;SACF;QAED,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC/B;IACD,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAS,iBAAiB,CAAC,YAAoB;IAC7C,MAAM,OAAO,GAAgB,EAAE,CAAC;IAChC,MAAM,EAAE,GAAG,eAAe,CAAC;IAC3B,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClC,OAAO,KAAK,KAAK,IAAI,EAAE;QACrB,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;YACd,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC,CAAC;QACH,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KAC/B;IACD,OAAO,OAAO,CAAC;AACjB,CAAC"}