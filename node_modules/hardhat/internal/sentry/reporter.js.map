{"version": 3, "file": "reporter.js", "sourceRoot": "", "sources": ["../../src/internal/sentry/reporter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAIwB;AACxB,2DAAoD;AACpD,qDAAyD;AACzD,uDAA2D;AAC3D,mDAA2D;AAC3D,qDAAwD;AAExD,2CAAqD;AAExC,QAAA,UAAU,GACrB,2EAA2E,CAAC;AAE9E;;GAEG;AACH,MAAa,QAAQ;IACZ,MAAM,CAAC,WAAW,CAAC,KAAY;QACpC,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QAEzC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;YACrB,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YACjC,OAAO;SACR;QAED,QAAQ,CAAC,IAAI,EAAE,CAAC;QAEhB,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;QACvC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;QACnD,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QAEhD,MAAM,cAAc,GAAG,IAAA,+BAAiB,GAAE,CAAC;QAC3C,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;QAElD,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,UAAU,CAAC,OAAgB;QACvC,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QACzC,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,UAAU,CAAC,OAAgB;QACvC,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QACzC,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,aAAa,CAAC,UAAkB;QAC5C,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QACzC,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;IACnC,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,OAAe;QACvC,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QACzC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC9C,OAAO,IAAI,CAAC;SACb;QAED,MAAM,MAAM,GAAG,wDAAa,cAAc,GAAC,CAAC;QAC5C,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,KAAY;QACrC,IACE,qBAAY,CAAC,cAAc,CAAC,KAAK,CAAC;YAClC,CAAC,KAAK,CAAC,eAAe,CAAC,gBAAgB,EACvC;YACA,OAAO,KAAK,CAAC;SACd;QAED,IAAI,2BAAkB,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;YAClD,IAAI,oCAA2B,CAAC,6BAA6B,CAAC,KAAK,CAAC,EAAE;gBACpE,OAAO,KAAK,CAAC,gBAAgB,CAAC;aAC/B;YAED,4CAA4C;YAC5C,OAAO,KAAK,CAAC;SACd;QAED,yCAAyC;QACzC,IAAI,KAAK,YAAY,sBAAa,EAAE;YAClC,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE;YACpC,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAIO,MAAM,CAAC,YAAY;QACzB,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;YAChC,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,EAAE,CAAC;SACjC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEO,MAAM,CAAC,oBAAoB;QACjC,MAAM,gBAAgB,GAAG,IAAA,kCAAqB,GAAE,CAAC;QAEjD,OAAO,gBAAgB,KAAK,IAAI,CAAC;IACnC,CAAC;IAOD;QAJO,gBAAW,GAAG,KAAK,CAAC;QACpB,YAAO,GAAG,KAAK,CAAC;QAIrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,IAAA,kCAAmB,GAAE,EAAE;YACzB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACtB;QAED,yFAAyF;QACzF,IAAI,IAAA,2BAAU,GAAE,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,SAAS,EAAE;YACnE,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACtB;IACH,CAAC;IAEM,IAAI;QACT,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO;SACR;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;QAEvC,MAAM,uBAAuB,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC;YACnE,GAAG,EAAE,QAAQ;SACd,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC;YACV,GAAG,EAAE,kBAAU;YACf,SAAS,EAAE,IAAA,kCAAsB,GAAE;YACnC,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,uBAAuB,CAAC;SAC9C,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;CACF;AAzJD,4BAyJC"}