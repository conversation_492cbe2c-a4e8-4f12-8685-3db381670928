{"version": 3, "file": "handler.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/jsonrpc/handler.ts"], "names": [], "mappings": ";;;;;;AACA,wDAAkC;AAElC,iEAA4D;AAG5D,wDAKqC;AACrC,gDAO4B;AAE5B,+EAA+E;AAE/E,MAAa,cAAc;IACzB,YAA6B,SAA0B;QAA1B,cAAS,GAAT,SAAS,CAAiB;QAEhD,eAAU,GAAG,KAAK,EAAE,GAAoB,EAAE,GAAmB,EAAE,EAAE;YACtE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC7B,OAAO;aACR;YAED,IAAI,eAAoB,CAAC;YACzB,IAAI;gBACF,eAAe,GAAG,MAAM,oBAAoB,CAAC,GAAG,CAAC,CAAC;aACnD;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC7C,OAAO;aACR;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;gBAClC,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CACjC,eAAe,CAAC,GAAG,CAAC,CAAC,SAAc,EAAE,EAAE,CACrC,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CACrC,CACF,CAAC;gBAEF,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBACnC,OAAO;aACR;YAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAEjE,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACnC,CAAC,CAAC;QAEK,aAAQ,GAAG,KAAK,EAAE,EAAa,EAAE,EAAE;YACxC,MAAM,aAAa,GAAa,EAAE,CAAC;YACnC,IAAI,QAAQ,GAAG,KAAK,CAAC;YAErB,MAAM,QAAQ,GAAG,CAAC,OAA8C,EAAE,EAAE;gBAClE,oFAAoF;gBACpF,uFAAuF;gBACvF,IAAI,QAAQ,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;oBAC7D,OAAO;iBACR;gBAED,IAAI;oBACF,EAAE,CAAC,IAAI,CACL,IAAI,CAAC,SAAS,CAAC;wBACb,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,kBAAkB;wBAC1B,MAAM,EAAE,OAAO;qBAChB,CAAC,CACH,CAAC;iBACH;gBAAC,OAAO,KAAK,EAAE;oBACd,YAAY,CAAC,KAAK,CAAC,CAAC;iBACrB;YACH,CAAC,CAAC;YAEF,sCAAsC;YACtC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAErD,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;gBAC7B,IAAI,MAAyC,CAAC;gBAC9C,IAAI,OAA4C,CAAC;gBAEjD,IAAI;oBACF,MAAM,GAAG,cAAc,CAAC,GAAa,CAAC,CAAC;oBAEvC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;wBAC7B,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CACjB,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,aAAa,CAAC,CAChD,CACF;wBACH,CAAC,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;iBAC9D;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;iBAC/B;gBAED,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAClB,iCAAiC;gBACjC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;gBAExD,sEAAsE;gBACtE,QAAQ,GAAG,IAAI,CAAC;gBAChB,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,EAAE;oBAC7C,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;wBAC3B,MAAM,EAAE,iBAAiB;wBACzB,MAAM,EAAE,CAAC,cAAc,CAAC;qBACzB,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAsEM,mBAAc,GAAG,KAAK,EAC5B,GAAmB,EACO,EAAE;YAC5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC1C,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,MAAM;aACP,CAAC;QACJ,CAAC,CAAC;IAjLwD,CAAC;IAgGnD,kBAAkB,CAAC,GAAmB;QAC5C,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACnB,GAAG,CAAC,GAAG,EAAE,CAAC;IACZ,CAAC;IAEO,eAAe,CAAC,GAAmB;QACzC,GAAG,CAAC,SAAS,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;QAClD,GAAG,CAAC,SAAS,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC;QACpD,GAAG,CAAC,SAAS,CAAC,8BAA8B,EAAE,cAAc,CAAC,CAAC;QAC9D,GAAG,CAAC,SAAS,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;IAEO,aAAa,CACnB,GAAmB,EACnB,OAA4C;QAE5C,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;QACrB,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;QAClD,IAAI,2CAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,GAAmB;QAEnB,IAAI,CAAC,IAAA,4BAAkB,EAAC,GAAG,CAAC,EAAE;YAC5B,OAAO,YAAY,CAAC,IAAI,4BAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAC;SACjE;QAED,MAAM,MAAM,GAAmB,GAAG,CAAC;QACnC,IAAI,OAAoC,CAAC;QAEzC,IAAI;YACF,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SAC7C;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;SAC/B;QAED,6BAA6B;QAC7B,IAAI,CAAC,IAAA,6BAAmB,EAAC,OAAO,CAAC,EAAE;YACjC,oFAAoF;YACpF,OAAO,GAAG,YAAY,CAAC,IAAI,sBAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;SAC7D;QAED,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;SACzD;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,MAAsB,EACtB,aAAuB;QAEvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAExD,sEAAsE;QACtE,wCAAwC;QACxC,IACE,MAAM,CAAC,MAAM,KAAK,eAAe;YACjC,IAAA,kCAAwB,EAAC,OAAO,CAAC,EACjC;YACA,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACpC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CAgBF;AAnLD,wCAmLC;AAED,MAAM,oBAAoB,GAAG,KAAK,EAAE,GAAoB,EAAgB,EAAE;IACxE,IAAI,IAAI,CAAC;IAET,IAAI;QACF,MAAM,GAAG,GAAG,MAAM,IAAA,kBAAU,EAAC,GAAG,CAAC,CAAC;QAClC,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QAE5B,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACzB;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,KAAK,EAAE;YAC1B,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAClE;QAED,sFAAsF;QACtF,MAAM,KAAK,CAAC;KACb;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,GAAW,EAAqC,EAAE;IACxE,IAAI,IAAS,CAAC;IACd,IAAI;QACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KACxB;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,KAAK,EAAE;YAC1B,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAClE;QAED,sFAAsF;QACtF,MAAM,KAAK,CAAC;KACb;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,KAAU,EAAmB,EAAE;IACnD,gEAAgE;IAChE,IAAI,MAA0B,CAAC;IAC/B,IAAI,UAA8B,CAAC;IAEnC,IAAI,KAAK,CAAC,eAAe,KAAK,SAAS,EAAE;QACvC,MAAM,GAAG,KAAK,CAAC,eAAe,CAAC;KAChC;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;QAC5B,IAAI,KAAK,CAAC,IAAI,EAAE,IAAI,KAAK,SAAS,EAAE;YAClC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;SAC9B;aAAM;YACL,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC;SACzB;QAED,IAAI,MAAM,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,EAAE,eAAe,KAAK,SAAS,EAAE;YACrE,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC;SACrC;KACF;IAED,+FAA+F;IAC/F,IAAI,CAAC,sBAAa,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QACzC,KAAK,GAAG,IAAI,sBAAa,CAAC,KAAK,CAAC,CAAC;KAClC;IAED,MAAM,QAAQ,GAA0B;QACtC,OAAO,EAAE,KAAK;QACd,EAAE,EAAE,IAAI;QACR,KAAK,EAAE;YACL,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;SACvB;KACF,CAAC;IAEF,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG;QACpB,OAAO,EAAE,KAAK,CAAC,OAAO;KACvB,CAAC;IAEF,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACrC;IAED,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;KACvC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC"}