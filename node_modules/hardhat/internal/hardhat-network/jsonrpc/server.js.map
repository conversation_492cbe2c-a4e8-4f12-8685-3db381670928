{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../../../src/internal/hardhat-network/jsonrpc/server.ts"], "names": [], "mappings": ";;;;;;AAGA,kDAA0B;AAC1B,gDAAoC;AAOpC,oDAAyD;AAEzD,uCAA2C;AAE3C,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,sCAAsC,CAAC,CAAC;AAS1D,MAAa,aAAa;IAKxB,YAAY,MAA2B;QAgBhC,gBAAW,GAAG,CAAC,IAAI,GAAG,UAAU,EAAmB,EAAE;YAC1D,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAA+B,CAAC;YACnE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAiB,CAAC;YAEpE,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,OAAO,IAAI,IAAI,GAAG,EAAE;gBAC1D,gBAAgB,EAAE,EAAE;gBACpB,mBAAmB,EAAE,EAAE;aACxB,CAAC,CAAC;YAEH,OAAO,IAAI,mBAAY,CACrB,UAAU,OAAO,IAAI,IAAI,GAAG,EAC5B,IAAI,EACJ,EAAE,EACF,KAAK,EACL,UAAU,CACX,CAAC;QACJ,CAAC,CAAC;QAEK,WAAM,GAAG,GAA+C,EAAE;YAC/D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC7B,GAAG,CAAC,oCAAoC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC7D,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE;oBACrE,2GAA2G;oBAC3G,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAiB,CAAC,CAAC,iCAAiC;oBAC5F,OAAO,CAAC,OAAO,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEK,oBAAe,GAAG,KAAK,IAAI,EAAE;YAClC,MAAM,gBAAgB,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC/C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC7C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC;QAEK,UAAK,GAAG,KAAK,IAAI,EAAE;YACxB,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACpC,GAAG,CAAC,yBAAyB,CAAC,CAAC;oBAC/B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;wBAC7B,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;4BACrC,GAAG,CAAC,iCAAiC,CAAC,CAAC;4BACvC,MAAM,CAAC,GAAG,CAAC,CAAC;4BACZ,OAAO;yBACR;wBAED,GAAG,CAAC,wBAAwB,CAAC,CAAC;wBAC9B,OAAO,EAAE,CAAC;oBACZ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC;gBACF,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACpC,GAAG,CAAC,0BAA0B,CAAC,CAAC;oBAChC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;wBAC3B,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;4BACrC,GAAG,CAAC,kCAAkC,CAAC,CAAC;4BACxC,MAAM,CAAC,GAAG,CAAC,CAAC;4BACZ,OAAO;yBACR;wBAED,GAAG,CAAC,yBAAyB,CAAC,CAAC;wBAC/B,OAAO,EAAE,CAAC;oBACZ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC;QArFA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,IAAI,CAAe,CAAC;QAEzD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,MAAM,OAAO,GAAG,IAAI,wBAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEpD,IAAI,CAAC,WAAW,GAAG,cAAI,CAAC,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,CAAC;YAC5B,MAAM,EAAE,IAAI,CAAC,WAAW;SACzB,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QACnD,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IACpD,CAAC;CAyEF;AA5FD,sCA4FC"}