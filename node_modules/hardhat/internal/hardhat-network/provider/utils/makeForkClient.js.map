{"version": 3, "file": "makeForkClient.js", "sourceRoot": "", "sources": ["../../../../src/internal/hardhat-network/provider/utils/makeForkClient.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAAoC;AAEpC,kDAA0D;AAC1D,iDAA8D;AAC9D,uEAGgD;AAChD,uDAA4D;AAC5D,iDAAqD;AAIrD,2DAG6B;AAE7B,uCAAuC;AACvC,6DAA6D;AAC7D,8DAA8D;AAC9D,6EAA6E;AAC7E,gDAAgD;AAChD,MAAM,iBAAiB,GAAG,KAAK,CAAC;AAEzB,KAAK,UAAU,gBAAgB,CAAC,UAAsB;IAO3D,MAAM,YAAY,GAAG,IAAI,mBAAY,CACnC,UAAU,CAAC,UAAU,EACrB,gCAAoB,EACpB,UAAU,CAAC,WAAW,EACtB,iBAAiB,CAClB,CAAC;IAEF,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,YAAY,CAAC,CAAC;IACnD,MAAM,cAAc,GAAG,IAAA,2CAAuB,EAAC,SAAS,CAAC,CAAC;IAC1D,MAAM,QAAQ,GAAG,cAAc,IAAI,sCAAkB,CAAC;IAEtD,MAAM,iBAAiB,GAAG,MAAM,oBAAoB,CAAC,YAAY,CAAC,CAAC;IACnE,MAAM,mBAAmB,GAAG,sBAAsB,CAChD,iBAAiB,EACjB,QAAQ,CACT,CAAC;IAEF,IAAI,eAAe,CAAC;IACpB,IAAI,UAAU,CAAC,WAAW,KAAK,SAAS,EAAE;QACxC,IAAI,UAAU,CAAC,WAAW,GAAG,iBAAiB,EAAE;YAC9C,sFAAsF;YACtF,MAAM,IAAI,KAAK,CACb,8CAA8C,UAAU,CAAC,WAAW,6BAA6B,iBAAiB,EAAE,CACrH,CAAC;SACH;QAED,IAAI,UAAU,CAAC,WAAW,GAAG,mBAAmB,EAAE;YAChD,MAAM,aAAa,GACjB,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YAC1D,MAAM,qBAAqB,GAAG,QAAQ,GAAG,EAAE,CAAC;YAC5C,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CACf,8BACE,UAAU,CAAC,WACb,yBAAyB,qBAAqB;0BAC9B,mBAAmB,iCACjC,qBAAqB,GAAG,aAC1B,sBAAsB,CACvB,CACF,CAAC;SACH;QAED,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;KAClD;SAAM;QACL,eAAe,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;KAC/C;IAED,OAAO;QACL,YAAY;QACZ,SAAS;QACT,eAAe;QACf,iBAAiB;QACjB,QAAQ;KACT,CAAC;AACJ,CAAC;AA7DD,4CA6DC;AAEM,KAAK,UAAU,cAAc,CAClC,UAAsB,EACtB,aAAsB;IAQtB,MAAM,EACJ,YAAY,EACZ,SAAS,EACT,eAAe,EACf,iBAAiB,EACjB,QAAQ,GACT,GAAG,MAAM,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAEvC,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;IAEpE,MAAM,kBAAkB,GAAG,IAAA,gCAAmB,EAAC,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;IAEvE,MAAM,kBAAkB,GACtB,UAAU,CAAC,WAAW,KAAK,SAAS,IAAI,aAAa,KAAK,SAAS,CAAC;IAEtE,MAAM,UAAU,GAAG,IAAI,sBAAa,CAClC,YAAY,EACZ,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACR,kBAAkB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAC/C,CAAC;IAEF,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC;IAEjC,IAAA,+BAAsB,EACpB,aAAa,KAAK,IAAI,EACtB,iCAAiC,CAClC,CAAC;IAEF,MAAM,kBAAkB,GAAG,KAAK,CAAC,SAAS,CAAC;IAE3C,OAAO;QACL,UAAU;QACV,eAAe;QACf,kBAAkB;QAClB,aAAa;QACb,kBAAkB;KACnB,CAAC;AACJ,CAAC;AAjDD,wCAiDC;AAED,KAAK,UAAU,gBAAgB,CAC7B,QAAsB,EACtB,WAAmB;IAEnB,MAAM,cAAc,GAAG,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC;QAC7C,MAAM,EAAE,sBAAsB;QAC9B,MAAM,EAAE,CAAC,IAAA,gCAAmB,EAAC,WAAW,CAAC,EAAE,KAAK,CAAC;KAClD,CAAC,CAAmB,CAAC;IAEtB,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,KAAK,UAAU,YAAY,CAAC,QAAsB;IAChD,MAAM,eAAe,GAAG,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC;QAC9C,MAAM,EAAE,aAAa;KACtB,CAAC,CAAW,CAAC;IACd,OAAO,QAAQ,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;AACvC,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,QAAsB;IACxD,MAAM,iBAAiB,GAAG,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC;QAChD,MAAM,EAAE,iBAAiB;KAC1B,CAAC,CAAW,CAAC;IAEd,MAAM,WAAW,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAC9C,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAgB,sBAAsB,CACpC,iBAAyB,EACzB,QAAgB;IAEhB,iHAAiH;IACjH,mJAAmJ;IACnJ,OAAO,iBAAiB,GAAG,QAAQ,IAAI,CAAC;QACtC,CAAC,CAAC,iBAAiB,GAAG,QAAQ;QAC9B,CAAC,CAAC,iBAAiB,CAAC;AACxB,CAAC;AATD,wDASC"}