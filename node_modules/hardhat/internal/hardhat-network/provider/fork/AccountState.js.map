{"version": 3, "file": "AccountState.js", "sourceRoot": "", "sources": ["../../../../src/internal/hardhat-network/provider/fork/AccountState.ts"], "names": [], "mappings": ";;;AAAA,yCAA2E;AAW9D,QAAA,gBAAgB,GAAG,IAAA,kBAAe,EAAe;IAC5D,KAAK,EAAE,SAAS;IAChB,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,IAAA,eAAY,GAAyB;IAC9C,IAAI,EAAE,SAAS;IACf,cAAc,EAAE,KAAK;CACtB,CAAC,CAAC;AAEH,4BAA4B;AAC5B,wEAAwE;AAC3D,QAAA,qBAAqB,GAAG,IAAA,kBAAe,EAAe;IACjE,KAAK,EAAE,KAAK;IACZ,OAAO,EAAE,KAAK;IACd,OAAO,EAAE,IAAA,eAAY,GAAyB;IAC9C,IAAI,EAAE,IAAI;IACV,cAAc,EAAE,IAAI;CACrB,CAAC,CAAC"}