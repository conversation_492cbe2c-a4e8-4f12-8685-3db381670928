"use strict";
// ------------------------------------
// This code was autogenerated using
// scripts/console-library-generator.ts
// ------------------------------------
Object.defineProperty(exports, "__esModule", { value: true });
exports.CONSOLE_LOG_SIGNATURES = exports.Bytes32Ty = exports.Bytes31Ty = exports.Bytes30Ty = exports.Bytes29Ty = exports.Bytes28Ty = exports.Bytes27Ty = exports.Bytes26Ty = exports.Bytes25Ty = exports.Bytes24Ty = exports.Bytes23Ty = exports.Bytes22Ty = exports.Bytes21Ty = exports.Bytes20Ty = exports.Bytes19Ty = exports.Bytes18Ty = exports.Bytes17Ty = exports.Bytes16Ty = exports.Bytes15Ty = exports.Bytes14Ty = exports.Bytes13Ty = exports.Bytes12Ty = exports.Bytes11Ty = exports.Bytes10Ty = exports.Bytes9Ty = exports.Bytes8Ty = exports.Bytes7Ty = exports.Bytes6Ty = exports.Bytes5Ty = exports.Bytes4Ty = exports.Bytes3Ty = exports.Bytes2Ty = exports.Bytes1Ty = exports.BytesTy = exports.AddressTy = exports.BoolTy = exports.StringTy = exports.Uint256Ty = exports.Int256Ty = void 0;
exports.Int256Ty = "Int256";
exports.Uint256Ty = "Uint256";
exports.StringTy = "String";
exports.BoolTy = "Bool";
exports.AddressTy = "Address";
exports.BytesTy = "Bytes";
exports.Bytes1Ty = "Bytes1";
exports.Bytes2Ty = "Bytes2";
exports.Bytes3Ty = "Bytes3";
exports.Bytes4Ty = "Bytes4";
exports.Bytes5Ty = "Bytes5";
exports.Bytes6Ty = "Bytes6";
exports.Bytes7Ty = "Bytes7";
exports.Bytes8Ty = "Bytes8";
exports.Bytes9Ty = "Bytes9";
exports.Bytes10Ty = "Bytes10";
exports.Bytes11Ty = "Bytes11";
exports.Bytes12Ty = "Bytes12";
exports.Bytes13Ty = "Bytes13";
exports.Bytes14Ty = "Bytes14";
exports.Bytes15Ty = "Bytes15";
exports.Bytes16Ty = "Bytes16";
exports.Bytes17Ty = "Bytes17";
exports.Bytes18Ty = "Bytes18";
exports.Bytes19Ty = "Bytes19";
exports.Bytes20Ty = "Bytes20";
exports.Bytes21Ty = "Bytes21";
exports.Bytes22Ty = "Bytes22";
exports.Bytes23Ty = "Bytes23";
exports.Bytes24Ty = "Bytes24";
exports.Bytes25Ty = "Bytes25";
exports.Bytes26Ty = "Bytes26";
exports.Bytes27Ty = "Bytes27";
exports.Bytes28Ty = "Bytes28";
exports.Bytes29Ty = "Bytes29";
exports.Bytes30Ty = "Bytes30";
exports.Bytes31Ty = "Bytes31";
exports.Bytes32Ty = "Bytes32";
/** Maps from a 4-byte function selector to a signature (argument types) */
exports.CONSOLE_LOG_SIGNATURES = {
    0x51973ec9: [],
    0x2d5b6cb9: [exports.Int256Ty],
    0xf82c50f1: [exports.Uint256Ty],
    0x41304fac: [exports.StringTy],
    0x32458eed: [exports.BoolTy],
    0x2c2ecbc2: [exports.AddressTy],
    0x0be77f56: [exports.BytesTy],
    0x6e18a128: [exports.Bytes1Ty],
    0xe9b62296: [exports.Bytes2Ty],
    0x2d834926: [exports.Bytes3Ty],
    0xe05f48d1: [exports.Bytes4Ty],
    0xa684808d: [exports.Bytes5Ty],
    0xae84a591: [exports.Bytes6Ty],
    0x4ed57e28: [exports.Bytes7Ty],
    0x4f84252e: [exports.Bytes8Ty],
    0x90bd8cd0: [exports.Bytes9Ty],
    0x013d178b: [exports.Bytes10Ty],
    0x04004a2e: [exports.Bytes11Ty],
    0x86a06abd: [exports.Bytes12Ty],
    0x94529e34: [exports.Bytes13Ty],
    0x9266f07f: [exports.Bytes14Ty],
    0xda9574e0: [exports.Bytes15Ty],
    0x665c6104: [exports.Bytes16Ty],
    0x339f673a: [exports.Bytes17Ty],
    0xc4d23d9a: [exports.Bytes18Ty],
    0x5e6b5a33: [exports.Bytes19Ty],
    0x5188e3e9: [exports.Bytes20Ty],
    0xe9da3560: [exports.Bytes21Ty],
    0xd5fae89c: [exports.Bytes22Ty],
    0xaba1cf0d: [exports.Bytes23Ty],
    0xf1b35b34: [exports.Bytes24Ty],
    0x0b84bc58: [exports.Bytes25Ty],
    0xf8b149f1: [exports.Bytes26Ty],
    0x3a3757dd: [exports.Bytes27Ty],
    0xc82aeaee: [exports.Bytes28Ty],
    0x4b69c3d5: [exports.Bytes29Ty],
    0xee12c4ed: [exports.Bytes30Ty],
    0xc2854d92: [exports.Bytes31Ty],
    0x27b7cf85: [exports.Bytes32Ty],
    0xf666715a: [exports.Uint256Ty, exports.Uint256Ty],
    0x643fd0df: [exports.Uint256Ty, exports.StringTy],
    0x1c9d7eb3: [exports.Uint256Ty, exports.BoolTy],
    0x69276c86: [exports.Uint256Ty, exports.AddressTy],
    0xb60e72cc: [exports.StringTy, exports.Uint256Ty],
    0x4b5c4277: [exports.StringTy, exports.StringTy],
    0xc3b55635: [exports.StringTy, exports.BoolTy],
    0x319af333: [exports.StringTy, exports.AddressTy],
    0x399174d3: [exports.BoolTy, exports.Uint256Ty],
    0x8feac525: [exports.BoolTy, exports.StringTy],
    0x2a110e83: [exports.BoolTy, exports.BoolTy],
    0x853c4849: [exports.BoolTy, exports.AddressTy],
    0x8309e8a8: [exports.AddressTy, exports.Uint256Ty],
    0x759f86bb: [exports.AddressTy, exports.StringTy],
    0x75b605d3: [exports.AddressTy, exports.BoolTy],
    0xdaf0d4aa: [exports.AddressTy, exports.AddressTy],
    0xd1ed7a3c: [exports.Uint256Ty, exports.Uint256Ty, exports.Uint256Ty],
    0x71d04af2: [exports.Uint256Ty, exports.Uint256Ty, exports.StringTy],
    0x4766da72: [exports.Uint256Ty, exports.Uint256Ty, exports.BoolTy],
    0x5c96b331: [exports.Uint256Ty, exports.Uint256Ty, exports.AddressTy],
    0x37aa7d4c: [exports.Uint256Ty, exports.StringTy, exports.Uint256Ty],
    0xb115611f: [exports.Uint256Ty, exports.StringTy, exports.StringTy],
    0x4ceda75a: [exports.Uint256Ty, exports.StringTy, exports.BoolTy],
    0x7afac959: [exports.Uint256Ty, exports.StringTy, exports.AddressTy],
    0x20098014: [exports.Uint256Ty, exports.BoolTy, exports.Uint256Ty],
    0x85775021: [exports.Uint256Ty, exports.BoolTy, exports.StringTy],
    0x20718650: [exports.Uint256Ty, exports.BoolTy, exports.BoolTy],
    0x35085f7b: [exports.Uint256Ty, exports.BoolTy, exports.AddressTy],
    0x5a9b5ed5: [exports.Uint256Ty, exports.AddressTy, exports.Uint256Ty],
    0x63cb41f9: [exports.Uint256Ty, exports.AddressTy, exports.StringTy],
    0x9b6ec042: [exports.Uint256Ty, exports.AddressTy, exports.BoolTy],
    0xbcfd9be0: [exports.Uint256Ty, exports.AddressTy, exports.AddressTy],
    0xca47c4eb: [exports.StringTy, exports.Uint256Ty, exports.Uint256Ty],
    0x5970e089: [exports.StringTy, exports.Uint256Ty, exports.StringTy],
    0xca7733b1: [exports.StringTy, exports.Uint256Ty, exports.BoolTy],
    0x1c7ec448: [exports.StringTy, exports.Uint256Ty, exports.AddressTy],
    0x5821efa1: [exports.StringTy, exports.StringTy, exports.Uint256Ty],
    0x2ced7cef: [exports.StringTy, exports.StringTy, exports.StringTy],
    0xb0e0f9b5: [exports.StringTy, exports.StringTy, exports.BoolTy],
    0x95ed0195: [exports.StringTy, exports.StringTy, exports.AddressTy],
    0xc95958d6: [exports.StringTy, exports.BoolTy, exports.Uint256Ty],
    0xe298f47d: [exports.StringTy, exports.BoolTy, exports.StringTy],
    0x850b7ad6: [exports.StringTy, exports.BoolTy, exports.BoolTy],
    0x932bbb38: [exports.StringTy, exports.BoolTy, exports.AddressTy],
    0x0d26b925: [exports.StringTy, exports.AddressTy, exports.Uint256Ty],
    0xe0e9ad4f: [exports.StringTy, exports.AddressTy, exports.StringTy],
    0xc91d5ed4: [exports.StringTy, exports.AddressTy, exports.BoolTy],
    0xfcec75e0: [exports.StringTy, exports.AddressTy, exports.AddressTy],
    0x37103367: [exports.BoolTy, exports.Uint256Ty, exports.Uint256Ty],
    0xc3fc3970: [exports.BoolTy, exports.Uint256Ty, exports.StringTy],
    0xe8defba9: [exports.BoolTy, exports.Uint256Ty, exports.BoolTy],
    0x088ef9d2: [exports.BoolTy, exports.Uint256Ty, exports.AddressTy],
    0x1093ee11: [exports.BoolTy, exports.StringTy, exports.Uint256Ty],
    0xb076847f: [exports.BoolTy, exports.StringTy, exports.StringTy],
    0xdbb4c247: [exports.BoolTy, exports.StringTy, exports.BoolTy],
    0x9591b953: [exports.BoolTy, exports.StringTy, exports.AddressTy],
    0x12f21602: [exports.BoolTy, exports.BoolTy, exports.Uint256Ty],
    0x2555fa46: [exports.BoolTy, exports.BoolTy, exports.StringTy],
    0x50709698: [exports.BoolTy, exports.BoolTy, exports.BoolTy],
    0x1078f68d: [exports.BoolTy, exports.BoolTy, exports.AddressTy],
    0x5f7b9afb: [exports.BoolTy, exports.AddressTy, exports.Uint256Ty],
    0xde9a9270: [exports.BoolTy, exports.AddressTy, exports.StringTy],
    0x18c9c746: [exports.BoolTy, exports.AddressTy, exports.BoolTy],
    0xd2763667: [exports.BoolTy, exports.AddressTy, exports.AddressTy],
    0xb69bcaf6: [exports.AddressTy, exports.Uint256Ty, exports.Uint256Ty],
    0xa1f2e8aa: [exports.AddressTy, exports.Uint256Ty, exports.StringTy],
    0x678209a8: [exports.AddressTy, exports.Uint256Ty, exports.BoolTy],
    0x7bc0d848: [exports.AddressTy, exports.Uint256Ty, exports.AddressTy],
    0x67dd6ff1: [exports.AddressTy, exports.StringTy, exports.Uint256Ty],
    0xfb772265: [exports.AddressTy, exports.StringTy, exports.StringTy],
    0xcf020fb1: [exports.AddressTy, exports.StringTy, exports.BoolTy],
    0xf08744e8: [exports.AddressTy, exports.StringTy, exports.AddressTy],
    0x9c4f99fb: [exports.AddressTy, exports.BoolTy, exports.Uint256Ty],
    0x212255cc: [exports.AddressTy, exports.BoolTy, exports.StringTy],
    0xeb830c92: [exports.AddressTy, exports.BoolTy, exports.BoolTy],
    0xf11699ed: [exports.AddressTy, exports.BoolTy, exports.AddressTy],
    0x17fe6185: [exports.AddressTy, exports.AddressTy, exports.Uint256Ty],
    0x007150be: [exports.AddressTy, exports.AddressTy, exports.StringTy],
    0xf2a66286: [exports.AddressTy, exports.AddressTy, exports.BoolTy],
    0x018c84c2: [exports.AddressTy, exports.AddressTy, exports.AddressTy],
    0x193fb800: [exports.Uint256Ty, exports.Uint256Ty, exports.Uint256Ty, exports.Uint256Ty],
    0x59cfcbe3: [exports.Uint256Ty, exports.Uint256Ty, exports.Uint256Ty, exports.StringTy],
    0xc598d185: [exports.Uint256Ty, exports.Uint256Ty, exports.Uint256Ty, exports.BoolTy],
    0xfa8185af: [exports.Uint256Ty, exports.Uint256Ty, exports.Uint256Ty, exports.AddressTy],
    0x5da297eb: [exports.Uint256Ty, exports.Uint256Ty, exports.StringTy, exports.Uint256Ty],
    0x27d8afd2: [exports.Uint256Ty, exports.Uint256Ty, exports.StringTy, exports.StringTy],
    0x7af6ab25: [exports.Uint256Ty, exports.Uint256Ty, exports.StringTy, exports.BoolTy],
    0x42d21db7: [exports.Uint256Ty, exports.Uint256Ty, exports.StringTy, exports.AddressTy],
    0xeb7f6fd2: [exports.Uint256Ty, exports.Uint256Ty, exports.BoolTy, exports.Uint256Ty],
    0xa5b4fc99: [exports.Uint256Ty, exports.Uint256Ty, exports.BoolTy, exports.StringTy],
    0xab085ae6: [exports.Uint256Ty, exports.Uint256Ty, exports.BoolTy, exports.BoolTy],
    0x9a816a83: [exports.Uint256Ty, exports.Uint256Ty, exports.BoolTy, exports.AddressTy],
    0x88f6e4b2: [exports.Uint256Ty, exports.Uint256Ty, exports.AddressTy, exports.Uint256Ty],
    0x6cde40b8: [exports.Uint256Ty, exports.Uint256Ty, exports.AddressTy, exports.StringTy],
    0x15cac476: [exports.Uint256Ty, exports.Uint256Ty, exports.AddressTy, exports.BoolTy],
    0x56a5d1b1: [exports.Uint256Ty, exports.Uint256Ty, exports.AddressTy, exports.AddressTy],
    0x82c25b74: [exports.Uint256Ty, exports.StringTy, exports.Uint256Ty, exports.Uint256Ty],
    0xb7b914ca: [exports.Uint256Ty, exports.StringTy, exports.Uint256Ty, exports.StringTy],
    0x691a8f74: [exports.Uint256Ty, exports.StringTy, exports.Uint256Ty, exports.BoolTy],
    0x3b2279b4: [exports.Uint256Ty, exports.StringTy, exports.Uint256Ty, exports.AddressTy],
    0xb028c9bd: [exports.Uint256Ty, exports.StringTy, exports.StringTy, exports.Uint256Ty],
    0x21ad0683: [exports.Uint256Ty, exports.StringTy, exports.StringTy, exports.StringTy],
    0xb3a6b6bd: [exports.Uint256Ty, exports.StringTy, exports.StringTy, exports.BoolTy],
    0xd583c602: [exports.Uint256Ty, exports.StringTy, exports.StringTy, exports.AddressTy],
    0xcf009880: [exports.Uint256Ty, exports.StringTy, exports.BoolTy, exports.Uint256Ty],
    0xd2d423cd: [exports.Uint256Ty, exports.StringTy, exports.BoolTy, exports.StringTy],
    0xba535d9c: [exports.Uint256Ty, exports.StringTy, exports.BoolTy, exports.BoolTy],
    0xae2ec581: [exports.Uint256Ty, exports.StringTy, exports.BoolTy, exports.AddressTy],
    0xe8d3018d: [exports.Uint256Ty, exports.StringTy, exports.AddressTy, exports.Uint256Ty],
    0x9c3adfa1: [exports.Uint256Ty, exports.StringTy, exports.AddressTy, exports.StringTy],
    0x90c30a56: [exports.Uint256Ty, exports.StringTy, exports.AddressTy, exports.BoolTy],
    0x6168ed61: [exports.Uint256Ty, exports.StringTy, exports.AddressTy, exports.AddressTy],
    0xc6acc7a8: [exports.Uint256Ty, exports.BoolTy, exports.Uint256Ty, exports.Uint256Ty],
    0xde03e774: [exports.Uint256Ty, exports.BoolTy, exports.Uint256Ty, exports.StringTy],
    0x91a02e2a: [exports.Uint256Ty, exports.BoolTy, exports.Uint256Ty, exports.BoolTy],
    0x88cb6041: [exports.Uint256Ty, exports.BoolTy, exports.Uint256Ty, exports.AddressTy],
    0x2c1d0746: [exports.Uint256Ty, exports.BoolTy, exports.StringTy, exports.Uint256Ty],
    0x68c8b8bd: [exports.Uint256Ty, exports.BoolTy, exports.StringTy, exports.StringTy],
    0xeb928d7f: [exports.Uint256Ty, exports.BoolTy, exports.StringTy, exports.BoolTy],
    0xef529018: [exports.Uint256Ty, exports.BoolTy, exports.StringTy, exports.AddressTy],
    0x7464ce23: [exports.Uint256Ty, exports.BoolTy, exports.BoolTy, exports.Uint256Ty],
    0xdddb9561: [exports.Uint256Ty, exports.BoolTy, exports.BoolTy, exports.StringTy],
    0xb6f577a1: [exports.Uint256Ty, exports.BoolTy, exports.BoolTy, exports.BoolTy],
    0x69640b59: [exports.Uint256Ty, exports.BoolTy, exports.BoolTy, exports.AddressTy],
    0x078287f5: [exports.Uint256Ty, exports.BoolTy, exports.AddressTy, exports.Uint256Ty],
    0xade052c7: [exports.Uint256Ty, exports.BoolTy, exports.AddressTy, exports.StringTy],
    0x454d54a5: [exports.Uint256Ty, exports.BoolTy, exports.AddressTy, exports.BoolTy],
    0xa1ef4cbb: [exports.Uint256Ty, exports.BoolTy, exports.AddressTy, exports.AddressTy],
    0x0c9cd9c1: [exports.Uint256Ty, exports.AddressTy, exports.Uint256Ty, exports.Uint256Ty],
    0xddb06521: [exports.Uint256Ty, exports.AddressTy, exports.Uint256Ty, exports.StringTy],
    0x5f743a7c: [exports.Uint256Ty, exports.AddressTy, exports.Uint256Ty, exports.BoolTy],
    0x15c127b5: [exports.Uint256Ty, exports.AddressTy, exports.Uint256Ty, exports.AddressTy],
    0x46826b5d: [exports.Uint256Ty, exports.AddressTy, exports.StringTy, exports.Uint256Ty],
    0x3e128ca3: [exports.Uint256Ty, exports.AddressTy, exports.StringTy, exports.StringTy],
    0xcc32ab07: [exports.Uint256Ty, exports.AddressTy, exports.StringTy, exports.BoolTy],
    0x9cba8fff: [exports.Uint256Ty, exports.AddressTy, exports.StringTy, exports.AddressTy],
    0x5abd992a: [exports.Uint256Ty, exports.AddressTy, exports.BoolTy, exports.Uint256Ty],
    0x90fb06aa: [exports.Uint256Ty, exports.AddressTy, exports.BoolTy, exports.StringTy],
    0xe351140f: [exports.Uint256Ty, exports.AddressTy, exports.BoolTy, exports.BoolTy],
    0xef72c513: [exports.Uint256Ty, exports.AddressTy, exports.BoolTy, exports.AddressTy],
    0x736efbb6: [exports.Uint256Ty, exports.AddressTy, exports.AddressTy, exports.Uint256Ty],
    0x031c6f73: [exports.Uint256Ty, exports.AddressTy, exports.AddressTy, exports.StringTy],
    0x091ffaf5: [exports.Uint256Ty, exports.AddressTy, exports.AddressTy, exports.BoolTy],
    0x2488b414: [exports.Uint256Ty, exports.AddressTy, exports.AddressTy, exports.AddressTy],
    0xa7a87853: [exports.StringTy, exports.Uint256Ty, exports.Uint256Ty, exports.Uint256Ty],
    0x854b3496: [exports.StringTy, exports.Uint256Ty, exports.Uint256Ty, exports.StringTy],
    0x7626db92: [exports.StringTy, exports.Uint256Ty, exports.Uint256Ty, exports.BoolTy],
    0xe21de278: [exports.StringTy, exports.Uint256Ty, exports.Uint256Ty, exports.AddressTy],
    0xc67ea9d1: [exports.StringTy, exports.Uint256Ty, exports.StringTy, exports.Uint256Ty],
    0x5ab84e1f: [exports.StringTy, exports.Uint256Ty, exports.StringTy, exports.StringTy],
    0x7d24491d: [exports.StringTy, exports.Uint256Ty, exports.StringTy, exports.BoolTy],
    0x7c4632a4: [exports.StringTy, exports.Uint256Ty, exports.StringTy, exports.AddressTy],
    0xe41b6f6f: [exports.StringTy, exports.Uint256Ty, exports.BoolTy, exports.Uint256Ty],
    0xabf73a98: [exports.StringTy, exports.Uint256Ty, exports.BoolTy, exports.StringTy],
    0x354c36d6: [exports.StringTy, exports.Uint256Ty, exports.BoolTy, exports.BoolTy],
    0xe0e95b98: [exports.StringTy, exports.Uint256Ty, exports.BoolTy, exports.AddressTy],
    0x4f04fdc6: [exports.StringTy, exports.Uint256Ty, exports.AddressTy, exports.Uint256Ty],
    0x9ffb2f93: [exports.StringTy, exports.Uint256Ty, exports.AddressTy, exports.StringTy],
    0x82112a42: [exports.StringTy, exports.Uint256Ty, exports.AddressTy, exports.BoolTy],
    0x5ea2b7ae: [exports.StringTy, exports.Uint256Ty, exports.AddressTy, exports.AddressTy],
    0xf45d7d2c: [exports.StringTy, exports.StringTy, exports.Uint256Ty, exports.Uint256Ty],
    0x5d1a971a: [exports.StringTy, exports.StringTy, exports.Uint256Ty, exports.StringTy],
    0xc3a8a654: [exports.StringTy, exports.StringTy, exports.Uint256Ty, exports.BoolTy],
    0x1023f7b2: [exports.StringTy, exports.StringTy, exports.Uint256Ty, exports.AddressTy],
    0x8eafb02b: [exports.StringTy, exports.StringTy, exports.StringTy, exports.Uint256Ty],
    0xde68f20a: [exports.StringTy, exports.StringTy, exports.StringTy, exports.StringTy],
    0x2c1754ed: [exports.StringTy, exports.StringTy, exports.StringTy, exports.BoolTy],
    0x6d572f44: [exports.StringTy, exports.StringTy, exports.StringTy, exports.AddressTy],
    0xd6aefad2: [exports.StringTy, exports.StringTy, exports.BoolTy, exports.Uint256Ty],
    0x5e84b0ea: [exports.StringTy, exports.StringTy, exports.BoolTy, exports.StringTy],
    0x40785869: [exports.StringTy, exports.StringTy, exports.BoolTy, exports.BoolTy],
    0xc371c7db: [exports.StringTy, exports.StringTy, exports.BoolTy, exports.AddressTy],
    0x7cc3c607: [exports.StringTy, exports.StringTy, exports.AddressTy, exports.Uint256Ty],
    0xeb1bff80: [exports.StringTy, exports.StringTy, exports.AddressTy, exports.StringTy],
    0x5ccd4e37: [exports.StringTy, exports.StringTy, exports.AddressTy, exports.BoolTy],
    0x439c7bef: [exports.StringTy, exports.StringTy, exports.AddressTy, exports.AddressTy],
    0x64b5bb67: [exports.StringTy, exports.BoolTy, exports.Uint256Ty, exports.Uint256Ty],
    0x742d6ee7: [exports.StringTy, exports.BoolTy, exports.Uint256Ty, exports.StringTy],
    0x8af7cf8a: [exports.StringTy, exports.BoolTy, exports.Uint256Ty, exports.BoolTy],
    0x935e09bf: [exports.StringTy, exports.BoolTy, exports.Uint256Ty, exports.AddressTy],
    0x24f91465: [exports.StringTy, exports.BoolTy, exports.StringTy, exports.Uint256Ty],
    0xa826caeb: [exports.StringTy, exports.BoolTy, exports.StringTy, exports.StringTy],
    0x3f8a701d: [exports.StringTy, exports.BoolTy, exports.StringTy, exports.BoolTy],
    0xe0625b29: [exports.StringTy, exports.BoolTy, exports.StringTy, exports.AddressTy],
    0x8e3f78a9: [exports.StringTy, exports.BoolTy, exports.BoolTy, exports.Uint256Ty],
    0x9d22d5dd: [exports.StringTy, exports.BoolTy, exports.BoolTy, exports.StringTy],
    0x895af8c5: [exports.StringTy, exports.BoolTy, exports.BoolTy, exports.BoolTy],
    0x7190a529: [exports.StringTy, exports.BoolTy, exports.BoolTy, exports.AddressTy],
    0x5d08bb05: [exports.StringTy, exports.BoolTy, exports.AddressTy, exports.Uint256Ty],
    0x2d8e33a4: [exports.StringTy, exports.BoolTy, exports.AddressTy, exports.StringTy],
    0x958c28c6: [exports.StringTy, exports.BoolTy, exports.AddressTy, exports.BoolTy],
    0x33e9dd1d: [exports.StringTy, exports.BoolTy, exports.AddressTy, exports.AddressTy],
    0xf8f51b1e: [exports.StringTy, exports.AddressTy, exports.Uint256Ty, exports.Uint256Ty],
    0x5a477632: [exports.StringTy, exports.AddressTy, exports.Uint256Ty, exports.StringTy],
    0xfc4845f0: [exports.StringTy, exports.AddressTy, exports.Uint256Ty, exports.BoolTy],
    0x63fb8bc5: [exports.StringTy, exports.AddressTy, exports.Uint256Ty, exports.AddressTy],
    0x91d1112e: [exports.StringTy, exports.AddressTy, exports.StringTy, exports.Uint256Ty],
    0x245986f2: [exports.StringTy, exports.AddressTy, exports.StringTy, exports.StringTy],
    0x5f15d28c: [exports.StringTy, exports.AddressTy, exports.StringTy, exports.BoolTy],
    0xaabc9a31: [exports.StringTy, exports.AddressTy, exports.StringTy, exports.AddressTy],
    0x3e9f866a: [exports.StringTy, exports.AddressTy, exports.BoolTy, exports.Uint256Ty],
    0x0454c079: [exports.StringTy, exports.AddressTy, exports.BoolTy, exports.StringTy],
    0x79884c2b: [exports.StringTy, exports.AddressTy, exports.BoolTy, exports.BoolTy],
    0x223603bd: [exports.StringTy, exports.AddressTy, exports.BoolTy, exports.AddressTy],
    0x8ef3f399: [exports.StringTy, exports.AddressTy, exports.AddressTy, exports.Uint256Ty],
    0x800a1c67: [exports.StringTy, exports.AddressTy, exports.AddressTy, exports.StringTy],
    0xb59dbd60: [exports.StringTy, exports.AddressTy, exports.AddressTy, exports.BoolTy],
    0xed8f28f6: [exports.StringTy, exports.AddressTy, exports.AddressTy, exports.AddressTy],
    0x374bb4b2: [exports.BoolTy, exports.Uint256Ty, exports.Uint256Ty, exports.Uint256Ty],
    0x8e69fb5d: [exports.BoolTy, exports.Uint256Ty, exports.Uint256Ty, exports.StringTy],
    0xbe984353: [exports.BoolTy, exports.Uint256Ty, exports.Uint256Ty, exports.BoolTy],
    0x00dd87b9: [exports.BoolTy, exports.Uint256Ty, exports.Uint256Ty, exports.AddressTy],
    0x6a1199e2: [exports.BoolTy, exports.Uint256Ty, exports.StringTy, exports.Uint256Ty],
    0xf5bc2249: [exports.BoolTy, exports.Uint256Ty, exports.StringTy, exports.StringTy],
    0xe5e70b2b: [exports.BoolTy, exports.Uint256Ty, exports.StringTy, exports.BoolTy],
    0xfedd1fff: [exports.BoolTy, exports.Uint256Ty, exports.StringTy, exports.AddressTy],
    0x7f9bbca2: [exports.BoolTy, exports.Uint256Ty, exports.BoolTy, exports.Uint256Ty],
    0x9143dbb1: [exports.BoolTy, exports.Uint256Ty, exports.BoolTy, exports.StringTy],
    0xceb5f4d7: [exports.BoolTy, exports.Uint256Ty, exports.BoolTy, exports.BoolTy],
    0x9acd3616: [exports.BoolTy, exports.Uint256Ty, exports.BoolTy, exports.AddressTy],
    0x1537dc87: [exports.BoolTy, exports.Uint256Ty, exports.AddressTy, exports.Uint256Ty],
    0x1bb3b09a: [exports.BoolTy, exports.Uint256Ty, exports.AddressTy, exports.StringTy],
    0xb4c314ff: [exports.BoolTy, exports.Uint256Ty, exports.AddressTy, exports.BoolTy],
    0x26f560a8: [exports.BoolTy, exports.Uint256Ty, exports.AddressTy, exports.AddressTy],
    0x28863fcb: [exports.BoolTy, exports.StringTy, exports.Uint256Ty, exports.Uint256Ty],
    0x1ad96de6: [exports.BoolTy, exports.StringTy, exports.Uint256Ty, exports.StringTy],
    0x6b0e5d53: [exports.BoolTy, exports.StringTy, exports.Uint256Ty, exports.BoolTy],
    0x1596a1ce: [exports.BoolTy, exports.StringTy, exports.Uint256Ty, exports.AddressTy],
    0x7be0c3eb: [exports.BoolTy, exports.StringTy, exports.StringTy, exports.Uint256Ty],
    0x1762e32a: [exports.BoolTy, exports.StringTy, exports.StringTy, exports.StringTy],
    0x1e4b87e5: [exports.BoolTy, exports.StringTy, exports.StringTy, exports.BoolTy],
    0x97d394d8: [exports.BoolTy, exports.StringTy, exports.StringTy, exports.AddressTy],
    0x1606a393: [exports.BoolTy, exports.StringTy, exports.BoolTy, exports.Uint256Ty],
    0x483d0416: [exports.BoolTy, exports.StringTy, exports.BoolTy, exports.StringTy],
    0xdc5e935b: [exports.BoolTy, exports.StringTy, exports.BoolTy, exports.BoolTy],
    0x538e06ab: [exports.BoolTy, exports.StringTy, exports.BoolTy, exports.AddressTy],
    0xa5cada94: [exports.BoolTy, exports.StringTy, exports.AddressTy, exports.Uint256Ty],
    0x12d6c788: [exports.BoolTy, exports.StringTy, exports.AddressTy, exports.StringTy],
    0x6dd434ca: [exports.BoolTy, exports.StringTy, exports.AddressTy, exports.BoolTy],
    0x2b2b18dc: [exports.BoolTy, exports.StringTy, exports.AddressTy, exports.AddressTy],
    0x0bb00eab: [exports.BoolTy, exports.BoolTy, exports.Uint256Ty, exports.Uint256Ty],
    0x7dd4d0e0: [exports.BoolTy, exports.BoolTy, exports.Uint256Ty, exports.StringTy],
    0x619e4d0e: [exports.BoolTy, exports.BoolTy, exports.Uint256Ty, exports.BoolTy],
    0x54a7a9a0: [exports.BoolTy, exports.BoolTy, exports.Uint256Ty, exports.AddressTy],
    0xe3a9ca2f: [exports.BoolTy, exports.BoolTy, exports.StringTy, exports.Uint256Ty],
    0x6d1e8751: [exports.BoolTy, exports.BoolTy, exports.StringTy, exports.StringTy],
    0xb857163a: [exports.BoolTy, exports.BoolTy, exports.StringTy, exports.BoolTy],
    0xf9ad2b89: [exports.BoolTy, exports.BoolTy, exports.StringTy, exports.AddressTy],
    0x6d7045c1: [exports.BoolTy, exports.BoolTy, exports.BoolTy, exports.Uint256Ty],
    0x2ae408d4: [exports.BoolTy, exports.BoolTy, exports.BoolTy, exports.StringTy],
    0x3b2a5ce0: [exports.BoolTy, exports.BoolTy, exports.BoolTy, exports.BoolTy],
    0x8c329b1a: [exports.BoolTy, exports.BoolTy, exports.BoolTy, exports.AddressTy],
    0x4c123d57: [exports.BoolTy, exports.BoolTy, exports.AddressTy, exports.Uint256Ty],
    0xa0a47963: [exports.BoolTy, exports.BoolTy, exports.AddressTy, exports.StringTy],
    0xc0a302d8: [exports.BoolTy, exports.BoolTy, exports.AddressTy, exports.BoolTy],
    0xf4880ea4: [exports.BoolTy, exports.BoolTy, exports.AddressTy, exports.AddressTy],
    0x7bf181a1: [exports.BoolTy, exports.AddressTy, exports.Uint256Ty, exports.Uint256Ty],
    0x51f09ff8: [exports.BoolTy, exports.AddressTy, exports.Uint256Ty, exports.StringTy],
    0xd6019f1c: [exports.BoolTy, exports.AddressTy, exports.Uint256Ty, exports.BoolTy],
    0x136b05dd: [exports.BoolTy, exports.AddressTy, exports.Uint256Ty, exports.AddressTy],
    0xc21f64c7: [exports.BoolTy, exports.AddressTy, exports.StringTy, exports.Uint256Ty],
    0xa73c1db6: [exports.BoolTy, exports.AddressTy, exports.StringTy, exports.StringTy],
    0xe2bfd60b: [exports.BoolTy, exports.AddressTy, exports.StringTy, exports.BoolTy],
    0x6f7c603e: [exports.BoolTy, exports.AddressTy, exports.StringTy, exports.AddressTy],
    0x07831502: [exports.BoolTy, exports.AddressTy, exports.BoolTy, exports.Uint256Ty],
    0x4a66cb34: [exports.BoolTy, exports.AddressTy, exports.BoolTy, exports.StringTy],
    0x6a9c478b: [exports.BoolTy, exports.AddressTy, exports.BoolTy, exports.BoolTy],
    0x1c41a336: [exports.BoolTy, exports.AddressTy, exports.BoolTy, exports.AddressTy],
    0x0c66d1be: [exports.BoolTy, exports.AddressTy, exports.AddressTy, exports.Uint256Ty],
    0xd812a167: [exports.BoolTy, exports.AddressTy, exports.AddressTy, exports.StringTy],
    0x46600be0: [exports.BoolTy, exports.AddressTy, exports.AddressTy, exports.BoolTy],
    0x1d14d001: [exports.BoolTy, exports.AddressTy, exports.AddressTy, exports.AddressTy],
    0x34f0e636: [exports.AddressTy, exports.Uint256Ty, exports.Uint256Ty, exports.Uint256Ty],
    0x4a28c017: [exports.AddressTy, exports.Uint256Ty, exports.Uint256Ty, exports.StringTy],
    0x66f1bc67: [exports.AddressTy, exports.Uint256Ty, exports.Uint256Ty, exports.BoolTy],
    0x20e3984d: [exports.AddressTy, exports.Uint256Ty, exports.Uint256Ty, exports.AddressTy],
    0xbf01f891: [exports.AddressTy, exports.Uint256Ty, exports.StringTy, exports.Uint256Ty],
    0x88a8c406: [exports.AddressTy, exports.Uint256Ty, exports.StringTy, exports.StringTy],
    0xcf18105c: [exports.AddressTy, exports.Uint256Ty, exports.StringTy, exports.BoolTy],
    0x5c430d47: [exports.AddressTy, exports.Uint256Ty, exports.StringTy, exports.AddressTy],
    0x22f6b999: [exports.AddressTy, exports.Uint256Ty, exports.BoolTy, exports.Uint256Ty],
    0xc5ad85f9: [exports.AddressTy, exports.Uint256Ty, exports.BoolTy, exports.StringTy],
    0x3bf5e537: [exports.AddressTy, exports.Uint256Ty, exports.BoolTy, exports.BoolTy],
    0xa31bfdcc: [exports.AddressTy, exports.Uint256Ty, exports.BoolTy, exports.AddressTy],
    0x100f650e: [exports.AddressTy, exports.Uint256Ty, exports.AddressTy, exports.Uint256Ty],
    0x1da986ea: [exports.AddressTy, exports.Uint256Ty, exports.AddressTy, exports.StringTy],
    0xa1bcc9b3: [exports.AddressTy, exports.Uint256Ty, exports.AddressTy, exports.BoolTy],
    0x478d1c62: [exports.AddressTy, exports.Uint256Ty, exports.AddressTy, exports.AddressTy],
    0x1dc8e1b8: [exports.AddressTy, exports.StringTy, exports.Uint256Ty, exports.Uint256Ty],
    0x448830a8: [exports.AddressTy, exports.StringTy, exports.Uint256Ty, exports.StringTy],
    0x0ef7e050: [exports.AddressTy, exports.StringTy, exports.Uint256Ty, exports.BoolTy],
    0x63183678: [exports.AddressTy, exports.StringTy, exports.Uint256Ty, exports.AddressTy],
    0x159f8927: [exports.AddressTy, exports.StringTy, exports.StringTy, exports.Uint256Ty],
    0x5d02c50b: [exports.AddressTy, exports.StringTy, exports.StringTy, exports.StringTy],
    0x35a5071f: [exports.AddressTy, exports.StringTy, exports.StringTy, exports.BoolTy],
    0xa04e2f87: [exports.AddressTy, exports.StringTy, exports.StringTy, exports.AddressTy],
    0x515e38b6: [exports.AddressTy, exports.StringTy, exports.BoolTy, exports.Uint256Ty],
    0xbc0b61fe: [exports.AddressTy, exports.StringTy, exports.BoolTy, exports.StringTy],
    0x5f1d5c9f: [exports.AddressTy, exports.StringTy, exports.BoolTy, exports.BoolTy],
    0x205871c2: [exports.AddressTy, exports.StringTy, exports.BoolTy, exports.AddressTy],
    0x457fe3cf: [exports.AddressTy, exports.StringTy, exports.AddressTy, exports.Uint256Ty],
    0xf7e36245: [exports.AddressTy, exports.StringTy, exports.AddressTy, exports.StringTy],
    0x0df12b76: [exports.AddressTy, exports.StringTy, exports.AddressTy, exports.BoolTy],
    0x0d36fa20: [exports.AddressTy, exports.StringTy, exports.AddressTy, exports.AddressTy],
    0x386ff5f4: [exports.AddressTy, exports.BoolTy, exports.Uint256Ty, exports.Uint256Ty],
    0x0aa6cfad: [exports.AddressTy, exports.BoolTy, exports.Uint256Ty, exports.StringTy],
    0xc4643e20: [exports.AddressTy, exports.BoolTy, exports.Uint256Ty, exports.BoolTy],
    0xccf790a1: [exports.AddressTy, exports.BoolTy, exports.Uint256Ty, exports.AddressTy],
    0x80e6a20b: [exports.AddressTy, exports.BoolTy, exports.StringTy, exports.Uint256Ty],
    0x475c5c33: [exports.AddressTy, exports.BoolTy, exports.StringTy, exports.StringTy],
    0x50ad461d: [exports.AddressTy, exports.BoolTy, exports.StringTy, exports.BoolTy],
    0x19fd4956: [exports.AddressTy, exports.BoolTy, exports.StringTy, exports.AddressTy],
    0x8c4e5de6: [exports.AddressTy, exports.BoolTy, exports.BoolTy, exports.Uint256Ty],
    0xdfc4a2e8: [exports.AddressTy, exports.BoolTy, exports.BoolTy, exports.StringTy],
    0xcac43479: [exports.AddressTy, exports.BoolTy, exports.BoolTy, exports.BoolTy],
    0xcf394485: [exports.AddressTy, exports.BoolTy, exports.BoolTy, exports.AddressTy],
    0xa75c59de: [exports.AddressTy, exports.BoolTy, exports.AddressTy, exports.Uint256Ty],
    0x2dd778e6: [exports.AddressTy, exports.BoolTy, exports.AddressTy, exports.StringTy],
    0xa6f50b0f: [exports.AddressTy, exports.BoolTy, exports.AddressTy, exports.BoolTy],
    0x660375dd: [exports.AddressTy, exports.BoolTy, exports.AddressTy, exports.AddressTy],
    0xbe553481: [exports.AddressTy, exports.AddressTy, exports.Uint256Ty, exports.Uint256Ty],
    0xfdb4f990: [exports.AddressTy, exports.AddressTy, exports.Uint256Ty, exports.StringTy],
    0x9b4254e2: [exports.AddressTy, exports.AddressTy, exports.Uint256Ty, exports.BoolTy],
    0x8da6def5: [exports.AddressTy, exports.AddressTy, exports.Uint256Ty, exports.AddressTy],
    0xef1cefe7: [exports.AddressTy, exports.AddressTy, exports.StringTy, exports.Uint256Ty],
    0x21bdaf25: [exports.AddressTy, exports.AddressTy, exports.StringTy, exports.StringTy],
    0x6f1a594e: [exports.AddressTy, exports.AddressTy, exports.StringTy, exports.BoolTy],
    0x8f736d16: [exports.AddressTy, exports.AddressTy, exports.StringTy, exports.AddressTy],
    0x3971e78c: [exports.AddressTy, exports.AddressTy, exports.BoolTy, exports.Uint256Ty],
    0xaa6540c8: [exports.AddressTy, exports.AddressTy, exports.BoolTy, exports.StringTy],
    0x2cd4134a: [exports.AddressTy, exports.AddressTy, exports.BoolTy, exports.BoolTy],
    0x9f1bc36e: [exports.AddressTy, exports.AddressTy, exports.BoolTy, exports.AddressTy],
    0x94250d77: [exports.AddressTy, exports.AddressTy, exports.AddressTy, exports.Uint256Ty],
    0xf808da20: [exports.AddressTy, exports.AddressTy, exports.AddressTy, exports.StringTy],
    0x0e378994: [exports.AddressTy, exports.AddressTy, exports.AddressTy, exports.BoolTy],
    0x665bf134: [exports.AddressTy, exports.AddressTy, exports.AddressTy, exports.AddressTy],
};
//# sourceMappingURL=logger.js.map