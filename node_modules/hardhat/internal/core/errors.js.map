{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/internal/core/errors.ts"], "names": [], "mappings": ";;;AAAA,2DAAiE;AACjE,6CAA6C;AAE7C,+CAAsE;AAEtE,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;AAEzD,MAAa,WAAY,SAAQ,KAAK;IAGpC,YAAY,OAAe,EAAkB,MAAc;QACzD,wEAAwE;QACxE,4EAA4E;QAC5E,+CAA+C;QAC/C,EAAE;QACF,0KAA0K;QAC1K,KAAK,CAAC,OAAO,CAAC,CAAC;QAN4B,WAAM,GAAN,MAAM,CAAQ;QAQzD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAElC,mEAAmE;QACnE,IAAK,KAAa,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACjD,KAAa,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SAC1D;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;QAE/B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;YACnC,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;SAC3B,CAAC,CAAC;IACL,CAAC;IAEM,CAAC,OAAO,CAAC;QACd,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QACtB,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,MAAa,CAAC;YACvC,MAAM,WAAW,GACf,WAAW,CAAC,OAAO,CAAC,EAAE,EAAE;gBACxB,WAAW,CAAC,OAAO,EAAE,EAAE;gBACvB,WAAW,CAAC,KAAK;gBACjB,WAAW,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,cAAc,GAAG,WAAW;iBAC/B,KAAK,CAAC,IAAI,CAAC;iBACX,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC;iBACpC,IAAI,CAAC,IAAI,CAAC;iBACV,IAAI,EAAE,CAAC;YACV,GAAG,IAAI;;iBAEI,cAAc,EAAE,CAAC;SAC7B;QACD,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AA7CD,kCA6CC;AAED,MAAa,YAAa,SAAQ,WAAW;IACpC,MAAM,CAAC,cAAc,CAAC,KAAU;QACrC,OAAO,CACL,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,eAAe,KAAK,IAAI,CACxE,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAC9B,KAAU,EACV,UAA2B;QAE3B,OAAO,CACL,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC;YAClC,KAAK,CAAC,eAAe,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,CACnD,CAAC;IACJ,CAAC;IAQD,YACE,eAAgC,EAChC,mBAAoD,EAAE,EACtD,WAAmB;QAEnB,MAAM,MAAM,GAAG,GAAG,IAAA,0BAAY,EAAC,eAAe,CAAC,IAAI,CAAC;QAEpD,MAAM,gBAAgB,GAAG,yBAAyB,CAChD,eAAe,CAAC,OAAO,EACvB,gBAAgB,CACjB,CAAC;QAEF,KAAK,CAAC,MAAM,GAAG,gBAAgB,EAAE,WAAW,CAAC,CAAC;QAE9C,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;QACrC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAEzC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;CACF;AA5CD,oCA4CC;AAED;;GAEG;AACH,MAAa,kBAAmB,SAAQ,WAAW;IAC1C,MAAM,CAAC,oBAAoB,CAAC,KAAU;QAC3C,OAAO,CACL,KAAK,KAAK,SAAS;YACnB,KAAK,KAAK,IAAI;YACd,KAAK,CAAC,qBAAqB,KAAK,IAAI,CACrC,CAAC;IACJ,CAAC;IA0BD,YACE,mBAA2B,EAC3B,eAAgC,EAChC,MAAc;QAEd,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;YACvC,KAAK,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAC/B,IAAI,CAAC,UAAU,GAAG,mBAAmB,CAAC;SACvC;aAAM;YACL,KAAK,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC;YAC5C,IAAI,CAAC,UAAU,GAAG,IAAA,wCAAuB,GAAG,CAAC;SAC9C;QAED,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAC5D,CAAC;CACF;AAjDD,gDAiDC;AAED,MAAa,2BAA4B,SAAQ,kBAAkB;IAC1D,MAAM,CAAC,6BAA6B,CACzC,KAAU;QAEV,OAAO,CACL,KAAK,KAAK,SAAS;YACnB,KAAK,KAAK,IAAI;YACd,KAAK,CAAC,8BAA8B,KAAK,IAAI,CAC9C,CAAC;IACJ,CAAC;IAID;;;OAGG;IACH,YACE,UAAkB,EAClB,OAAe,EACf,MAAc,EACP,mBAAmB,KAAK;QAE/B,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAF5B,qBAAgB,GAAhB,gBAAgB,CAAQ;QAI/B,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;QAC3C,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,2BAA2B,CAAC,SAAS,CAAC,CAAC;IACrE,CAAC;CACF;AA5BD,kEA4BC;AAED;;;;;;;;;;;;;;GAcG;AACH,SAAgB,yBAAyB,CACvC,QAAgB,EAChB,MAAsC;IAEtC,OAAO,0BAA0B,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC;AALD,8DAKC;AAED,SAAS,0BAA0B,CACjC,QAAgB,EAChB,MAAsC,EACtC,eAAwB;IAExB,IAAI,CAAC,eAAe,EAAE;QACpB,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAC9C,IAAI,YAAY,CAAC,KAAK,CAAC,wBAAwB,CAAC,KAAK,IAAI,EAAE;gBACzD,MAAM,IAAI,YAAY,CAAC,oBAAM,CAAC,QAAQ,CAAC,8BAA8B,EAAE;oBACrE,QAAQ,EAAE,YAAY;iBACvB,CAAC,CAAC;aACJ;YAED,MAAM,WAAW,GAAG,IAAI,YAAY,GAAG,CAAC;YAExC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;gBACnC,MAAM,IAAI,YAAY,CAAC,oBAAM,CAAC,QAAQ,CAAC,6BAA6B,EAAE;oBACpE,QAAQ,EAAE,YAAY;iBACvB,CAAC,CAAC;aACJ;SACF;KACF;IAED,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC3B,OAAO,QAAQ;aACZ,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,0BAA0B,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;aAC7D,IAAI,CAAC,GAAG,CAAC,CAAC;KACd;IAED,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QAC9C,IAAI,KAAa,CAAC;QAElB,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,SAAS,EAAE;YACtC,KAAK,GAAG,WAAW,CAAC;SACrB;aAAM,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;YACxC,KAAK,GAAG,MAAM,CAAC;SAChB;aAAM;YACL,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;SACzC;QAED,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,KAAK,GAAG,WAAW,CAAC;SACrB;QAED,MAAM,WAAW,GAAG,IAAI,YAAY,GAAG,CAAC;QAExC,IAAI,KAAK,CAAC,KAAK,CAAC,2BAA2B,CAAC,KAAK,IAAI,EAAE;YACrD,MAAM,IAAI,YAAY,CACpB,oBAAM,CAAC,QAAQ,CAAC,oCAAoC,EACpD,EAAE,QAAQ,EAAE,YAAY,EAAE,CAC3B,CAAC;SACH;QAED,QAAQ,GAAG,IAAA,oBAAU,EAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;KACrD;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAgB,sBAAsB,CACpC,SAAkB,EAClB,OAAe;IAEf,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,YAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;KACrE;AACH,CAAC;AAPD,wDAOC"}