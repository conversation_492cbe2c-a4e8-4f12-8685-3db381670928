{"version": 3, "file": "config-resolution.js", "sourceRoot": "", "sources": ["../../../src/internal/core/config/config-resolution.ts"], "names": [], "mappings": ";;;;;;AAEA,gDAAwB;AACxB,oDAA4B;AA+B5B,+CAAuD;AAEvD,0CAA8C;AAC9C,sCAAmD;AAEnD,kDAAsD;AACtD,qDAU0B;AAE1B;;;;;;;;GAQG;AACH,SAAgB,aAAa,CAC3B,cAAsB,EACtB,UAA6B;IAE7B,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAA8B,CAAC;IAC3E,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;IAEnC,OAAO;QACL,GAAG,UAAU;QACb,cAAc,EAAE,UAAU,CAAC,cAAc,IAAI,sCAAqB;QAClE,KAAK,EAAE,mBAAmB,CAAC,cAAc,EAAE,UAAU,CAAC,KAAK,CAAC;QAC5D,QAAQ,EAAE,qBAAqB,CAAC,UAAU,CAAC,QAAQ,CAAC;QACpD,QAAQ,EAAE,qBAAqB,CAAC,UAAU,CAAC;QAC3C,KAAK,EAAE,kBAAkB,CAAC,UAAU,CAAC;KACtC,CAAC;AACJ,CAAC;AAfD,sCAeC;AAED,SAAS,qBAAqB,CAC5B,iBAAqC,EAAE;IAEvC,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAA8B,CAAC;IAC3E,MAAM,oBAAoB,GAAG,cAAc,CAAC,gCAAoB,CAAC,CAAC;IAElE,MAAM,sBAAsB,GACzB,cAAc,CAAC,SAAmC,IAAI,SAAS,CAAC;IAEnE,MAAM,OAAO,GAAG,2BAA2B,CAAC,oBAAoB,CAAC,CAAC;IAClE,MAAM,SAAS,GAAG,wBAAwB,CAAC;QACzC,GAAG,SAAS,CAAC,8CAA6B,CAAC;QAC3C,GAAG,sBAAsB;KAC1B,CAAC,CAAC;IAEH,MAAM,aAAa,GAA0C,IAAA,kBAAW,EACtE,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;SAC3B,MAAM,CACL,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,CACjB,IAAI,KAAK,WAAW;QACpB,IAAI,KAAK,SAAS;QAClB,MAAM,KAAK,SAAS;QACpB,mBAAmB,CAAC,MAAM,CAAC,CAC9B;SACA,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC;QACvB,IAAI;QACJ,wBAAwB,CAAC,MAA+B,CAAC;KAC1D,CAAC,CACL,CAAC;IAEF,OAAO;QACL,OAAO;QACP,SAAS;QACT,GAAG,aAAa;KACjB,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAC1B,MAAyB;IAEzB,OAAO,KAAK,IAAI,MAAM,CAAC;AACzB,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW;IACrC,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAC5C,IAAI,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QAC/B,OAAO,UAAU,CAAC;KACnB;IAED,OAAO,KAAK,UAAU,EAAE,CAAC;AAC3B,CAAC;AAED,SAAS,2BAA2B,CAClC,uBAAiD,EAAE;IAEnD,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAA8B,CAAC;IAC3E,MAAM,iCAAiC,GAAG,SAAS,CACjD,4CAA2B,CAC5B,CAAC;IAEF,MAAM,QAAQ,GACZ,oBAAoB,CAAC,QAAQ,KAAK,SAAS;QACzC,CAAC,CAAC,4DAA2C;QAC7C,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC;YAC9C,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC9D,UAAU,EAAE,kBAAkB,CAAC,UAAU,CAAC;gBAC1C,OAAO;aACR,CAAC,CAAC;YACL,CAAC,CAAC;gBACE,GAAG,4DAA2C;gBAC9C,GAAG,oBAAoB,CAAC,QAAQ;aACjC,CAAC;IAER,MAAM,OAAO,GACX,oBAAoB,CAAC,OAAO,KAAK,SAAS;QACxC,CAAC,CAAC;YACE,GAAG,EAAE,oBAAoB,CAAC,OAAO,CAAC,GAAG;YACrC,OAAO,EAAE,oBAAoB,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI;YACrD,WAAW,EAAE,EAAE;SAChB;QACH,CAAC,CAAC,SAAS,CAAC;IAEhB,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,MAAM,WAAW,GAAG,oBAAoB,EAAE,OAAO,EAAE,WAAW,CAAC;QAC/D,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,OAAO,CAAC,WAAW,GAAG,oBAAoB,EAAE,OAAO,EAAE,WAAW,CAAC;SAClE;QAED,MAAM,WAAW,GAAG,oBAAoB,CAAC,OAAO,EAAE,WAAW,CAAC;QAC9D,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;SACnC;KACF;IAED,MAAM,MAAM,GAAG,mBAAmB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IAEhE,MAAM,WAAW,GAAG,MAAM,CACxB,oBAAoB,CAAC,WAAW;QAC9B,iCAAiC,CAAC,WAAW,CAChD,CAAC;IAEF,MAAM,aAAa,GACjB,oBAAoB,CAAC,aAAa;QAClC,iCAAiC,CAAC,aAAa,CAAC;IAElD,MAAM,GAAG,GAAG,oBAAoB,CAAC,GAAG,IAAI,aAAa,CAAC;IACtD,MAAM,QAAQ,GACZ,oBAAoB,CAAC,QAAQ,IAAI,iCAAiC,CAAC,QAAQ,CAAC;IAC9E,MAAM,oBAAoB,GACxB,oBAAoB,CAAC,oBAAoB;QACzC,iCAAiC,CAAC,oBAAoB,CAAC;IAEzD,MAAM,WAAW,GACf,oBAAoB,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAE/D,MAAM,MAAM,GAA+B,IAAI,GAAG,CAChD,4CAA2B,CAAC,MAAM,CACnC,CAAC;IACF,IAAI,oBAAoB,CAAC,MAAM,KAAK,SAAS,EAAE;QAC7C,KAAK,MAAM,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,MAAM,CAAC,OAAO,CACrD,oBAAoB,CAAC,MAAM,CAC5B,EAAE;YACD,MAAM,WAAW,GAA8B;gBAC7C,eAAe,EAAE,IAAI,GAAG,EAAE;aAC3B,CAAC;YACF,IAAI,eAAe,CAAC,eAAe,KAAK,SAAS,EAAE;gBACjD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CACxC,eAAe,CAAC,eAAe,CAChC,EAAE;oBACD,WAAW,CAAC,eAAe,CAAC,GAAG,CAC7B,IAAoB,EACpB,KAAe,CAChB,CAAC;iBACH;aACF;YACD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;SAChD;KACF;IAED,MAAM,MAAM,GAAyB;QACnC,GAAG,iCAAiC;QACpC,GAAG,oBAAoB;QACvB,QAAQ;QACR,OAAO;QACP,MAAM;QACN,aAAa;QACb,GAAG;QACH,QAAQ;QACR,oBAAoB;QACpB,WAAW;QACX,WAAW;QACX,MAAM;KACP,CAAC;IAEF,mDAAmD;IACnD,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE;QAChC,OAAO,MAAM,CAAC,OAAO,CAAC;KACvB;IACD,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;QAC7C,OAAO,MAAM,CAAC,oBAAoB,CAAC;KACpC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CACzB,QAAuC;IAEvC,OAAO,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,wBAAwB,CAC/B,aAAoC;IAEpC,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAA8B,CAAC;IAC3E,MAAM,QAAQ,GACZ,aAAa,CAAC,QAAQ,KAAK,SAAS;QAClC,CAAC,CAAC,yCAAwB,CAAC,QAAQ;QACnC,CAAC,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,CAAC,CAAC;gBACE,GAAG,8CAA6B;gBAChC,GAAG,aAAa,CAAC,QAAQ;aAC1B;YACH,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;gBACvC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC;gBAChD,CAAC,CAAC,QAAQ,CAAC;IAEf,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;IAE9B,IAAA,+BAAsB,EACpB,GAAG,KAAK,SAAS,EACjB,oDAAoD,CACrD,CAAC;IAEF,OAAO;QACL,GAAG,SAAS,CAAC,yCAAwB,CAAC;QACtC,GAAG,aAAa;QAChB,QAAQ;QACR,GAAG;QACH,GAAG,EAAE,aAAa,CAAC,GAAG,IAAI,yCAAwB,CAAC,GAAG;QACtD,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,yCAAwB,CAAC,QAAQ;KACtE,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAC1B,UAAsD;IAEtD,MAAM,OAAO,GAAG,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAC1D,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,OAAO;YACL,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,CAAC;YACX,OAAO;SACR,CAAC;KACH;IAED,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAC;IAEtC,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE;QAChD,OAAO;YACL,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,CAAC;YACX,OAAO;SACR,CAAC;KACH;IAED,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE;QAChD,OAAO;YACL,IAAI,EAAE,KAAK;YACX,QAAQ;YACR,OAAO;SACR,CAAC;KACH;IAED,IAAI,IAAI,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE;QAChD,OAAO;YACL,IAAI;YACJ,QAAQ,EAAE,CAAC;YACX,OAAO;SACR,CAAC;KACH;IAED,sDAAsD;IACtD,OAAO;QACL,IAAI,EAAE,IAAK;QACX,QAAQ,EAAE,QAAS;QACnB,OAAO;KACR,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAC3B,UAAuD;IAEvD,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,OAAO;YACL,KAAK,EAAE,UAAU;SAClB,CAAC;KACH;IAED,IAAI,UAAU,CAAC,KAAK,KAAK,SAAS,EAAE;QAClC,OAAO;YACL,KAAK,EAAE,UAAU;SAClB,CAAC;KACH;IAED,OAAO;QACL,KAAK,EAAE,UAAU,CAAC,KAAK;KACO,CAAC;AACnC,CAAC;AAED,SAAS,qBAAqB,CAAC,UAA6B;IAC1D,MAAM,kBAAkB,GAAG,UAAU,CAAC,QAAQ,IAAI,qCAAoB,CAAC;IAEvE,MAAM,eAAe,GACnB,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;IAE9C,MAAM,SAAS,GAAG,eAAe,CAAC,SAAS,IAAI,EAAE,CAAC;IAElD,OAAO;QACL,SAAS,EAAE,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC;QACzD,SAAS,EAAE,IAAA,kBAAW,EACpB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC;YAChD,IAAI;YACJ,eAAe,CAAC,MAAM,CAAC;SACxB,CAAC,CACH;KACF,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAC9B,cAAkC;IAElC,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;QACtC,OAAO;YACL,SAAS,EAAE;gBACT;oBACE,OAAO,EAAE,cAAc;iBACxB;aACF;SACF,CAAC;KACH;IAED,IAAI,SAAS,IAAI,cAAc,EAAE;QAC/B,OAAO,EAAE,SAAS,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC;KACxC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,eAAe,CAAC,QAAwB;IAC/C,MAAM,QAAQ,GAAe;QAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;QACzB,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,EAAE;KAClC,CAAC;IAEF,IAAI,gBAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;QAC1C,QAAQ,CAAC,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ,EAAE,UAAU,IAAI,OAAO,CAAC;KACzE;IAED,QAAQ,CAAC,QAAQ,CAAC,SAAS,GAAG;QAC5B,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,GAAG;QACT,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS;KAC/B,CAAC;IAEF,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE;QACnD,QAAQ,CAAC,QAAQ,CAAC,eAAe,GAAG,EAAE,CAAC;KACxC;IAED,KAAK,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,IAAI,MAAM,CAAC,OAAO,CACpD,2CAA0B,CAC3B,EAAE;QACD,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;YACzD,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;SAC9C;QAED,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACnE,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE;gBACnE,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;aACxD;YAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;gBAC5B,MAAM,cAAc,GAClB,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAErE,IAAI,CAAC,cAAc,EAAE;oBACnB,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAChE;aACF;SACF;KACF;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,kBAAkB,CAAC,UAA6B;IACvD,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAA8B,CAAC;IAC3E,OAAO;QACL,GAAG,SAAS,CAAC,oCAAmB,CAAC;QACjC,GAAG,UAAU,CAAC,KAAK;KACpB,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;GAWG;AACH,SAAgB,mBAAmB,CACjC,cAAsB,EACtB,YAAoC,EAAE;IAEtC,MAAM,UAAU,GAAG,IAAA,0BAAe,EAAC,cAAc,CAAC,CAAC;IACnD,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAE3C,MAAM,IAAI,GAAG,eAAe,CAAC,SAAS,EAAE,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;IAE5D,OAAO;QACL,GAAG,SAAS;QACZ,IAAI;QACJ,UAAU;QACV,OAAO,EAAE,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,CAAC,OAAO,CAAC;QAC9D,KAAK,EAAE,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,KAAK,CAAC;QACtD,SAAS,EAAE,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,CAAC,SAAS,CAAC;QAClE,KAAK,EAAE,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,KAAK,CAAC;KACtD,CAAC;AACJ,CAAC;AAlBD,kDAkBC;AAED,SAAS,eAAe,CACtB,IAAY,EACZ,WAAmB,EACnB,yBAAiC,WAAW;IAE5C,IAAI,cAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE;QAC3C,OAAO,sBAAsB,CAAC;KAC/B;IAED,OAAO,cAAI,CAAC,IAAI,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;AACjD,CAAC"}