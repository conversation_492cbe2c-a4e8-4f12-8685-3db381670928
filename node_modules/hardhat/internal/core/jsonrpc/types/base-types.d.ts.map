{"version": 3, "file": "base-types.d.ts", "sourceRoot": "", "sources": ["../../../../src/internal/core/jsonrpc/types/base-types.ts"], "names": [], "mappings": ";;AAKA,OAAO,KAAK,CAAC,MAAM,OAAO,CAAC;AAS3B,eAAO,MAAM,WAAW,iCAKvB,CAAC;AAEF,eAAO,MAAM,OAAO,iCAMnB,CAAC;AAEF,eAAO,MAAM,SAAS,iCAMrB,CAAC;AAEF,eAAO,MAAM,OAAO,iCAMnB,CAAC;AAEF,eAAO,MAAM,cAAc,iCAK1B,CAAC;AAKF,eAAO,MAAM,uBAAuB,iCAMnC,CAAC;AA4CF,eAAO,MAAM,UAAU,iCAQtB,CAAC;AAEF,eAAO,MAAM,kBAAkB,iCAK9B,CAAC;AAEF,eAAO,MAAM,mBAAmB,iCAK/B,CAAC;AAEF,eAAO,MAAM,QAAQ,iCAKpB,CAAC;AAIF;;;GAGG;AACH,wBAAgB,mBAAmB,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAE5D;AAED,wBAAgB,mBAAmB,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAS5D;AAED,wBAAgB,mBAAmB,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,CAO9D;AAED,wBAAgB,sBAAsB,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,CAOjE;AAED;;;GAGG;AACH,wBAAgB,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAEpD;AAED,wBAAgB,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAEpD;AAED,wBAAgB,eAAe,CAC7B,MAAM,EAAE,UAAU,EAClB,UAAU,GAAE,MAAU,GACrB,MAAM,CAMR;AAED,wBAAgB,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CASpD"}