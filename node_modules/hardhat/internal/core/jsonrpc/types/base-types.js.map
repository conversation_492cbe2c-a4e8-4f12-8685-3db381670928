{"version": 3, "file": "base-types.js", "sourceRoot": "", "sources": ["../../../../src/internal/core/jsonrpc/types/base-types.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAI0B;AAC1B,yCAA2B;AAE3B,kEAAoD;AACpD,yCAAoE;AACpE,mDAA2C;AAE3C,MAAM,oBAAoB,GAAG,EAAE,CAAC;AAChC,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAEhB,QAAA,WAAW,GAAG,IAAI,CAAC,CAAC,IAAI,CACnC,UAAU,EACV,WAAW,CAAC,QAAQ,EACpB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAC3E,CAAC,CAAC,QAAQ,CACX,CAAC;AAEW,QAAA,OAAO,GAAG,IAAI,CAAC,CAAC,IAAI,CAC/B,MAAM,EACN,MAAM,CAAC,QAAQ,EACf,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACP,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,cAAO,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3E,CAAC,CAAC,QAAQ,CACX,CAAC;AAEW,QAAA,SAAS,GAAG,IAAI,CAAC,CAAC,IAAI,CACjC,QAAQ,EACR,MAAM,CAAC,QAAQ,EACf,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACP,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,cAAO,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC7E,CAAC,CAAC,QAAQ,CACX,CAAC;AAEW,QAAA,OAAO,GAAG,IAAI,CAAC,CAAC,IAAI,CAC/B,MAAM,EACN,CAAC,CAAC,EAAe,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,iBAAiB,EACxE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACP,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,cAAO,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3E,CAAC,CAAC,QAAQ,CACX,CAAC;AAEW,QAAA,cAAc,GAAG,IAAI,CAAC,CAAC,IAAI,CACtC,cAAc,EACd,WAAW,CAAC,QAAQ,EACpB,mBAAmB,EACnB,CAAC,CAAC,QAAQ,CACX,CAAC;AAEF,kIAAkI;AAClI,qIAAqI;AACrI,yEAAyE;AAC5D,QAAA,uBAAuB,GAAG,IAAI,CAAC,CAAC,IAAI,CAC/C,yBAAyB,EACzB,CAAC,CAAC,EAAe,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,EACzC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACP,+BAA+B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACrE,CAAC,CAAC,QAAQ,CACX,CAAC;AAEF,SAAS,mBAAmB,CAAC,CAAU,EAAE,CAAY;IACnD,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAO,CAAC,CAAC,OAAO,CACd,CAAC,EACD,CAAC,EACD,gDAAgD,CAAQ,GAAG,CAC5D,CAAC;KACH;IAED,IAAI,CAAC,KAAK,EAAE,EAAE;QACZ,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,iDAAiD,CAAC,CAAC;KAC3E;IAED,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QACtB,IAAI,CAAC,CAAC,MAAM,GAAG,EAAE,EAAE;YACjB,OAAO,CAAC,CAAC,OAAO,CACd,CAAC,EACD,CAAC,EACD,kFAAkF,CAAC,qBAAqB,CAAC,CAAC,MAAM,EAAE,CACnH,CAAC;SACH;KACF;SAAM;QACL,IAAI,CAAC,CAAC,MAAM,GAAG,EAAE,EAAE;YACjB,OAAO,CAAC,CAAC,OAAO,CACd,CAAC,EACD,CAAC,EACD,2EAA2E,CAAC,qBAAqB,CAAC,CAAC,MAAM,EAAE,CAC5G,CAAC;SACH;KACF;IAED,IAAI,CAAC,CAAC,KAAK,CAAC,4BAA4B,CAAC,KAAK,IAAI,EAAE;QAClD,OAAO,CAAC,CAAC,OAAO,CACd,CAAC,EACD,CAAC,EACD,2DAA2D,CAAC,GAAG,CAChE,CAAC;KACH;IAED,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AAChF,CAAC;AAEY,QAAA,UAAU,GAAG,IAAI,CAAC,CAAC,IAAI,CAClC,SAAS,EACT,CAAC,CAAC,EAAe,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,oBAAoB,EAC3E,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACP,kBAAkB,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,cAAO,EAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACrB,CAAC,CAAC,QAAQ,CACX,CAAC;AAEW,QAAA,kBAAkB,GAAG,IAAI,CAAC,CAAC,IAAI,CAC1C,kBAAkB,EAClB,SAAS,EACT,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACnE,CAAC,CAAC,QAAQ,CACX,CAAC;AAEW,QAAA,mBAAmB,GAAG,IAAI,CAAC,CAAC,IAAI,CAC3C,SAAS,EACT,WAAW,CAAC,QAAQ,EACpB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACjE,CAAC,CAAC,QAAQ,CACX,CAAC;AAEW,QAAA,QAAQ,GAAG,IAAI,CAAC,CAAC,IAAI,CAChC,cAAc,EACd,QAAQ,EACR,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAClE,CAAC,CAAC,QAAQ,CACX,CAAC;AAEF,uBAAuB;AAEvB;;;GAGG;AACH,SAAgB,mBAAmB,CAAC,QAAgB;IAClD,OAAO,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/C,CAAC;AAFD,kDAEC;AAED,SAAgB,mBAAmB,CAAC,QAAgB;IAClD,iEAAiE;IACjE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE;QAClC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,0BAA0B,EAAE;YAChE,KAAK,EAAE,QAAQ;SAChB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC1B,CAAC;AATD,kDASC;AAED,SAAgB,mBAAmB,CAAC,CAAkB;IACpD,IAAA,+BAAsB,EACpB,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAC9C,iBAAiB,CAClB,CAAC;IAEF,OAAO,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;AAC/B,CAAC;AAPD,kDAOC;AAED,SAAgB,sBAAsB,CAAC,CAAkB;IACvD,IAAA,+BAAsB,EACpB,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAC9C,iBAAiB,CAClB,CAAC;IAEF,OAAO,KAAK,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;AACzC,CAAC;AAPD,wDAOC;AAED;;;GAGG;AACH,SAAgB,eAAe,CAAC,IAAY;IAC1C,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;AACvC,CAAC;AAFD,0CAEC;AAED,SAAgB,eAAe,CAAC,IAAY;IAC1C,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3C,CAAC;AAFD,0CAEC;AAED,SAAgB,eAAe,CAC7B,MAAkB,EAClB,aAAqB,CAAC;IAEtB,IAAI,CAAC,GAAG,IAAA,iBAAW,EAAC,MAAM,CAAC,CAAC;IAC5B,IAAI,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE;QACnD,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;KACnE;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AATD,0CASC;AAED,SAAgB,eAAe,CAAC,IAAY;IAC1C,iEAAiE;IACjE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;QAC1B,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,sBAAsB,EAAE;YAC5D,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,cAAO,EAAC,IAAI,CAAC,CAAC,CAAC;AACpC,CAAC;AATD,0CASC;AAED,cAAc;AAEd,SAAS,+BAA+B,CAAC,CAAU;IACjD,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,mBAAmB,CAAC,CAAU;IACrC,OAAO,CACL,OAAO,CAAC,KAAK,QAAQ;QACrB,CAAC,CAAC,KAAK,CAAC,uCAAuC,CAAC,KAAK,IAAI,CAC1D,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,CAAU;IACjC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,yBAAyB,CAAC,KAAK,IAAI,CAAC;AAC9E,CAAC;AAED,SAAS,iBAAiB,CAAC,CAAU;IACnC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,KAAK,IAAI,CAAC;AAC3E,CAAC;AAED,SAAS,eAAe,CAAC,CAAU;IACjC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC;AAED,SAAS,kBAAkB,CAAC,CAAU;IACpC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,IAAA,qBAAc,EAAC,CAAC,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,SAAS,CAAC,GAAY;IAC7B,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,QAAQ,CAAC,GAAY;IAC5B,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC;AACjC,CAAC"}