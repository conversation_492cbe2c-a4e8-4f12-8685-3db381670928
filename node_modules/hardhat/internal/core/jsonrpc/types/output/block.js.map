{"version": 3, "file": "block.js", "sourceRoot": "", "sources": ["../../../../../src/internal/core/jsonrpc/types/output/block.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA2B;AAE3B,kDAA4D;AAC5D,8CAA0E;AAE1E,+CAA+C;AAE/C,MAAM,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAC9B;IACE,KAAK,EAAE,wBAAW;IAClB,cAAc,EAAE,wBAAW;IAC3B,OAAO,EAAE,uBAAU;IACnB,MAAM,EAAE,wBAAW;CACpB,EACD,wBAAwB,CACzB,CAAC;AAEF,MAAM,iBAAiB,GAAG;IACxB,MAAM,EAAE,IAAA,gBAAQ,EAAC,wBAAW,CAAC;IAC7B,IAAI,EAAE,IAAA,gBAAQ,EAAC,oBAAO,CAAC;IACvB,UAAU,EAAE,oBAAO;IACnB,KAAK,EAAE,IAAA,gBAAQ,EAAC,oBAAO,CAAC;IACxB,UAAU,EAAE,oBAAO;IACnB,SAAS,EAAE,oBAAO;IAClB,gBAAgB,EAAE,oBAAO;IACzB,SAAS,EAAE,oBAAO;IAClB,YAAY,EAAE,oBAAO;IACrB,KAAK,EAAE,uBAAU;IACjB,UAAU,EAAE,wBAAW;IACvB,yEAAyE;IACzE,eAAe,EAAE,IAAA,gBAAQ,EAAC,wBAAW,CAAC;IACtC,SAAS,EAAE,oBAAO;IAClB,IAAI,EAAE,wBAAW;IACjB,QAAQ,EAAE,wBAAW;IACrB,OAAO,EAAE,wBAAW;IACpB,SAAS,EAAE,wBAAW;IACtB,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,oBAAO,EAAE,YAAY,CAAC;IACtC,OAAO,EAAE,IAAA,gBAAQ,EAAC,oBAAO,CAAC;IAC1B,aAAa,EAAE,IAAA,gBAAQ,EAAC,wBAAW,CAAC;IACpC,WAAW,EAAE,IAAA,gBAAQ,EAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACjD,eAAe,EAAE,IAAA,gBAAQ,EAAC,oBAAO,CAAC;IAClC,qBAAqB,EAAE,IAAA,gBAAQ,EAAC,oBAAO,CAAC;IACxC,WAAW,EAAE,IAAA,gBAAQ,EAAC,wBAAW,CAAC;IAClC,aAAa,EAAE,IAAA,gBAAQ,EAAC,wBAAW,CAAC;CACrC,CAAC;AAIW,QAAA,QAAQ,GAAG,CAAC,CAAC,IAAI,CAC5B;IACE,GAAG,iBAAiB;IACpB,YAAY,EAAE,CAAC,CAAC,KAAK,CAAC,oBAAO,EAAE,YAAY,CAAC;CAC7C,EACD,UAAU,CACX,CAAC;AAMW,QAAA,wBAAwB,GAAG,CAAC,CAAC,IAAI,CAC5C;IACE,GAAG,iBAAiB;IACpB,YAAY,EAAE,CAAC,CAAC,KAAK,CAAC,4BAAc,EAAE,sBAAsB,CAAC;CAC9D,EACD,0BAA0B,CAC3B,CAAC"}