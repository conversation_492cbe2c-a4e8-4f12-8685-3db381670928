{"version": 3, "file": "gas-providers.js", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/gas-providers.ts"], "names": [], "mappings": ";;;AACA,4DAIqC;AAErC,uCAA4C;AAE5C,MAAM,sBAAsB,GAAG,CAAC,CAAC;AAEjC,MAAa,gBAAiB,SAAQ,yBAAe;IACnD,YAAY,QAAyB,EAAmB,SAAiB;QACvE,KAAK,CAAC,QAAQ,CAAC,CAAC;QADsC,cAAS,GAAT,SAAS,CAAQ;IAEzE,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB,EAAE;YACzC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAErC,sCAAsC;YACtC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,CAAC,GAAG,KAAK,SAAS,EAAE;gBAC5C,EAAE,CAAC,GAAG,GAAG,IAAA,gCAAmB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9C;SACF;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;CACF;AAlBD,4CAkBC;AAED,MAAa,qBAAsB,SAAQ,yBAAe;IACxD,YAAY,QAAyB,EAAmB,SAAiB;QACvE,KAAK,CAAC,QAAQ,CAAC,CAAC;QADsC,cAAS,GAAT,SAAS,CAAQ;IAEzE,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB,EAAE;YACzC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAErC,sCAAsC;YACtC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,sCAAsC;YACtC,IACE,EAAE,KAAK,SAAS;gBAChB,EAAE,CAAC,QAAQ,KAAK,SAAS;gBACzB,EAAE,CAAC,YAAY,KAAK,SAAS;gBAC7B,EAAE,CAAC,oBAAoB,KAAK,SAAS,EACrC;gBACA,EAAE,CAAC,QAAQ,GAAG,IAAA,gCAAmB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACnD;SACF;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;CACF;AAxBD,sDAwBC;AAED,MAAe,+BAAgC,SAAQ,yBAAe;IAGpE,YACE,QAAyB,EACR,cAAsB;QAEvC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAFC,mBAAc,GAAd,cAAc,CAAQ;IAGzC,CAAC;IAES,KAAK,CAAC,2BAA2B,CAAC,MAAa;QACvD,IAAI;YACF,MAAM,cAAc,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBAC1D,MAAM,EAAE,iBAAiB;gBACzB,MAAM;aACP,CAAC,CAAW,CAAC;YAEd,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,EAAE;gBAC7B,OAAO,cAAc,CAAC;aACvB;YAED,MAAM,SAAS,GAAG,IAAA,gCAAmB,EAAC,cAAc,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEhD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/D,MAAM,GAAG,GAAG,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YAE9D,OAAO,IAAA,gCAAmB,EAAC,GAAG,CAAC,CAAC;SACjC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;oBAC3D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACrD,OAAO,IAAA,gCAAmB,EAAC,aAAa,CAAC,CAAC;iBAC3C;aACF;YAED,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrC,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACvD,MAAM,EAAE,sBAAsB;gBAC9B,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC;aAC1B,CAAC,CAAyB,CAAC;YAE5B,MAAM,eAAe,GAAG,IAAA,gCAAmB,EAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAElE,+DAA+D;YAC/D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC;SAC1D;QAED,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;CACF;AAED,MAAa,oBAAqB,SAAQ,+BAA+B;IACvE,YACE,QAAyB,EACzB,gBAAwB,sBAAsB;QAE9C,KAAK,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IACjC,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB,EAAE;YACzC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAErC,sCAAsC;YACtC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,CAAC,GAAG,KAAK,SAAS,EAAE;gBAC5C,EAAE,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;aACzD;SACF;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;CACF;AArBD,oDAqBC;AAED,MAAa,yBAA0B,SAAQ,yBAAe;IAYrD,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB,EAAE;YACzC,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC5C;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAErC,sCAAsC;QACtC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAErB,IAAI,EAAE,KAAK,SAAS,EAAE;YACpB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC5C;QAED,8CAA8C;QAC9C,IACE,EAAE,CAAC,QAAQ,KAAK,SAAS;YACzB,CAAC,EAAE,CAAC,YAAY,KAAK,SAAS,IAAI,EAAE,CAAC,oBAAoB,KAAK,SAAS,CAAC,EACxE;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC5C;QAED,IAAI,sBAAsB,GAAG,MAAM,IAAI,CAAC,6BAA6B,EAAE,CAAC;QAExE,iDAAiD;QACjD,IACE,EAAE,CAAC,YAAY,KAAK,SAAS;YAC7B,EAAE,CAAC,oBAAoB,KAAK,SAAS;YACrC,sBAAsB,KAAK,SAAS,EACpC;YACA,EAAE,CAAC,QAAQ,GAAG,IAAA,gCAAmB,EAAC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC5C;QAED,4EAA4E;QAC5E,yCAAyC;QACzC,IAAI,sBAAsB,KAAK,SAAS,EAAE;YACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAE3C,sBAAsB,GAAG;gBACvB,YAAY,EAAE,QAAQ;gBACtB,oBAAoB,EAAE,QAAQ;aAC/B,CAAC;SACH;QAED,IAAI,YAAY,GACd,EAAE,CAAC,YAAY,KAAK,SAAS;YAC3B,CAAC,CAAC,IAAA,gCAAmB,EAAC,EAAE,CAAC,YAAY,CAAC;YACtC,CAAC,CAAC,sBAAsB,CAAC,YAAY,CAAC;QAE1C,MAAM,oBAAoB,GACxB,EAAE,CAAC,oBAAoB,KAAK,SAAS;YACnC,CAAC,CAAC,IAAA,gCAAmB,EAAC,EAAE,CAAC,oBAAoB,CAAC;YAC9C,CAAC,CAAC,sBAAsB,CAAC,oBAAoB,CAAC;QAElD,IAAI,YAAY,GAAG,oBAAoB,EAAE;YACvC,YAAY,IAAI,oBAAoB,CAAC;SACtC;QAED,EAAE,CAAC,YAAY,GAAG,IAAA,gCAAmB,EAAC,YAAY,CAAC,CAAC;QACpD,EAAE,CAAC,oBAAoB,GAAG,IAAA,gCAAmB,EAAC,oBAAoB,CAAC,CAAC;QAEpE,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACpD,MAAM,EAAE,cAAc;SACvB,CAAC,CAAW,CAAC;QAEd,OAAO,IAAA,gCAAmB,EAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAEO,KAAK,CAAC,6BAA6B;QAOzC,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACjD,MAAM,EAAE,sBAAsB;gBAC9B,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC;aAC1B,CAAC,CAAQ,CAAC;YAEX,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC;SAC/D;QAED,IACE,IAAI,CAAC,kBAAkB,KAAK,KAAK;YACjC,IAAI,CAAC,oBAAoB,KAAK,KAAK,EACnC;YACA,OAAO;SACR;QAED,IAAI;YACF,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACpD,MAAM,EAAE,gBAAgB;gBACxB,MAAM,EAAE;oBACN,KAAK;oBACL,QAAQ;oBACR,CAAC,yBAAyB,CAAC,yBAAyB,CAAC;iBACtD;aACF,CAAC,CAAoD,CAAC;YAEvD,IAAI,oBAAoB,GAAG,IAAA,gCAAmB,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtE,IAAI,oBAAoB,KAAK,EAAE,EAAE;gBAC/B,IAAI;oBACF,MAAM,6BAA6B,GACjC,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;wBACnC,MAAM,EAAE,0BAA0B;wBAClC,MAAM,EAAE,EAAE;qBACX,CAAC,CAAW,CAAC;oBAEhB,oBAAoB,GAAG,IAAA,gCAAmB,EACxC,6BAA6B,CAC9B,CAAC;iBACH;gBAAC,MAAM;oBACN,wDAAwD;oBACxD,oBAAoB,GAAG,EAAE,CAAC;iBAC3B;aACF;YAED,uEAAuE;YACvE,kEAAkE;YAClE,wEAAwE;YACxE,uCAAuC;YACvC,IAAI,oBAAoB,KAAK,EAAE,EAAE;gBAC/B,oBAAoB,GAAG,EAAE,CAAC;aAC3B;YAED,OAAO;gBACL,+DAA+D;gBAC/D,iEAAiE;gBACjE,sBAAsB;gBAEtB,YAAY,EACV,CAAC,IAAA,gCAAmB,EAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAC7C,EAAE;wBACA,CAAC,yBAAyB,CAAC,2CAA2C;4BACpE,EAAE,CAAC,CAAC;oBACV,EAAE;wBACA,CAAC,yBAAyB,CAAC,2CAA2C;4BACpE,EAAE,CAAC;gBAET,oBAAoB;aACrB,CAAC;SACH;QAAC,MAAM;YACN,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAEhC,OAAO,SAAS,CAAC;SAClB;IACH,CAAC;;AArKD,2DAA2D;AAC3D,wDAAwD;AACjC,qEAA2C,GAChE,EAAE,CAAC;AAEL,2DAA2D;AACpC,mDAAyB,GAAG,EAAE,CAAC;AAP3C,8DAAyB"}