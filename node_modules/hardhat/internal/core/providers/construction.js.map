{"version": 3, "file": "construction.js", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/construction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,+CAAuD;AACvD,0CAAkD;AAElD,iCAA+D;AAE/D,SAAgB,kBAAkB,CAChC,QAAwC;IAExC,OAAO,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC9E,CAAC;AAJD,gDAIC;AAED,SAAS,2BAA2B,CAClC,SAAiC;IAEjC,OAAO,KAAK,IAAI,SAAS,CAAC;AAC5B,CAAC;AAED,uEAAuE;AACvE,iBAAiB;AACjB,8EAA8E;AAC9E,4EAA4E;AAC5E,SAAS,cAAc,CACrB,QAAgB,EAChB,IAAmB;IAEnB,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC9B,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;AACnB,CAAC;AAEM,KAAK,UAAU,cAAc,CAClC,MAAqB,EACrB,WAAmB,EACnB,SAAqB,EACrB,YAAgC,EAAE;IAElC,IAAI,eAAgC,CAAC;IACrC,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACnD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IAE3B,IAAI,WAAW,KAAK,gCAAoB,EAAE;QACxC,MAAM,gBAAgB,GAAG,aAAqC,CAAC;QAE/D,MAAM,EAAE,4BAA4B,EAAE,GAAG,wDACvC,yCAAyC,GAC1C,CAAC;QAEF,IAAI,UAAkC,CAAC;QAEvC,IACE,gBAAgB,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI;YAC1C,gBAAgB,CAAC,OAAO,EAAE,GAAG,KAAK,SAAS,EAC3C;YACA,UAAU,GAAG;gBACX,UAAU,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG;gBACzC,WAAW,EAAE,gBAAgB,CAAC,OAAO,EAAE,WAAW;gBAClD,WAAW,EAAE,gBAAgB,CAAC,OAAO,CAAC,WAAW;aAClD,CAAC;SACH;QAED,MAAM,QAAQ,GAAG,IAAA,4CAAqC,EACpD,gBAAgB,CAAC,QAAQ,CAC1B,CAAC;QAEF,MAAM,EAAE,mBAAmB,EAAE,GAC3B,OAAO,CAAC,iDAAiD,CAAsB,CAAC;QAElF,eAAe,GAAG,MAAM,4BAA4B,CAClD;YACE,OAAO,EAAE,gBAAgB,CAAC,OAAO;YACjC,SAAS,EAAE,gBAAgB,CAAC,OAAO;YACnC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;YACnC,aAAa,EAAE,gBAAgB,CAAC,aAAa;YAC7C,oBAAoB,EAAE,gBAAgB,CAAC,oBAAoB;YAC3D,WAAW,EAAE,gBAAgB,CAAC,WAAW;YACzC,0BAA0B,EAAE,gBAAgB,CAAC,0BAA0B;YACvE,mBAAmB,EAAE,gBAAgB,CAAC,mBAAmB;YACzD,QAAQ,EAAE,gBAAgB,CAAC,MAAM,CAAC,IAAI;YACtC,cAAc,EAAE,gBAAgB,CAAC,MAAM,CAAC,QAAQ;YAChD,qEAAqE;YACrE,YAAY,EAAE,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAqB;YACnE,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;YACnC,eAAe,EAAE,QAAQ;YACzB,0BAA0B,EAAE,gBAAgB,CAAC,0BAA0B;YACvE,4BAA4B,EAC1B,gBAAgB,CAAC,4BAA4B,IAAI,KAAK;YACxD,WAAW,EACT,gBAAgB,CAAC,WAAW,KAAK,SAAS;gBACxC,CAAC,CAAC,IAAA,sBAAe,EAAC,gBAAgB,CAAC,WAAW,CAAC;gBAC/C,CAAC,CAAC,SAAS;YACf,UAAU;YACV,aAAa,EACX,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;YAC9D,sBAAsB,EACpB,gBAAgB,CAAC,sBAAsB,IAAI,KAAK;YAClD,aAAa,EAAE,gBAAgB,CAAC,aAAa,IAAI,KAAK;SACvD,EACD;YACE,OAAO,EAAE,gBAAgB,CAAC,cAAc;SACzC,EACD,SAAS,CACV,CAAC;KACH;SAAM;QACL,MAAM,YAAY,GAAG,cAAc,CAGjC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAC5B,MAAM,aAAa,GAAG,aAAkC,CAAC;QAEzD,eAAe,GAAG,IAAI,YAAY,CAChC,aAAa,CAAC,GAAI,EAClB,WAAW,EACX,aAAa,CAAC,WAAW,EACzB,aAAa,CAAC,OAAO,CACtB,CAAC;KACH;IAED,IAAI,eAAe,GAAG,eAAe,CAAC;IAEtC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,eAAe,GAAG,MAAM,QAAQ,CAAC,eAAe,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;KACxE;IAED,eAAe,GAAG,qBAAqB,CACrC,eAAe,EACf,aAAa,EACb,SAAS,CACV,CAAC;IAEF,MAAM,qCAAqC,GAAG,cAAc,CAG1D,2BAA2B,EAAE,uCAAuC,CAAC,CAAC;IAExE,OAAO,IAAI,qCAAqC,CAAC,eAAe,CAAC,CAAC;AACpE,CAAC;AA1GD,wCA0GC;AAED,SAAgB,qBAAqB,CACnC,QAAyB,EACzB,SAAiC,EACjC,SAA6B;IAE7B,kEAAkE;IAClE,MAAM,qBAAqB,GAAG,cAAc,CAG1C,YAAY,EAAE,uBAAuB,CAAC,CAAC;IACzC,MAAM,gBAAgB,GAAG,cAAc,CAGrC,YAAY,EAAE,kBAAkB,CAAC,CAAC;IACpC,MAAM,mBAAmB,GAAG,cAAc,CAGxC,YAAY,EAAE,qBAAqB,CAAC,CAAC;IACvC,MAAM,uBAAuB,GAAG,cAAc,CAG5C,YAAY,EAAE,yBAAyB,CAAC,CAAC;IAE3C,MAAM,oBAAoB,GAAG,cAAc,CAGzC,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;IAC7C,MAAM,gBAAgB,GAAG,cAAc,CAGrC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;IACzC,MAAM,yBAAyB,GAAG,cAAc,CAG9C,iBAAiB,EAAE,2BAA2B,CAAC,CAAC;IAClD,MAAM,qBAAqB,GAAG,cAAc,CAG1C,iBAAiB,EAAE,uBAAuB,CAAC,CAAC;IAC9C,MAAM,wBAAwB,GAAG,cAAc,CAG7C,WAAW,EAAE,0BAA0B,CAAC,CAAC;IAE3C,IAAI,2BAA2B,CAAC,SAAS,CAAC,EAAE;QAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;QAEpC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,QAAQ,GAAG,IAAI,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC1D;aAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YACvC,QAAQ,GAAG,IAAI,gBAAgB,CAC7B,QAAQ,EACR,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,YAAY,EACrB,QAAQ,CAAC,KAAK,EACd,QAAQ,CAAC,UAAU,CACpB,CAAC;SACH;QAED,8DAA8D;KAC/D;IAED,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,EAAE;QAChC,QAAQ,GAAG,IAAI,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;KAC9D;SAAM;QACL,QAAQ,GAAG,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC;KAClD;IAED,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,IAAI,SAAS,CAAC,GAAG,KAAK,MAAM,EAAE;QAC3D,QAAQ,GAAG,IAAI,oBAAoB,CAAC,QAAQ,EAAE,SAAS,CAAC,aAAa,CAAC,CAAC;KACxE;SAAM;QACL,QAAQ,GAAG,IAAI,gBAAgB,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;KAC1D;IAED,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,IAAI,SAAS,CAAC,QAAQ,KAAK,MAAM,EAAE;QACrE,4EAA4E;QAC5E,0EAA0E;QAC1E,gEAAgE;QAChE,EAAE;QACF,2EAA2E;QAC3E,2EAA2E;QAC3E,sDAAsD;QACtD,EAAE;QACF,yEAAyE;QACzE,4EAA4E;QAC5E,2EAA2E;QAC3E,qEAAqE;QACrE,IAAI,2BAA2B,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAClE,QAAQ,GAAG,IAAI,yBAAyB,CAAC,QAAQ,CAAC,CAAC;SACpD;KACF;SAAM;QACL,QAAQ,GAAG,IAAI,qBAAqB,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;KACpE;IAED,IACE,2BAA2B,CAAC,SAAS,CAAC;QACtC,SAAS,CAAC,OAAO,KAAK,SAAS,EAC/B;QACA,QAAQ,GAAG,IAAI,wBAAwB,CAAC,QAAQ,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;KACtE;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAvGD,sDAuGC"}