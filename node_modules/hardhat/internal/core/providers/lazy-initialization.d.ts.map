{"version": 3, "file": "lazy-initialization.d.ts", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/lazy-initialization.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EACL,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,gBAAgB,EACjB,MAAM,gBAAgB,CAAC;AAIxB,MAAM,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAC9D,MAAM,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;AAEhD;;;GAGG;AACH,qBAAa,iCAAkC,YAAW,gBAAgB;IAK5D,OAAO,CAAC,gBAAgB;IAJpC,SAAS,CAAC,QAAQ,EAAE,gBAAgB,GAAG,SAAS,CAAC;IACjD,OAAO,CAAC,QAAQ,CAAoC;IACpD,OAAO,CAAC,oBAAoB,CAAwC;gBAEhD,gBAAgB,EAAE,eAAe;IAErD;;;;OAIG;IACH,IAAW,QAAQ,IAAI,YAAY,CAKlC;IAEY,IAAI,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAyBjC,OAAO,CAAC,IAAI,EAAE,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC;IAKjD,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC;IAKxD,SAAS,CACd,OAAO,EAAE,cAAc,EACvB,QAAQ,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,eAAe,KAAK,IAAI,GACxD,IAAI;IAaA,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,EAAE,aAAa,GAAG,IAAI;IAKlE,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,EAAE,aAAa,GAAG,IAAI;IAKzD,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAKtD,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAKjE,mBAAmB,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAKrE,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAKhE,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAKrD,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,IAAI;IAK7D,eAAe,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI;IAKhC,eAAe,IAAI,MAAM;IAMzB,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,EAAE;IAM7C,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,EAAE;IAIhD,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO;IAIrD,UAAU,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IAIpC,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM;IAInD,OAAO,CAAC,WAAW;YAIL,kBAAkB;CAajC"}