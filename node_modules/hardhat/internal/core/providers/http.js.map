{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/http.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,mCAAsC;AAGtC,+CAGyB;AACzB,gDAM4B;AAC5B,wDAA2D;AAC3D,sCAAyC;AACzC,gDAAwC;AACxC,4CAAkD;AAElD,qCAAyC;AAEzC,SAAgB,eAAe,CAC7B,QAAa;IAEb,OAAO,OAAO,QAAQ,CAAC,KAAK,KAAK,WAAW,CAAC;AAC/C,CAAC;AAJD,0CAIC;AAED,MAAM,WAAW,GAAG,CAAC,CAAC;AACtB,MAAM,uBAAuB,GAAG,CAAC,CAAC;AAElC,MAAM,uBAAuB,GAAG,GAAG,CAAC;AAEpC,MAAM,cAAc,GAAG,IAAA,+BAAiB,GAAE,CAAC;AAE3C,MAAa,YAAa,SAAQ,qBAAY;IAM5C,YACmB,IAAY,EACZ,YAAoB,EACpB,gBAA4C,EAAE,EAC9C,WAAW,KAAK,EACjC,SAAwC,SAAS;QAEjD,KAAK,EAAE,CAAC;QANS,SAAI,GAAJ,IAAI,CAAQ;QACZ,iBAAY,GAAZ,YAAY,CAAQ;QACpB,kBAAa,GAAb,aAAa,CAAiC;QAC9C,aAAQ,GAAR,QAAQ,CAAQ;QAT3B,mBAAc,GAAG,CAAC,CAAC;QAczB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAkB,CAAC;QAEhE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,SAAS,EAAE;gBAC/C,KAAK,EAAE,IAAI,CAAC,IAAI;aACjB,CAAC,CAAC;SACJ;QAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC;QAC1B,IAAI,CAAC,WAAW;YACd,GAAG,CAAC,QAAQ,KAAK,EAAE;gBACjB,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,SAAS,MAAM,CAAC,IAAI,CAClB,GAAG,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,EAAE,EACjC,OAAO,CACR,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7B,IAAI;YACF,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAElD,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,SAAS,IAAI,IAAA,sBAAc,EAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACtE,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;aAC3D;SACF;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,YAAY,SAAS,IAAI,CAAC,CAAC,OAAO,KAAK,aAAa,EAAE;gBACzD,CAAC,CAAC,OAAO,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;aAC/B;YACD,sFAAsF;YACtF,MAAM,CAAC,CAAC;SACT;IACH,CAAC;IAED,IAAW,GAAG;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,sEAAsE;QACtE,yEAAyE;QACzE,IAAI,IAAI,CAAC,MAAM,KAAK,gCAAgC,EAAE;YACpD,MAAM,KAAK,GAAG,IAAI,sBAAa,CAC7B,uDAAuD,EACvD,CAAC,KAAK,CACP,CAAC;YACF,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAC5C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAe,CACrB,CAAC;QACF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;QAEzE,IAAI,eAAe,CAAC,eAAe,CAAC,EAAE;YACpC,mEAAmE;YACnE,IACE,8CAA8C,CAAC,IAAI,CACjD,eAAe,CAAC,KAAK,CAAC,OAAO,CAC9B,EACD;gBACA,OAAO,EAAE,CAAC;aACX;YAED,MAAM,KAAK,GAAG,IAAI,sBAAa,CAC7B,eAAe,CAAC,KAAK,CAAC,OAAO,EAC7B,eAAe,CAAC,KAAK,CAAC,IAAI,CAC3B,CAAC;YACF,KAAK,CAAC,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC;YACxC,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,uCAA2B,CAAC,CAAC;SACxC;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,YAAY,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,iDAAqC,CAAC,CAAC;SAClD;QAED,OAAO,eAAe,CAAC,MAAM,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAS,CACpB,KAA+C;QAE/C,uEAAuE;QACvE,sEAAsE;QACtE,MAAM,gBAAgB,GAAG,IAAI,sBAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,CAAC;QAEpE,qCAAqC;QACrC,MAAM,YAAY,GAA2B,EAAE,CAAC;QAEhD,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;YACnE,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACpC,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAEpE,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE;YACvC,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE;gBAC7B,MAAM,KAAK,GAAG,IAAI,sBAAa,CAC7B,QAAQ,CAAC,KAAK,CAAC,OAAO,EACtB,QAAQ,CAAC,KAAK,CAAC,IAAI,EACnB,gBAAgB,CACjB,CAAC;gBACF,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;gBACjC,sFAAsF;gBACtF,MAAM,KAAK,CAAC;aACb;SACF;QAED,gEAAgE;QAChE,MAAM,SAAS,GAAG,gBAA+C,CAAC;QAElE,mFAAmF;QACnF,MAAM,eAAe,GAAG,SAAS;aAC9B,GAAG,CACF,CAAC,QAAQ,EAAE,EAAE,CACX,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAkB,CAChE;aACA,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;aAC7C,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;QAE/B,OAAO,eAAe,CAAC;IACzB,CAAC;IAcO,KAAK,CAAC,qBAAqB,CACjC,OAA0C,EAC1C,WAAW,GAAG,CAAC;QAEf,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,wDAAa,QAAQ,GAAC,CAAC;QACxD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE/B,MAAM,OAAO,GAA+B;YAC1C,cAAc,EAAE,kBAAkB;YAClC,YAAY,EAAE,WAAW,cAAc,EAAE;YACzC,GAAG,IAAI,CAAC,aAAa;SACtB,CAAC;QAEF,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;YAClC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC;SAC1C;QAED,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,GAAG,EAAE;gBACtC,UAAU,EAAE,IAAI,CAAC,WAAW;gBAC5B,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC7B,eAAe,EAAE,EAAE;gBACnB,cAAc,EACZ,OAAO,CAAC,GAAG,CAAC,wCAAwC,KAAK,SAAS;oBAChE,CAAC,CAAC,CAAC;oBACH,CAAC,CAAC,IAAI,CAAC,QAAQ;gBACnB,OAAO;aACR,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE;gBACvC,uEAAuE;gBACvE,oEAAoE;gBACpE,oEAAoE;gBACpE,+CAA+C;gBAC/C,qDAAqD;gBACrD,yDAAyD;gBACzD,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAClE,IAAI,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;oBACpE,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;iBACzD;gBAED,sFAAsF;gBACtF,MAAM,IAAI,sBAAa,CACrB,yCAAyC,GAAG,CAAC,QAAQ,EAAE,EACvD,CAAC,KAAK,CAAC,sCAAsC;iBAC9C,CAAC;aACH;YAED,OAAO,IAAA,2BAAiB,EAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;SACtD;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE;gBACjC,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,mBAAmB,EAClC,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,EAC9B,KAAK,CACN,CAAC;aACH;YAED,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE;gBACpC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;aACnE;YAED,sFAAsF;YACtF,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,KAAK,CAAC,MAAM,CAClB,OAA0C,EAC1C,OAAe,EACf,WAAmB;QAEnB,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC;IAC9D,CAAC;IAEO,kBAAkB,CACxB,MAAc,EACd,SAAgB,EAAE;QAElB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,MAAM;YACN,MAAM;YACN,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE;SAC1B,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,WAAmB,EAAE,iBAAyB;QACjE,IAAI,WAAW,GAAG,WAAW,EAAE;YAC7B,OAAO,KAAK,CAAC;SACd;QAED,IAAI,iBAAiB,GAAG,uBAAuB,EAAE;YAC/C,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,oBAAoB,CAAC,QAAwC;QACnE,OAAO,QAAQ,CAAC,UAAU,KAAK,uBAAuB,CAAC;IACzD,CAAC;IAEO,qBAAqB,CAC3B,QAAwC,EACxC,WAAmB;QAEnB,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAE/C,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACpE,2DAA2D;YAC3D,yBAAyB;YACzB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,WAAW,EAAE,uBAAuB,CAAC,CAAC;SAC5D;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACpC,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;YACjB,OAAO,SAAS,CAAC;SAClB;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA5RD,oCA4RC"}