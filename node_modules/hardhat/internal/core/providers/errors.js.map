{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/errors.ts"], "names": [], "mappings": ";;;AACA,sCAAwC;AAExC,8FAA8F;AAC9F,EAAE;AACF,4EAA4E;AAC5E,EAAE;AACF,2EAA2E;AAC3E,4EAA4E;AAC5E,2EAA2E;AAC3E,2EAA2E;AAC3E,2EAA2E;AAC3E,+EAA+E;AAC/E,gFAAgF;AAChF,+EAA+E;AAC/E,EAAE;AACF,EAAE;AACF,gBAAgB;AAEhB,oFAAoF;AACpF,+DAA+D;AAC/D,sDAAsD;AACtD,EAAE;AACF,mBAAmB;AACnB,EAAE;AACF,8EAA8E;AAC9E,8EAA8E;AAE9E,MAAa,aAAc,SAAQ,oBAAW;IACrC,MAAM,CAAC,eAAe,CAAC,KAAU;QACtC,OAAO,CACL,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,gBAAgB,KAAK,IAAI,CACzE,CAAC;IACJ,CAAC;IAMD,YAAY,OAAe,EAAE,IAAY,EAAkB,MAAc;QACvE,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QADkC,WAAM,GAAN,MAAM,CAAQ;QAEvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC/B,CAAC;CACF;AAjBD,sCAiBC;AAED,MAAa,qBAAsB,SAAQ,aAAa;IAGtD,YAAY,OAAe,EAAE,MAAc;QACzC,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;;AAJsB,0BAAI,GAAG,CAAC,KAAK,CAAC;AAD1B,sDAAqB;AAQlC,MAAa,mBAAoB,SAAQ,aAAa;IAGpD,YAAY,OAAe,EAAE,MAAc;QACzC,KAAK,CAAC,OAAO,EAAE,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;;AAJsB,wBAAI,GAAG,CAAC,KAAK,CAAC;AAD1B,kDAAmB;AAQhC,MAAa,mBAAoB,SAAQ,aAAa;IAGpD,YAAY,OAAe,EAAE,MAAc;QACzC,KAAK,CAAC,OAAO,EAAE,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;;AAJsB,wBAAI,GAAG,CAAC,KAAK,CAAC;AAD1B,kDAAmB;AAQhC,MAAa,qBAAsB,SAAQ,aAAa;IAGtD,YAAY,OAAe,EAAE,MAAc;QACzC,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;;AAJsB,0BAAI,GAAG,CAAC,KAAK,CAAC;AAD1B,sDAAqB;AAQlC,MAAa,aAAc,SAAQ,aAAa;IAG9C,YAAY,OAAe,EAAE,MAAc;QACzC,KAAK,CAAC,OAAO,EAAE,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;;AAJsB,kBAAI,GAAG,CAAC,KAAK,CAAC;AAD1B,sCAAa;AAQ1B,MAAa,iBAAkB,SAAQ,aAAa;IAGlD,YAAY,OAAe,EAAE,MAAc;QACzC,KAAK,CAAC,OAAO,EAAE,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;;AAJsB,sBAAI,GAAG,CAAC,KAAK,CAAC;AAD1B,8CAAiB;AAQ9B,MAAa,yBAA0B,SAAQ,aAAa;IAG1D,4CAA4C;IAC5C,mCAAmC;IACnC,YAAY,WAA2B;QACrC,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,KAAK,CAAC,WAAW,EAAE,yBAAyB,CAAC,IAAI,CAAC,CAAC;SACpD;aAAM;YACL,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,yBAAyB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;SACzE;IACH,CAAC;;AAVsB,8BAAI,GAAG,CAAC,KAAK,CAAC;AAD1B,8DAAyB;AActC,MAAa,uBAAwB,SAAQ,aAAa;IAGxD,YAAY,MAAc,EAAE,MAAc;QACxC,KAAK,CACH,UAAU,MAAM,mBAAmB,EACnC,uBAAuB,CAAC,IAAI,EAC5B,MAAM,CACP,CAAC;IACJ,CAAC;;AARsB,4BAAI,GAAG,CAAC,KAAK,CAAC;AAD1B,0DAAuB;AAYpC,MAAa,oBAAqB,SAAQ,aAAa;IAGrD,YAAY,OAAe,EAAE,MAAc;QACzC,KAAK,CAAC,OAAO,EAAE,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;;AAJsB,yBAAI,GAAG,CAAC,KAAK,CAAC;AAD1B,oDAAoB"}