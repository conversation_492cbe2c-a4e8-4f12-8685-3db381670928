{"version": 3, "file": "accounts.js", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/accounts.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA2B;AAG3B,sCAAiE;AACjE,gDAAwC;AACxC,4DAIqC;AACrC,kFAGmD;AACnD,kEAAmE;AAEnE,mDAA2D;AAC3D,uCAAuD;AACvD,iCAA2C;AAC3C,uCAA4C;AAY5C,MAAa,qBAAsB,SAAQ,oCAA0B;IAGnE,YACE,QAAyB,EACzB,2BAAqC;QAErC,KAAK,CAAC,QAAQ,CAAC,CAAC;QANV,yBAAoB,GAAwB,IAAI,GAAG,EAAE,CAAC;QAQ5D,IAAI,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,MAAM,EACJ,MAAM,EACN,mBAAmB,EACnB,QAAQ,EACR,OAAO,EACP,UAAU,EAAE,WAAW,GACxB,GAAG,wDAAa,kBAAkB,GAAC,CAAC;QACrC,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,6BAA6B,GAAC,CAAC;QAElE,IACE,IAAI,CAAC,MAAM,KAAK,cAAc;YAC9B,IAAI,CAAC,MAAM,KAAK,qBAAqB,EACrC;YACA,OAAO,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC,CAAC;SAC9C;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,IAAA,2BAAc,EAAC,MAAM,EAAE,uBAAU,EAAE,oBAAO,CAAC,CAAC;gBAEpE,IAAI,OAAO,KAAK,SAAS,EAAE;oBACzB,IAAI,IAAI,KAAK,SAAS,EAAE;wBACtB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;qBACnE;oBAED,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;oBAC1D,MAAM,WAAW,GAAG,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;oBAClD,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;iBACxD;aACF;SACF;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,EAAE;YACnC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,IAAA,2BAAc,EAAC,MAAM,EAAE,oBAAO,EAAE,uBAAU,CAAC,CAAC;gBAEpE,IAAI,IAAI,KAAK,SAAS,EAAE;oBACtB,IAAI,OAAO,KAAK,SAAS,EAAE;wBACzB,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,kCAAkC,CAClD,CAAC;qBACH;oBAED,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;oBAC1D,MAAM,WAAW,GAAG,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;oBAClD,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;iBACxD;aACF;SACF;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,sBAAsB,EAAE;YAC1C,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,IAAA,2BAAc,EAAC,MAAM,EAAE,uBAAU,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;YAElE,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;aACnE;YAED,IAAI,YAAY,GAAG,IAAI,CAAC;YACxB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,IAAI;oBACF,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBACjC;gBAAC,MAAM;oBACN,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,wCAAwC,CACxD,CAAC;iBACH;aACF;YAED,0DAA0D;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;YAChE,IAAI,UAAU,KAAK,IAAI,EAAE;gBACvB,gFAAgF;gBAChF,OAAO,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;aACnD;SACF;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9D,MAAM,CAAC,SAAS,CAAC,GAAG,IAAA,2BAAc,EAAC,MAAM,EAAE,0CAAqB,CAAC,CAAC;YAElE,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,EAAE;gBAC/B,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,gCAAgC,EAC/C,EAAE,KAAK,EAAE,KAAK,EAAE,CACjB,CAAC;aACH;YAED,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,EAAE;gBAChC,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,gCAAgC,EAC/C,EAAE,KAAK,EAAE,MAAM,EAAE,CAClB,CAAC;aACH;YAED,MAAM,WAAW,GAAG,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC;YACrD,MAAM,gBAAgB,GACpB,SAAS,CAAC,YAAY,KAAK,SAAS;gBACpC,SAAS,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAC/C,MAAM,gBAAgB,GAAG,SAAS,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAEnE,IAAI,CAAC,WAAW,IAAI,CAAC,gBAAgB,EAAE;gBACrC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;aACjE;YAED,IAAI,WAAW,IAAI,gBAAgB,EAAE;gBACnC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;aACtE;YAED,IAAI,WAAW,IAAI,gBAAgB,EAAE;gBACnC,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;aACpE;YAED,IAAI,gBAAgB,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS,EAAE;gBAC5D,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,gCAAgC,EAC/C,EAAE,KAAK,EAAE,cAAc,EAAE,CAC1B,CAAC;aACH;YAED,IAAI,gBAAgB,IAAI,SAAS,CAAC,oBAAoB,KAAK,SAAS,EAAE;gBACpE,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,gCAAgC,EAC/C,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAClC,CAAC;aACH;YAED,IAAI,SAAS,CAAC,EAAE,KAAK,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC9D,MAAM,IAAI,qBAAY,CACpB,oBAAM,CAAC,OAAO,CAAC,2CAA2C,CAC3D,CAAC;aACH;YAED,IAAI,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE;gBACjC,SAAS,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;aACxD;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,IAAK,CAAC,CAAC;YAElE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAEzC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACrD,SAAS,EACT,OAAO,EACP,UAAU,CACX,CAAC;YAEF,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACnC,MAAM,EAAE,wBAAwB;gBAChC,MAAM,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;aACtC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEO,sBAAsB,CAAC,2BAAqC;QAClE,MAAM,EACJ,UAAU,EAAE,WAAW,EACvB,OAAO,EACP,gBAAgB,GACjB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAEhC,MAAM,WAAW,GAAa,2BAA2B,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAClE,OAAO,CAAC,CAAC,CAAC,CACX,CAAC;QAEF,KAAK,MAAM,EAAE,IAAI,WAAW,EAAE;YAC5B,MAAM,OAAO,GAAW,WAAW,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACxE,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SAC5C;IACH,CAAC;IAEO,wBAAwB,CAAC,OAAe;QAC9C,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAChE,MAAM,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/D,IAAI,EAAE,KAAK,SAAS,EAAE;YACpB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE;gBACvD,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC;aAC9B,CAAC,CAAC;SACJ;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,8BAA8B,CAAC,OAAe;QACpD,IAAI;YACF,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;SAC/C;QAAC,MAAM;YACN,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,OAAe;QACrC,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,wDAAa,kBAAkB,GAAC,CAAC;QAErE,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACpD,MAAM,EAAE,yBAAyB;YACjC,MAAM,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC;SAC1C,CAAC,CAAW,CAAC;QAEd,OAAO,IAAA,gCAAmB,EAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAEO,KAAK,CAAC,qBAAqB,CACjC,kBAAyC,EACzC,OAAe,EACf,UAAkB;QAElB,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,wDAChD,kBAAkB,GACnB,CAAC;QACF,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,wDAAa,kBAAkB,GAAC,CAAC;QAE/D,MAAM,MAAM,GAAG;YACb,GAAG,kBAAkB;YACrB,QAAQ,EAAE,kBAAkB,CAAC,GAAG;SACjC,CAAC;QAEF,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE;YACrE,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAC9C,WAAW,EACT,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACpE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,EAAE,GAAG,CACrD,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;YAC1D,OAAO;gBACL,OAAO,EAAE,WAAW;gBACpB,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAC9C,KAAK;gBACL,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC;gBAC5B,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;gBACnB,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;aACpB,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CACzC,UAAU,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,UAAU,EAAE,CAAC,EACzC,IAAI,CACL,CAAC;QAEF,IAAA,+BAAsB,EACpB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC1B,yBAAyB,CAC1B,CAAC;QAEF,IAAI,WAAW,CAAC;QAChB,gEAAgE;QAChE,MAAM,UAAU,GAAG,KAAK,CAAC;QACzB,MAAM,YAAY,GAAG;YACnB,EAAE,EAAE,kBAAkB;YACtB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,IAAA,0BAAiB,EAAC,OAAO,CAAC;YACrD,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;YACzB,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,UAAU,EAAE,CAAC;YACjD,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B,CAAC;QAEF,IAAI,iBAAiB,KAAK,SAAS,EAAE;YACnC,IAAA,+BAAsB,EACpB,MAAM,CAAC,YAAY,KAAK,SAAS,EACjC,gCAAgC,CACjC,CAAC;YAEF,WAAW,GAAG,WAAW,CAAC,OAAO,CAC/B;gBACE,IAAI,EAAE,SAAS;gBACf,GAAG,YAAY;gBACf,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;gBACjD,UAAU,EAAE,UAAU,IAAI,EAAE;gBAC5B,iBAAiB,EAAE,iBAAiB,IAAI,EAAE;aAC3C,EACD,UAAU,CACX,CAAC;SACH;aAAM,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE;YAC5C,WAAW,GAAG,WAAW,CAAC,OAAO,CAC/B;gBACE,IAAI,EAAE,SAAS;gBACf,GAAG,YAAY;gBACf,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;gBACjD,UAAU,EAAE,UAAU,IAAI,EAAE;aAC7B,EACD,UAAU,CACX,CAAC;SACH;aAAM,IAAI,UAAU,KAAK,SAAS,EAAE;YACnC,WAAW,GAAG,WAAW,CAAC,OAAO,CAC/B;gBACE,IAAI,EAAE,SAAS;gBACf,GAAG,YAAY;gBACf,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;gBAC/B,UAAU;aACX,EACD,UAAU,CACX,CAAC;SACH;aAAM;YACL,WAAW,GAAG,WAAW,CAAC,OAAO,CAC/B;gBACE,IAAI,EAAE,QAAQ;gBACd,GAAG,YAAY;gBACf,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;aAChC,EACD,UAAU,CACX,CAAC;SACH;QAED,gFAAgF;QAChF,MAAM,iBAAiB,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAEhE,OAAO,iBAAiB,CAAC,UAAU,EAAE,CAAC;IACxC,CAAC;CACF;AA3UD,sDA2UC;AAED,MAAa,gBAAiB,SAAQ,qBAAqB;IACzD,YACE,QAAyB,EACzB,QAAgB,EAChB,SAAiB,iBAAiB,EAClC,eAAuB,CAAC,EACxB,QAAgB,EAAE,EAClB,aAAqB,EAAE;QAEvB,sFAAsF;QACtF,gFAAgF;QAChF,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxC,MAAM,WAAW,GAAG,IAAA,wBAAiB,EACnC,eAAe,EACf,MAAM,EACN,YAAY,EACZ,KAAK,EACL,UAAU,CACX,CAAC;QAEF,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;IACpC,CAAC;CACF;AAxBD,4CAwBC;AAED,MAAe,cAAe,SAAQ,yBAAe;IAC5C,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAErC,IACE,MAAM,KAAK,qBAAqB;YAChC,MAAM,KAAK,UAAU;YACrB,MAAM,KAAK,iBAAiB,EAC5B;YACA,sCAAsC;YACtC,MAAM,EAAE,GAA2B,MAAM,CAAC,CAAC,CAAC,CAAC;YAE7C,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC7C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBAE9C,IAAI,aAAa,KAAK,SAAS,EAAE;oBAC/B,EAAE,CAAC,IAAI,GAAG,aAAa,CAAC;iBACzB;qBAAM,IAAI,MAAM,KAAK,qBAAqB,EAAE;oBAC3C,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;iBACpE;aACF;SACF;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;CAGF;AAED,MAAa,uBAAwB,SAAQ,cAAc;IAG/C,KAAK,CAAC,UAAU;QACxB,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;YACpC,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACpD,MAAM,EAAE,cAAc;aACvB,CAAC,CAAa,CAAC;YAEhB,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;SAClC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;CACF;AAdD,0DAcC;AAED,MAAa,mBAAoB,SAAQ,cAAc;IACrD,YAAY,QAAyB,EAAmB,OAAe;QACrE,KAAK,CAAC,QAAQ,CAAC,CAAC;QADsC,YAAO,GAAP,OAAO,CAAQ;IAEvE,CAAC;IAES,KAAK,CAAC,UAAU;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;CACF;AARD,kDAQC"}