{"version": 3, "file": "chainId.js", "sourceRoot": "", "sources": ["../../../src/internal/core/providers/chainId.ts"], "names": [], "mappings": ";;;AACA,sCAAyC;AACzC,gDAAwC;AACxC,4DAAkE;AAElE,uCAA4C;AAE5C,MAAsB,0BAA2B,SAAQ,yBAAe;IAG5D,KAAK,CAAC,WAAW;QACzB,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC/B,IAAI;gBACF,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;aACxD;YAAC,MAAM;gBACN,iDAAiD;gBACjD,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;aAC3D;SACF;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACrC,MAAM,EAAE,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAC9C,MAAM,EAAE,aAAa;SACtB,CAAC,CAAW,CAAC;QAEd,OAAO,IAAA,gCAAmB,EAAC,EAAE,CAAC,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,4BAA4B;QACxC,MAAM,EAAE,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAC9C,MAAM,EAAE,aAAa;SACtB,CAAC,CAAW,CAAC;QAEd,sFAAsF;QACtF,wFAAwF;QACxF,8EAA8E;QAC9E,OAAO,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAA,gCAAmB,EAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC1E,CAAC;CACF;AAlCD,gEAkCC;AAED,MAAa,wBAAyB,SAAQ,0BAA0B;IAGtE,YACE,QAAyB,EACR,gBAAwB;QAEzC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAFC,qBAAgB,GAAhB,gBAAgB,CAAQ;QAJnC,sBAAiB,GAAG,KAAK,CAAC;IAOlC,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,IAAsB;QACzC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAC/C,IAAI,aAAa,KAAK,IAAI,CAAC,gBAAgB,EAAE;gBAC3C,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE;oBAC7D,aAAa,EAAE,IAAI,CAAC,gBAAgB;oBACpC,iBAAiB,EAAE,aAAa;iBACjC,CAAC,CAAC;aACJ;YAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAC/B;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;CACF;AAzBD,4DAyBC"}