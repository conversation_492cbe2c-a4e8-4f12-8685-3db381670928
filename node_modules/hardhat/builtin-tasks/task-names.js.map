{"version": 3, "file": "task-names.js", "sourceRoot": "", "sources": ["../src/builtin-tasks/task-names.ts"], "names": [], "mappings": ";;;;AAAa,QAAA,UAAU,GAAG,OAAO,CAAC;AAErB,QAAA,UAAU,GAAG,OAAO,CAAC;AACrB,QAAA,iBAAiB,GAAG,cAAc,CAAC;AAEnC,QAAA,YAAY,GAAG,SAAS,CAAC;AACzB,QAAA,kCAAkC,GAC7C,+BAA+B,CAAC;AACrB,QAAA,qBAAqB,GAAG,kBAAkB,CAAC;AAC3C,QAAA,sCAAsC,GACjD,mCAAmC,CAAC;AACzB,QAAA,sCAAsC,GACjD,mCAAmC,CAAC;AACzB,QAAA,+BAA+B,GAAG,4BAA4B,CAAC;AAC/D,QAAA,kCAAkC,GAC7C,wCAAwC,CAAC;AAC9B,QAAA,2BAA2B,GAAG,iCAAiC,CAAC;AAChE,QAAA,0CAA0C,GACrD,uCAAuC,CAAC;AAC7B,QAAA,0CAA0C,GACrD,uCAAuC,CAAC;AAC7B,QAAA,kDAAkD,GAC7D,+CAA+C,CAAC;AACrC,QAAA,6CAA6C,GACxD,0CAA0C,CAAC;AAChC,QAAA,4CAA4C,GACvD,yCAAyC,CAAC;AAC/B,QAAA,4CAA4C,GACvD,yCAAyC,CAAC;AAC/B,QAAA,iCAAiC,GAAG,8BAA8B,CAAC;AACnE,QAAA,4CAA4C,GACvD,yCAAyC,CAAC;AAC/B,QAAA,0CAA0C,GACrD,uCAAuC,CAAC;AAC7B,QAAA,kCAAkC,GAC7C,+BAA+B,CAAC;AACrB,QAAA,wCAAwC,GACnD,qCAAqC,CAAC;AAC3B,QAAA,6BAA6B,GAAG,0BAA0B,CAAC;AAC3D,QAAA,kCAAkC,GAC7C,+BAA+B,CAAC;AACrB,QAAA,oCAAoC,GAC/C,iCAAiC,CAAC;AACvB,QAAA,iDAAiD,GAC5D,8CAA8C,CAAC;AACpC,QAAA,+CAA+C,GAC1D,4CAA4C,CAAC;AAClC,QAAA,gCAAgC,GAAG,6BAA6B,CAAC;AACjE,QAAA,8BAA8B,GAAG,2BAA2B,CAAC;AAC7D,QAAA,kCAAkC,GAC7C,+BAA+B,CAAC;AACrB,QAAA,4CAA4C,GACvD,yCAAyC,CAAC;AAC/B,QAAA,oCAAoC,GAC/C,iCAAiC,CAAC;AACvB,QAAA,0DAA0D,GACrE,uDAAuD,CAAC;AAC7C,QAAA,sDAAsD,GACjE,mDAAmD,CAAC;AACzC,QAAA,0DAA0D,GACrE,uDAAuD,CAAC;AAC7C,QAAA,4CAA4C,GACvD,yCAAyC,CAAC;AAC/B,QAAA,sCAAsC,GACjD,mCAAmC,CAAC;AAEzB,QAAA,YAAY,GAAG,SAAS,CAAC;AAEzB,QAAA,YAAY,GAAG,SAAS,CAAC;AACzB,QAAA,iCAAiC,GAC5C,+BAA+B,CAAC;AACrB,QAAA,8CAA8C,GACzD,4CAA4C,CAAC;AAClC,QAAA,iCAAiC,GAAG,8BAA8B,CAAC;AAEnE,QAAA,SAAS,GAAG,MAAM,CAAC;AAEnB,QAAA,QAAQ,GAAG,KAAK,CAAC;AAEjB,QAAA,SAAS,GAAG,MAAM,CAAC;AACnB,QAAA,sBAAsB,GAAG,mBAAmB,CAAC;AAC7C,QAAA,uBAAuB,GAAG,oBAAoB,CAAC;AAC/C,QAAA,wBAAwB,GAAG,qBAAqB,CAAC;AACjD,QAAA,sBAAsB,GAAG,mBAAmB,CAAC;AAE7C,QAAA,SAAS,GAAG,MAAM,CAAC;AAEnB,QAAA,uCAAuC,GAClD,gCAAgC,CAAC;AACtB,QAAA,yBAAyB,GAAG,sBAAsB,CAAC;AACnD,QAAA,wBAAwB,GAAG,qBAAqB,CAAC;AACjD,QAAA,gCAAgC,GAAG,6BAA6B,CAAC"}