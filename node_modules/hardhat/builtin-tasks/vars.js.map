{"version": 3, "file": "vars.js", "sourceRoot": "", "sources": ["../src/builtin-tasks/vars.ts"], "names": [], "mappings": ";;AAAA,oDAAuD;AACvD,mEAA2D;AAC3D,8DAAsD;AAEtD,MAAM,SAAS,GAAG,IAAA,kBAAK,EAAC,MAAM,EAAE,qCAAqC,CAAC,CAAC;AAEvE,SAAS;KACN,IAAI,CAAC,KAAK,EAAE,2CAA2C,CAAC;KACxD,kBAAkB,CAAC,KAAK,EAAE,0BAA0B,CAAC;KACrD,0BAA0B,CACzB,OAAO,EACP,iDAAiD,CAClD;KACA,SAAS,CAAC,KAAK,IAAI,EAAE;IACpB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC;AAEL,SAAS;KACN,IAAI,CAAC,KAAK,EAAE,2CAA2C,CAAC;KACxD,kBAAkB,CAAC,KAAK,EAAE,0BAA0B,CAAC;KACrD,SAAS,CAAC,KAAK,IAAI,EAAE;IACpB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC;AAEL,SAAS;KACN,IAAI,CAAC,MAAM,EAAE,sCAAsC,CAAC;KACpD,SAAS,CAAC,KAAK,IAAI,EAAE;IACpB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC;AAEL,SAAS;KACN,IAAI,CAAC,QAAQ,EAAE,iCAAiC,CAAC;KACjD,kBAAkB,CAAC,KAAK,EAAE,0BAA0B,CAAC;KACrD,SAAS,CAAC,KAAK,IAAI,EAAE;IACpB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC;AAEL,SAAS;KACN,IAAI,CACH,MAAM,EACN,4EAA4E,CAC7E;KACA,SAAS,CAAC,KAAK,IAAI,EAAE;IACpB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC;AAEL,SAAS;KACN,IAAI,CACH,OAAO,EACP,oEAAoE,CACrE;KACA,SAAS,CAAC,KAAK,IAAI,EAAE;IACpB,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC"}