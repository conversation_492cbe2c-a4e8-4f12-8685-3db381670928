{"version": 3, "file": "flatten.js", "sourceRoot": "", "sources": ["../src/builtin-tasks/flatten.ts"], "names": [], "mappings": ";;;;;AAAA,4DAAoC;AACpC,qCAAwC;AACxC,mEAA0E;AAC1E,oDAAuD;AACvD,8DAAsD;AAGtD,8DAA8D;AAE9D,wDAA4D;AAC5D,6CAQsB;AAStB,8FAA8F;AAC9F,MAAM,mBAAmB,GACvB,kEAAkE,CAAC;AACrE,2GAA2G;AAC3G,MAAM,uBAAuB,GAC3B,oFAAoF,CAAC;AAEvF,SAAS,cAAc,CAAC,iBAAkC;IACxD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/B,MAAM,KAAK,GAAG,KAAK,EAAE,CAAC;IAEtB,2DAA2D;IAC3D,MAAM,YAAY,GAAG,iBAAiB;SACnC,OAAO,EAAE;SACT,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;IAEhE,MAAM,QAAQ,GAAqB,EAAE,CAAC;IACtC,MAAM,aAAa,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAEhE,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAE3D,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,YAAY,EAAE;QACvC,0DAA0D;QAC1D,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACzC,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CACzC,CAAC;QAEF,KAAK,MAAM,EAAE,IAAI,UAAU,EAAE;YAC3B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;SAC3C;KACF;IAED,IAAI;QACF,MAAM,sBAAsB,GAAa,KAAK,CAAC,IAAI,EAAE,CAAC;QAEtD,yEAAyE;QACzE,oCAAoC;QACpC,MAAM,WAAW,GAAG,sBAAsB,CAAC,MAAM,CAC/C,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CACvC,CAAC;QAEF,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;QAC9C,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5C;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,KAAK,EAAE;YAC1B,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uCAAuC,CAAC,EAAE;gBACtE,MAAM,IAAI,qBAAY,CAAC,oBAAM,CAAC,aAAa,CAAC,aAAa,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;aACvE;SACF;QAED,sFAAsF;QACtF,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAED,SAAS,qBAAqB,CAAC,YAA0B;IACvD,MAAM,qBAAqB,GAAG,gCAAgC,CAAC;IAE/D,OAAO,YAAY,CAAC,OAAO,CAAC,UAAU;SACnC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;SAClC,IAAI,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,eAAe,CAAC,WAA2B;IAClD,MAAM,QAAQ,GAAgB,IAAI,GAAG,EAAE,CAAC;IACxC,MAAM,oBAAoB,GAAgB,IAAI,GAAG,EAAE,CAAC;IAEpD,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;QAC9B,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAE3E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,SAAS;SACV;QAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACzB;KACF;IAED,sBAAsB;IACtB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAChF,CAAC;AAED,SAAS,iBAAiB,CAAC,QAAkB;IAC3C,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC;QACzB,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,mCAAmC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;AAClE,CAAC;AAED,SAAS,uBAAuB,CAAC,GAAW;IAC1C,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACzC,CAAC;AAED,SAAS,8BAA8B,CACrC,WAA2B;IAE3B,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,MAAM,sBAAsB,GAAG;QAC7B,oBAAoB;QACpB,kCAAkC;QAClC,oBAAoB;KACrB,CAAC;IACF,MAAM,4BAA4B,GAAgB,IAAI,GAAG,EAAE,CAAC;IAC5D,MAAM,+BAA+B,GAA4B,EAAE,CAAC,CAAC,kFAAkF;IAEvJ,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;QAC9B,MAAM,OAAO,GAAG;YACd,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,uBAAuB,CAAC;SAC7D,CAAC;QAEF,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,4BAA4B,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,SAAS;SACV;QAED,IAAI,0BAA0B,GAAG,EAAE,CAAC;QACpC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5D,iEAAiE;YACjE,IACE,sBAAsB,CAAC,OAAO,CAAC,gBAAgB,CAAC;gBAChD,sBAAsB,CAAC,OAAO,CAAC,SAAS,CAAC,EACzC;gBACA,SAAS,GAAG,gBAAgB,CAAC;aAC9B;YAED,kEAAkE;YAClE,IACE,sBAAsB,CAAC,OAAO,CAAC,gBAAgB,CAAC;gBAChD,sBAAsB,CAAC,OAAO,CAAC,0BAA0B,CAAC,EAC1D;gBACA,0BAA0B,GAAG,gBAAgB,CAAC;aAC/C;SACF;QAED,qEAAqE;QACrE,+BAA+B,CAAC,IAAI,CAAC;YACnC,IAAI,CAAC,UAAU;YACf,0BAA0B;SAC3B,CAAC,CAAC;KACJ;IAED,qGAAqG;IACrG,0CAA0C;IAC1C,MAAM,kCAAkC,GAAG,+BAA+B;SACvE,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,EAAE,CAAC,aAAa,KAAK,SAAS,CAAC;SAC1D,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC;IAEjC,sBAAsB;IACtB,OAAO;QACL,SAAS;QACT,KAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,IAAI,EAAE;QAC/C,kCAAkC,CAAC,IAAI,EAAE;KAC1C,CAAC;AACJ,CAAC;AAED,SAAS,gCAAgC,CAAC,eAAuB;IAC/D,OAAO,eAAe,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,eAAe,GAAG,CAAC;AACjE,CAAC;AAED,SAAS,eAAe,CAAC,IAAY;IACnC,OAAO,IAAI,CAAC,UAAU,CACpB,mBAAmB,EACnB,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,iDAAiD,MAAM,CAAC,CAAC,CAAC,EAAE,CAC5E,CAAC;AACJ,CAAC;AAED,SAAS,+BAA+B,CAAC,IAAY;IACnD,OAAO,IAAI,CAAC,UAAU,CAAC,uBAAuB,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE;QAC5D,OAAO,iCAAiC,uBAAuB,CAC7D,MAAM,CAAC,CAAC,CAAC,CACV,EAAE,CAAC;IACN,CAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAA,oBAAO,EACL,2DAA8C,EAC9C,wHAAwH,CACzH;KACE,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KAC1D,SAAS,CACR,KAAK,EACH,EAAE,KAAK,EAAwB,EAC/B,EAAE,GAAG,EAAE,EACoC,EAAE;IAC7C,MAAM,eAAe,GAAoB,MAAM,GAAG,CAChD,8CAAiC,EACjC,EAAE,KAAK,EAAE,CACV,CAAC;IAEF,IAAI,SAAS,GAAG,EAAE,CAAC;IAEnB,IAAI,eAAe,CAAC,gBAAgB,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;QACnD,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;KAC1B;IAED,MAAM,WAAW,GAAG,MAAM,IAAA,4BAAc,GAAE,CAAC;IAC3C,SAAS,IAAI,sCAAsC,WAAW,CAAC,OAAO,sBAAsB,CAAC;IAE7F,MAAM,WAAW,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;IAEpD,MAAM,CAAC,QAAQ,EAAE,oBAAoB,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;IACtE,MAAM,CACJ,eAAe,EACf,4BAA4B,EAC5B,kCAAkC,EACnC,GAAG,8BAA8B,CAAC,WAAW,CAAC,CAAC;IAEhD,SAAS,IAAI,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACzC,SAAS,IAAI,gCAAgC,CAAC,eAAe,CAAC,CAAC;IAE/D,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;QAC9B,IAAI,OAAO,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC1C,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,GAAG,+BAA+B,CAAC,OAAO,CAAC,CAAC;QAEnD,SAAS,IAAI,eAAe,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC;QACxD,SAAS,IAAI,KAAK,OAAO,IAAI,CAAC;KAC/B;IAED,OAAO;QACL,SAAS,CAAC,IAAI,EAAE;QAChB;YACE,oBAAoB;YACpB,eAAe;YACf,4BAA4B;YAC5B,kCAAkC;SACnC;KACF,CAAC;AACJ,CAAC,CACF,CAAC;AAEJ,iEAAiE;AACjE,IAAA,oBAAO,EACL,8CAAiC,EACjC,wDAAwD,CACzD;KACE,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KAC1D,SAAS,CAAC,KAAK,EAAE,EAAE,KAAK,EAAwB,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;IAC5D,OAAO,CACL,MAAM,GAAG,CAAC,2DAA8C,EAAE,EAAE,KAAK,EAAE,CAAC,CACrE,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC;AAEL,IAAA,oBAAO,EAAC,8CAAiC,CAAC;KACvC,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAK,CAAC,GAAG,CAAC;KAC1D,SAAS,CAAC,KAAK,EAAE,EAAE,KAAK,EAAmC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;IACvE,MAAM,WAAW,GACf,KAAK,KAAK,SAAS;QACjB,CAAC,CAAC,MAAM,GAAG,CAAC,mDAAsC,CAAC;QACnD,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,0BAAe,EAAC,CAAC,CAAC,CAAC,CAAC;IAE3C,MAAM,WAAW,GAAa,MAAM,GAAG,CACrC,mDAAsC,EACtC;QACE,WAAW;KACZ,CACF,CAAC;IAEF,MAAM,eAAe,GAAoB,MAAM,GAAG,CAChD,uDAA0C,EAC1C,EAAE,WAAW,EAAE,CAChB,CAAC;IAEF,OAAO,eAAe,CAAC;AACzB,CAAC,CAAC,CAAC;AAEL,IAAA,iBAAI,EACF,yBAAY,EACZ,iIAAiI,CAClI;KACE,kCAAkC,CACjC,OAAO,EACP,sBAAsB,EACtB,SAAS,EACT,kBAAK,CAAC,SAAS,CAChB;KACA,gBAAgB,CACf,QAAQ,EACR,oDAAoD,EACpD,SAAS,EACT,kBAAK,CAAC,MAAM,CACb;KACA,SAAS,CACR,KAAK,EACH,EACE,KAAK,EACL,MAAM,GACsD,EAC9D,EAAE,GAAG,EAAE,EACP,EAAE;IACF,MAAM,CAAC,aAAa,EAAE,QAAQ,CAAC,GAC7B,MAAM,GAAG,CAAC,2DAA8C,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;IAEvE,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,IAAA,uBAAa,EAAC,MAAM,EAAE,aAAa,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;KAC7D;SAAM;QACL,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;KAC5B;IACD,IAAI,QAAQ,KAAK,IAAI;QAAE,OAAO;IAE9B,IAAI,QAAQ,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5C,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CACf,yDAAyD,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CACzF,IAAI,CACL,EAAE,CACJ,CACF,CAAC;KACH;IAED,IACE,QAAQ,CAAC,eAAe,KAAK,EAAE;QAC/B,QAAQ,CAAC,4BAA4B,CAAC,MAAM,GAAG,CAAC,EAChD;QACA,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CACf,2GAA2G,QAAQ,CAAC,4BAA4B,CAAC,IAAI,CACnJ,IAAI,CACL,EAAE,CACJ,CACF,CAAC;KACH;IAED,IAAI,QAAQ,CAAC,kCAAkC,CAAC,MAAM,GAAG,CAAC,EAAE;QAC1D,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CACf,gEACE,QAAQ,CAAC,eACX,iEAAiE,QAAQ,CAAC,kCAAkC,CAAC,IAAI,CAC/G,IAAI,CACL,EAAE,CACJ,CACF,CAAC;KACH;AACH,CAAC,CACF,CAAC"}