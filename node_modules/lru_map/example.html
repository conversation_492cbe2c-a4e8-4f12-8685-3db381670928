<!DOCTYPE HTML>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>lru</title>
    <style type="text/css">

body {
  font-family:
    -apple-system,BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    "Open Sans",
    "Helvetica Neue",
    sans-serif;
  letter-spacing:0;
  font-weight:400;
  font-style:normal;
  text-rendering:optimizeLegibility;
  -moz-font-feature-settings:"liga" on;
  background: white;
  color:rgba(0,0,0,.8);
  font-size:13px;
  line-height:1.4;
  margin:2em;
}

a { text-decoration:inherit; color: inherit; }
a:hover { color:#30C2FF; }
b { font-weight: 500; }

ul { list-style:none; }
li { line-height:1.4; }

h2 { font-size: 16px; font-weight:600; }

.log {
  background: rgba(0,0,0,0.05);
  white-space: pre-line;
  display: inline-block;
}

#out {
}

    </style>
    <!-- <script src="https://unpkg.com/amdld/amdld.min.js"></script> -->
    <script src="lru.js"></script>
  </head>
  <body>

    <h2>Expected results:</h2>
    <div class="log">adam:29 &lt; john:26 &lt; angela:24
adam:29 &lt; angela:24 &lt; john:26
angela:24 &lt; john:26 &lt; zorro:141
</div>

    <h2>Actual results:</h2>
    <div class="log" id="out"></div>

    <script>
const out = document.querySelector('#out')
function log(s) { out.innerText += s + '\n' }

let c = new LRUMap(3)
c.set('adam',   29)
c.set('john',   26)
c.set('angela', 24)
log(c.toString())        // -> "adam:29 < john:26 < angela:24"
c.get('john')       // -> 26

// Now 'john' is the most recently used entry, since we just requested it
log(c.toString())        // -> "adam:29 < angela:24 < john:26"
c.set('zorro', 141) // -> {key:adam, value:29}

// Because we only have room for 3 entries, adding 'zorro' caused 'adam'
// to be removed in order to make room for the new entry
log(c.toString())        // -> "angela:24 < john:26 < zorro:141"


// With AMDLD:
// define(['lru'], function(lru) {
//   let c = new lru.LRUMap(3)
//   // ...
// })

    </script>
  </body>
</html>
