{"name": "json-stream-stringify", "description": "JSON.Stringify as a readable stream", "version": "3.1.6", "license": "MIT", "author": "Faleij <<EMAIL>> (https://github.com/faleij)", "repository": {"type": "git", "url": "git+https://github.com/Faleij/json-stream-stringify.git"}, "bugs": {"url": "https://github.com/faleij/json-stream-stringify/issues"}, "files": ["lib/**/*"], "main": "./lib/cjs/index.js", "module": "./lib/esm/index.mjs", "umd:main": "./lib/umd/index.js", "browser": "./lib/umd/index.js", "types": "./lib/types/index.d.ts", "nyc": {"sourceMap": true, "instrument": true, "reporter": ["lcov", "text"], "extension": [".ts"], "exclude": ["**/*.d.ts", "test-src/**/*"]}, "scripts": {"lint": "eslint \"src/**/*.ts\" && echo ✅ eslint passed", "build": "node --max-old-space-size=8192 node_modules/rollup/dist/bin/rollup -c rollup.config.js", "build:watch": "npm run build -- --watch", "test": "node node_modules/mocha/bin/mocha --require source-map-support/register -R spec -b \"test/*.spec.js\"", "coverage": "node node_modules/nyc/bin/nyc.js npm test"}, "devDependencies": {"@babel/cli": "^7.18.9", "@babel/core": "^7.18.9", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.18.9", "@babel/plugin-transform-typescript": "^7.18.8", "@babel/preset-env": "^7.18.9", "@babel/preset-typescript": "^7.18.6", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-node-resolve": "^13.3.0", "@types/expect.js": "^0.3.29", "@types/mocha": "^9.1.1", "@types/readable-stream": "^2.3.14", "@typescript-eslint/eslint-plugin": "^5.31.0", "core-js": "^3.24.0", "coveralls": "3.1.1", "es-check": "^7.0.0", "eslint": "^8.20.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.26.0", "expect.js": "0.3.1", "mocha": "^10.2.0", "nyc": "15.1.0", "rollup": "2.77.0", "rollup-plugin-dts": "^4.2.2", "rollup-plugin-typescript2": "^0.32.1", "source-map-support": "^0.5.21", "typescript": "^4.7.4"}, "volta": {"node": "18.20.4"}, "engines": {"node": ">=7.10.1"}, "exports": {".": {"types": "./lib/types/index.d.ts", "import": "./lib/esm/index.mjs", "require": "./lib/umd/index.js"}, "./polyfill": {"types": "./lib/types/index.d.ts", "import": "./lib/esm/index.polyfill.mjs", "require": "./lib/umd/polyfill.js"}, "./esm/polyfill": {"types": "./lib/types/index.d.ts", "import": "./lib/esm/index.polyfill.mjs", "require": "./lib/esm/index.polyfill.mjs"}, "./esm": {"types": "./lib/types/index.d.ts", "import": "./lib/esm/index.mjs", "require": "./lib/esm/index.mjs"}, "./umd/polyfill": {"types": "./lib/types/index.d.ts", "import": "./lib/umd/polyfill.js", "require": "./lib/umd/polyfill.js"}, "./umd": {"types": "./lib/types/index.d.ts", "import": "./lib/umd/index.js", "require": "./lib/umd/index.js"}, "./cjs/polyfill": {"types": "./lib/types/index.d.ts", "import": "./lib/cjs/polyfill.js", "require": "./lib/cjs/polyfill.js"}, "./cjs": {"types": "./lib/types/index.d.ts", "import": "./lib/cjs/index.js", "require": "./lib/cjs/index.js"}}}