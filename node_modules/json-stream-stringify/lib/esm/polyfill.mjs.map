{"version": 3, "file": "polyfill.mjs", "sources": ["../../src/JsonStreamStringify.ts"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\nimport { Readable } from 'stream';\n\n// eslint-disable-next-line no-control-regex, no-misleading-character-class\nconst rxEscapable = /[\\\\\"\\u0000-\\u001f\\u007f-\\u009f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n\n// table of character substitutions\nconst meta = {\n  '\\b': '\\\\b',\n  '\\t': '\\\\t',\n  '\\n': '\\\\n',\n  '\\f': '\\\\f',\n  '\\r': '\\\\r',\n  '\"': '\\\\\"',\n  '\\\\': '\\\\\\\\',\n};\n\nfunction isReadableStream(value): boolean {\n  return typeof value.read === 'function'\n      && typeof value.pause === 'function'\n      && typeof value.resume === 'function'\n      && typeof value.pipe === 'function'\n      && typeof value.once === 'function'\n      && typeof value.removeListener === 'function';\n}\n\nenum Types {\n  Array,\n  Object,\n  ReadableString,\n  ReadableObject,\n  Primitive,\n  Promise,\n}\n\nfunction getType(value): Types {\n  if (!value) return Types.Primitive;\n  if (typeof value.then === 'function') return Types.Promise;\n  if (isReadableStream(value)) return value._readableState.objectMode ? Types.ReadableObject : Types.ReadableString;\n  if (Array.isArray(value)) return Types.Array;\n  if (typeof value === 'object' || value instanceof Object) return Types.Object;\n  return Types.Primitive;\n}\n\nfunction escapeString(string) {\n  // Modified code, original code by Douglas Crockford\n  // Original: https://github.com/douglascrockford/JSON-js/blob/master/json2.js\n\n  // If the string contains no control characters, no quote characters, and no\n  // backslash characters, then we can safely slap some quotes around it.\n  // Otherwise we must also replace the offending characters with safe escape\n  // sequences.\n\n  return string.replace(rxEscapable, (a) => {\n    const c = meta[a];\n    return typeof c === 'string' ? c : `\\\\u${a.charCodeAt(0).toString(16).padStart(4, '0')}`;\n  });\n}\n\nlet primitiveToJSON: (value: any) => string;\n\nif (global?.JSON?.stringify instanceof Function) {\n  try {\n    if (JSON.stringify(global.BigInt ? global.BigInt('123') : '') !== '123') throw new Error();\n    primitiveToJSON = JSON.stringify;\n  } catch (err) {\n    // Add support for bigint for primitiveToJSON\n    // eslint-disable-next-line no-confusing-arrow\n    primitiveToJSON = (value) => typeof value === 'bigint' ? String(value) : JSON.stringify(value);\n  }\n} else {\n  primitiveToJSON = (value) => {\n    switch (typeof value) {\n      case 'string':\n        return `\"${escapeString(value)}\"`;\n      case 'number':\n        return Number.isFinite(value) ? String(value) : 'null';\n      case 'bigint':\n        return String(value);\n      case 'boolean':\n        return value ? 'true' : 'false';\n      case 'object':\n        if (!value) {\n          return 'null';\n        }\n      // eslint-disable-next-line no-fallthrough\n      default:\n        // This should never happen, I can't imagine a situation where this executes.\n        // If you find a way, please open a ticket or PR\n        throw Object.assign(new Error(`Not a primitive \"${typeof value}\".`), { value });\n    }\n  };\n}\n\n/*\nfunction quoteString(string: string) {\n  return primitiveToJSON(String(string));\n}\n*/\n\nconst cache = new Map();\nfunction quoteString(string: string) {\n  const useCache = string.length < 10_000;\n  // eslint-disable-next-line no-lonely-if\n  if (useCache && cache.has(string)) {\n    return cache.get(string);\n  }\n  const str = primitiveToJSON(String(string));\n  if (useCache) cache.set(string, str);\n  return str;\n}\n\nfunction readAsPromised(stream: Readable, size?) {\n  const value = stream.read(size);\n  if (value === null && !(stream.readableEnded || (stream as any)._readableState?.ended)) {\n    return new Promise((resolve, reject) => {\n      const endListener = () => resolve(null);\n      stream.once('end', endListener);\n      stream.once('error', reject);\n      stream.once('readable', () => {\n        stream.removeListener('end', endListener);\n        stream.removeListener('error', reject);\n        readAsPromised(stream, size).then(resolve, reject);\n      });\n    });\n  }\n  return Promise.resolve(value);\n}\n\ninterface Item {\n  read(size?: number): Promise<void> | void;\n  depth?: number;\n  value?: any;\n  indent?: string;\n  path?: (string | number)[];\n  type?: string;\n}\n\nenum ReadState {\n  Inactive = 0,\n  Reading,\n  ReadMore,\n  Consumed,\n}\n\nexport class JsonStreamStringify extends Readable {\n  item?: Item;\n  indent?: string;\n  root: Item;\n  include: string[];\n  replacer: Function;\n  visited: [] | WeakMap<any, string[]>;\n\n  constructor(\n    input: any,\n    replacer?: Function | any[] | undefined,\n    spaces?: number | string | undefined,\n    private cycle = false,\n    private bufferSize = 512,\n  ) {\n    super({ encoding: 'utf8' });\n\n    const spaceType = typeof spaces;\n    if (spaceType === 'number') {\n      this.indent = ' '.repeat(<number>spaces);\n    } else if (spaceType === 'string') {\n      this.indent = <string>spaces;\n    }\n\n    const replacerType = typeof replacer;\n    if (replacerType === 'object') {\n      this.include = replacer as string[];\n    } else if (replacerType === 'function') {\n      this.replacer = replacer as Function;\n    }\n\n    this.visited = cycle ? new WeakMap() : [];\n\n    this.root = <any>{\n      value: { '': input },\n      depth: 0,\n      indent: '',\n      path: [],\n    };\n    this.setItem(input, this.root, '');\n  }\n\n  setItem(value, parent: Item, key: string | number = '') {\n    // call toJSON where applicable\n    if (\n      value\n      && typeof value === 'object'\n      && typeof value.toJSON === 'function'\n    ) {\n      value = value.toJSON(key);\n    }\n\n    // use replacer if applicable\n    if (this.replacer) {\n      value = this.replacer.call(parent.value, key, value);\n    }\n\n    // coerece functions and symbols into undefined\n    if (value instanceof Function || typeof value === 'symbol') {\n      value = undefined;\n    }\n\n    const type = getType(value);\n    let path;\n\n    // check for circular structure\n    if (!this.cycle && type !== Types.Primitive) {\n      if ((this.visited as any[]).some((v) => v === value)) {\n        this.destroy(Object.assign(new Error('Converting circular structure to JSON'), {\n          value,\n          key,\n        }));\n        return;\n      }\n      (this.visited as any[]).push(value);\n    } else if (this.cycle && type !== Types.Primitive) {\n      path = (this.visited as WeakMap<any, string[]>).get(value);\n      if (path) {\n        this._push(`{\"$ref\":\"$${path.map((v) => `[${(Number.isInteger(v as number) ? v : escapeString(quoteString(v as string)))}]`).join('')}\"}`);\n        this.item = parent;\n        return;\n      }\n      path = parent === this.root ? [] : parent.path.concat(key);\n      (this.visited as WeakMap<any, string[]>).set(value, path);\n    }\n\n    if (type === Types.Object) {\n      this.setObjectItem(value, parent);\n    } else if (type === Types.Array) {\n      this.setArrayItem(value, parent);\n    } else if (type === Types.Primitive) {\n      if (parent !== this.root && typeof key === 'string') {\n        // (<any>parent).write(key, primitiveToJSON(value));\n        if (value === undefined) {\n          // clear prePush buffer\n          // this.prePush = '';\n        } else {\n          this._push(primitiveToJSON(value));\n        }\n        // undefined values in objects should be rejected\n      } else if (value === undefined && typeof key === 'number') {\n        // undefined values in array should be null\n        this._push('null');\n      } else if (value === undefined) {\n        // undefined values should be ignored\n      } else {\n        this._push(primitiveToJSON(value));\n      }\n      this.item = parent;\n      return;\n    } else if (type === Types.Promise) {\n      this.setPromiseItem(value, parent, key);\n    } else if (type === Types.ReadableString) {\n      this.setReadableStringItem(value, parent);\n    } else if (type === Types.ReadableObject) {\n      this.setReadableObjectItem(value, parent);\n    }\n\n    this.item.value = value;\n    this.item.depth = parent.depth + 1;\n    if (this.indent) this.item.indent = this.indent.repeat(this.item.depth);\n    this.item.path = path;\n  }\n\n  setReadableStringItem(input: Readable, parent: Item) {\n    if (input.readableEnded || (input as any)._readableState?.endEmitted) {\n      this.emit('error', new Error('Readable Stream has ended before it was serialized. All stream data have been lost'), input, parent.path);\n    } else if (input.readableFlowing || (input as any)._readableState?.flowing) {\n      input.pause();\n      this.emit('error', new Error('Readable Stream is in flowing mode, data may have been lost. Trying to pause stream.'), input, parent.path);\n    }\n    const that = this;\n    this.prePush = '\"';\n    this.item = <any>{\n      type: 'readable string',\n      async read(size: number) {\n        try {\n          const data = await readAsPromised(input, size);\n          if (data === null) {\n            that._push('\"');\n            that.item = parent;\n            that.unvisit(input);\n            return;\n          }\n          if (data) that._push(escapeString(data.toString()));\n        } catch (err) {\n          that.emit('error', err);\n          that.destroy();\n        }\n      },\n    };\n  }\n\n  setReadableObjectItem(input: Readable, parent: Item) {\n    if (input.readableEnded || (input as any)._readableState?.endEmitted) {\n      this.emit('error', new Error('Readable Stream has ended before it was serialized. All stream data have been lost'), input, parent.path);\n    } else if (input.readableFlowing || (input as any)._readableState?.flowing) {\n      input.pause();\n      this.emit('error', new Error('Readable Stream is in flowing mode, data may have been lost. Trying to pause stream.'), input, parent.path);\n    }\n    const that = this;\n    this._push('[');\n    let first = true;\n    let i = 0;\n    const item = <any>{\n      type: 'readable object',\n      async read() {\n        try {\n          let out = '';\n          const data = await readAsPromised(input);\n          if (data === null) {\n            if (i && that.indent) {\n              out += `\\n${parent.indent}`;\n            }\n            out += ']';\n            that._push(out);\n            that.item = parent;\n            that.unvisit(input);\n            return;\n          }\n          if (first) first = false;\n          else out += ',';\n          if (that.indent) out += `\\n${item.indent}`;\n          that.prePush = out;\n          that.setItem(data, item, i);\n          i += 1;\n        } catch (err) {\n          that.emit('error', err);\n          that.destroy();\n        }\n      },\n    };\n    this.item = item;\n  }\n\n  setPromiseItem(input: Promise<any>, parent: Item, key) {\n    const that = this;\n    let read = false;\n    this.item = {\n      async read() {\n        if (read) return;\n        try {\n          read = true;\n          that.setItem(await input, parent, key);\n        } catch (err) {\n          that.emit('error', err);\n          that.destroy();\n        }\n      },\n    };\n  }\n\n  setArrayItem(input: any[], parent: any) {\n    // const entries = input.slice().reverse();\n    let i = 0;\n    const len = input.length;\n    let first = true;\n    const that = this;\n    const item: Item = {\n      read() {\n        let out = '';\n        let wasFirst = false;\n        if (first) {\n          first = false;\n          wasFirst = true;\n          if (!len) {\n            that._push('[]');\n            that.unvisit(input);\n            that.item = parent;\n            return;\n          }\n          out += '[';\n        }\n        const entry = input[i];\n        if (i === len) {\n          if (that.indent) out += `\\n${parent.indent}`;\n          out += ']';\n          that._push(out);\n          that.item = parent;\n          that.unvisit(input);\n          return;\n        }\n        if (!wasFirst) out += ',';\n        if (that.indent) out += `\\n${item.indent}`;\n        that._push(out);\n        that.setItem(entry, item, i);\n        i += 1;\n      },\n    };\n    this.item = item;\n  }\n\n  unvisit(item) {\n    if (this.cycle) return;\n    const _i = (this.visited as any[]).indexOf(item);\n    if (_i > -1) (this.visited as any[]).splice(_i, 1);\n  }\n\n  objectItem?: any;\n  setObjectItem(input: Record<any, any>, parent = undefined) {\n    const keys = Object.keys(input);\n    let i = 0;\n    const len = keys.length;\n    let first = true;\n    const that = this;\n    const { include } = this;\n    let hasItems = false;\n    let key;\n    const item: Item = <any>{\n      read() {\n        if (i === 0) that._push('{');\n        if (i === len) {\n          that.objectItem = undefined;\n          if (!hasItems) {\n            that._push('}');\n          } else {\n            that._push(`${that.indent ? `\\n${parent.indent}` : ''}}`);\n          }\n          that.item = parent;\n          that.unvisit(input);\n          return;\n        }\n        key = keys[i];\n        if (include?.indexOf?.(key) === -1) {\n          // replacer array excludes this key\n          i += 1;\n          return;\n        }\n        that.objectItem = item;\n        i += 1;\n        that.setItem(input[key], item, key);\n      },\n      write() {\n        const out = `${hasItems && !first ? ',' : ''}${item.indent ? `\\n${item.indent}` : ''}${quoteString(key)}:${that.indent ? ' ' : ''}`;\n        first = false;\n        hasItems = true;\n        that.objectItem = undefined;\n        return out;\n      },\n    };\n    this.item = item;\n  }\n\n  buffer = '';\n  bufferLength = 0;\n  pushCalled = false;\n\n  readSize = 0;\n  /** if set, this string will be prepended to the next _push call, if the call output is not empty, and set to undefined */\n  prePush?: string;\n  private _push(data) {\n    const out = (this.objectItem ? this.objectItem.write() : '') + data;\n    if (this.prePush && out.length) {\n      this.buffer += this.prePush;\n      this.prePush = undefined;\n    }\n    this.buffer += out;\n    if (this.buffer.length >= this.bufferSize) {\n      this.pushCalled = !this.push(this.buffer);\n      this.buffer = '';\n      this.bufferLength = 0;\n      return false;\n    }\n    return true;\n  }\n\n  readState: ReadState = ReadState.Inactive;\n  async _read(size?: number): Promise<void> {\n    if (this.readState === ReadState.Consumed) return;\n    if (this.readState !== ReadState.Inactive) {\n      this.readState = ReadState.ReadMore;\n      return;\n    }\n    this.readState = ReadState.Reading;\n    this.pushCalled = false;\n    let p;\n    while (!this.pushCalled && this.item !== this.root && this.buffer !== undefined) {\n      p = this.item.read(size);\n      // eslint-disable-next-line no-await-in-loop\n      if (p) await p;\n    }\n    if (this.buffer === undefined) return;\n    if (this.item === this.root) {\n      if (this.buffer.length) this.push(this.buffer);\n      this.push(null);\n      this.readState = ReadState.Consumed;\n      this.cleanup();\n      return;\n    }\n    if (this.readState === <any>ReadState.ReadMore) {\n      this.readState = ReadState.Inactive;\n      await this._read(size);\n      return;\n    }\n    this.readState = ReadState.Inactive;\n  }\n\n  private cleanup() {\n    this.readState = ReadState.Consumed;\n    this.buffer = undefined;\n    this.visited = undefined;\n    this.item = undefined;\n    this.root = undefined;\n    this.prePush = undefined;\n  }\n\n  destroy(error?: Error): this {\n    if (error) this.emit('error', error);\n    super.destroy?.();\n    this.cleanup();\n    return this;\n  }\n}\n"], "names": ["rxEscapable", "meta", "isReadableStream", "value", "read", "pause", "resume", "pipe", "once", "removeListener", "Types", "getType", "Primitive", "then", "Promise", "_readableState", "objectMode", "ReadableObject", "ReadableString", "Array", "isArray", "Object", "escapeString", "string", "replace", "a", "c", "charCodeAt", "toString", "padStart", "primitiveToJSON", "global", "JSON", "stringify", "Function", "BigInt", "Error", "err", "String", "Number", "isFinite", "assign", "cache", "Map", "quoteString", "useCache", "length", "has", "get", "str", "set", "readAsPromised", "stream", "size", "readableEnded", "ended", "resolve", "reject", "endListener", "ReadState", "JsonStreamStringify", "Readable", "constructor", "input", "replacer", "spaces", "cycle", "bufferSize", "encoding", "Inactive", "spaceType", "indent", "repeat", "replacerType", "include", "visited", "WeakMap", "root", "depth", "path", "setItem", "parent", "key", "toJSON", "call", "undefined", "type", "some", "v", "destroy", "push", "_push", "map", "isInteger", "join", "item", "concat", "setObjectItem", "setArrayItem", "setPromiseItem", "setReadableStringItem", "setReadableObjectItem", "endEmitted", "emit", "readableFlowing", "flowing", "that", "prePush", "data", "unvisit", "first", "i", "out", "len", "<PERSON><PERSON><PERSON><PERSON>", "entry", "_i", "indexOf", "splice", "keys", "hasItems", "objectItem", "write", "buffer", "pushCalled", "bufferLength", "_read", "readState", "Consumed", "ReadMore", "Reading", "p", "cleanup", "error"], "mappings": ";;;;;;;;;;;;AAIA,MAAMA,WAAW,GAAG,iIAApB;;AAGA,MAAMC,IAAI,GAAG;AACX,EAAA,IAAA,EAAM,KADK;AAEX,EAAA,IAAA,EAAM,KAFK;AAGX,EAAA,IAAA,EAAM,KAHK;AAIX,EAAA,IAAA,EAAM,KAJK;AAKX,EAAA,IAAA,EAAM,KALK;AAMX,EAAA,GAAA,EAAK,KANM;EAOX,IAAM,EAAA,MAAA;AAPK,CAAb,CAAA;;AAUA,SAASC,gBAAT,CAA0BC,KAA1B,EAA+B;AAC7B,EAAA,OAAO,OAAOA,KAAK,CAACC,IAAb,KAAsB,UAAtB,IACA,OAAOD,KAAK,CAACE,KAAb,KAAuB,UADvB,IAEA,OAAOF,KAAK,CAACG,MAAb,KAAwB,UAFxB,IAGA,OAAOH,KAAK,CAACI,IAAb,KAAsB,UAHtB,IAIA,OAAOJ,KAAK,CAACK,IAAb,KAAsB,UAJtB,IAKA,OAAOL,KAAK,CAACM,cAAb,KAAgC,UALvC,CAAA;AAMD,CAAA;;AAED,IAAKC,KAAL,CAAA;;AAAA,CAAA,UAAKA,KAAL,EAAU;EACRA,KAAA,CAAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAA,CAAA;EACAA,KAAA,CAAAA,KAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAA,CAAA;EACAA,KAAA,CAAAA,KAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAA,CAAA;EACAA,KAAA,CAAAA,KAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAA,CAAA;EACAA,KAAA,CAAAA,KAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAA,CAAA;EACAA,KAAA,CAAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;AACD,CAPD,EAAKA,KAAK,KAALA,KAAK,GAOT,EAPS,CAAV,CAAA,CAAA;;AASA,SAASC,OAAT,CAAiBR,KAAjB,EAAsB;AACpB,EAAA,IAAI,CAACA,KAAL,EAAY,OAAOO,KAAK,CAACE,SAAb,CAAA;EACZ,IAAI,OAAOT,KAAK,CAACU,IAAb,KAAsB,UAA1B,EAAsC,OAAOH,KAAK,CAACI,OAAb,CAAA;AACtC,EAAA,IAAIZ,gBAAgB,CAACC,KAAD,CAApB,EAA6B,OAAOA,KAAK,CAACY,cAAN,CAAqBC,UAArB,GAAkCN,KAAK,CAACO,cAAxC,GAAyDP,KAAK,CAACQ,cAAtE,CAAA;EAC7B,IAAIC,KAAK,CAACC,OAAN,CAAcjB,KAAd,CAAJ,EAA0B,OAAOO,KAAK,CAACS,KAAb,CAAA;AAC1B,EAAA,IAAI,OAAOhB,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,YAAYkB,MAAlD,EAA0D,OAAOX,KAAK,CAACW,MAAb,CAAA;EAC1D,OAAOX,KAAK,CAACE,SAAb,CAAA;AACD,CAAA;;AAED,SAASU,YAAT,CAAsBC,MAAtB,EAA4B;AAC1B;AACA;AAEA;AACA;AACA;AACA;AAEA,EAAA,OAAOA,MAAM,CAACC,OAAP,CAAexB,WAAf,EAA6ByB,CAAD,IAAM;AACvC,IAAA,MAAMC,CAAC,GAAGzB,IAAI,CAACwB,CAAD,CAAd,CAAA;IACA,OAAO,OAAOC,CAAP,KAAa,QAAb,GAAwBA,CAAxB,GAA4B,CAAA,GAAA,EAAMD,CAAC,CAACE,UAAF,CAAa,CAAb,CAAgBC,CAAAA,QAAhB,CAAyB,EAAzB,CAA6BC,CAAAA,QAA7B,CAAsC,CAAtC,EAAyC,GAAzC,EAAzC,CAAA,CAAA;AACD,GAHM,CAAP,CAAA;AAID,CAAA;;AAED,IAAIC,eAAJ,CAAA;;AAEA,IAAI,CAAA,CAAA,OAAA,GAAAC,MAAM,MAAN,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,YAAA,GAAA,OAAA,CAAQC,IAAR,MAAcC,IAAAA,IAAAA,YAAAA,KAAAA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,YAAAA,CAAAA,SAAd,aAAmCC,QAAvC,EAAiD;EAC/C,IAAI;IACF,IAAIF,IAAI,CAACC,SAAL,CAAeF,MAAM,CAACI,MAAP,GAAgBJ,MAAM,CAACI,MAAP,CAAc,KAAd,CAAhB,GAAuC,EAAtD,CAAA,KAA8D,KAAlE,EAAyE,MAAM,IAAIC,KAAJ,EAAN,CAAA;IACzEN,eAAe,GAAGE,IAAI,CAACC,SAAvB,CAAA;GAFF,CAGE,OAAOI,GAAP,EAAY;AACZ;AACA;AACAP,IAAAA,eAAe,GAAI3B,KAAD,IAAW,OAAOA,KAAP,KAAiB,QAAjB,GAA4BmC,MAAM,CAACnC,KAAD,CAAlC,GAA4C6B,IAAI,CAACC,SAAL,CAAe9B,KAAf,CAAzE,CAAA;AACD,GAAA;AACF,CATD,MASO;EACL2B,eAAe,GAAI3B,KAAD,IAAU;AAC1B,IAAA,QAAQ,OAAOA,KAAf;AACE,MAAA,KAAK,QAAL;AACE,QAAA,QAAWmB,CAAAA,EAAAA,YAAY,CAACnB,KAAD,EAAvB,CAAA,CAAA,CAAA;;AACF,MAAA,KAAK,QAAL;QACE,OAAOoC,MAAM,CAACC,QAAP,CAAgBrC,KAAhB,CAAyBmC,GAAAA,MAAM,CAACnC,KAAD,CAA/B,GAAyC,MAAhD,CAAA;;AACF,MAAA,KAAK,QAAL;QACE,OAAOmC,MAAM,CAACnC,KAAD,CAAb,CAAA;;AACF,MAAA,KAAK,SAAL;AACE,QAAA,OAAOA,KAAK,GAAG,MAAH,GAAY,OAAxB,CAAA;;AACF,MAAA,KAAK,QAAL;QACE,IAAI,CAACA,KAAL,EAAY;AACV,UAAA,OAAO,MAAP,CAAA;AACD,SAAA;;AACH;;AACA,MAAA;AACE;AACA;AACA,QAAA,MAAMkB,MAAM,CAACoB,MAAP,CAAc,IAAIL,KAAJ,CAAU,CAAA,iBAAA,EAAoB,OAAOjC,MAArC,EAAA,CAAA,CAAd,EAA+D;AAAEA,UAAAA,KAAAA;AAAF,SAA/D,CAAN,CAAA;AAjBJ,KAAA;GADF,CAAA;AAqBD,CAAA;AAED;;;;AAIE;;;AAEF,MAAMuC,KAAK,GAAG,IAAIC,GAAJ,EAAd,CAAA;;AACA,SAASC,WAAT,CAAqBrB,MAArB,EAAmC;EACjC,MAAMsB,QAAQ,GAAGtB,MAAM,CAACuB,MAAP,GAAgB,KAAjC,CADiC;;EAGjC,IAAID,QAAQ,IAAIH,KAAK,CAACK,GAAN,CAAUxB,MAAV,CAAhB,EAAmC;AACjC,IAAA,OAAOmB,KAAK,CAACM,GAAN,CAAUzB,MAAV,CAAP,CAAA;AACD,GAAA;;EACD,MAAM0B,GAAG,GAAGnB,eAAe,CAACQ,MAAM,CAACf,MAAD,CAAP,CAA3B,CAAA;EACA,IAAIsB,QAAJ,EAAcH,KAAK,CAACQ,GAAN,CAAU3B,MAAV,EAAkB0B,GAAlB,CAAA,CAAA;AACd,EAAA,OAAOA,GAAP,CAAA;AACD,CAAA;;AAED,SAASE,cAAT,CAAwBC,MAAxB,EAA0CC,IAA1C,EAA+C;AAAA,EAAA,IAAA,qBAAA,CAAA;;AAC7C,EAAA,MAAMlD,KAAK,GAAGiD,MAAM,CAAChD,IAAP,CAAYiD,IAAZ,CAAd,CAAA;;AACA,EAAA,IAAIlD,KAAK,KAAK,IAAV,IAAkB,EAAEiD,MAAM,CAACE,aAAP,IAAyBF,CAAAA,qBAAAA,GAAAA,MAAc,CAACrC,cAAxC,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAyB,qBAA+BwC,CAAAA,KAA1D,CAAtB,EAAwF;AACtF,IAAA,OAAO,IAAIzC,OAAJ,CAAY,CAAC0C,OAAD,EAAUC,MAAV,KAAoB;AACrC,MAAA,MAAMC,WAAW,GAAG,MAAMF,OAAO,CAAC,IAAD,CAAjC,CAAA;;AACAJ,MAAAA,MAAM,CAAC5C,IAAP,CAAY,KAAZ,EAAmBkD,WAAnB,CAAA,CAAA;AACAN,MAAAA,MAAM,CAAC5C,IAAP,CAAY,OAAZ,EAAqBiD,MAArB,CAAA,CAAA;AACAL,MAAAA,MAAM,CAAC5C,IAAP,CAAY,UAAZ,EAAwB,MAAK;AAC3B4C,QAAAA,MAAM,CAAC3C,cAAP,CAAsB,KAAtB,EAA6BiD,WAA7B,CAAA,CAAA;AACAN,QAAAA,MAAM,CAAC3C,cAAP,CAAsB,OAAtB,EAA+BgD,MAA/B,CAAA,CAAA;QACAN,cAAc,CAACC,MAAD,EAASC,IAAT,CAAd,CAA6BxC,IAA7B,CAAkC2C,OAAlC,EAA2CC,MAA3C,CAAA,CAAA;OAHF,CAAA,CAAA;AAKD,KATM,CAAP,CAAA;AAUD,GAAA;;AACD,EAAA,OAAO3C,OAAO,CAAC0C,OAAR,CAAgBrD,KAAhB,CAAP,CAAA;AACD,CAAA;;AAWD,IAAKwD,SAAL,CAAA;;AAAA,CAAA,UAAKA,SAAL,EAAc;EACZA,SAAA,CAAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAA,CAAA;EACAA,SAAA,CAAAA,SAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;EACAA,SAAA,CAAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAA,CAAA;EACAA,SAAA,CAAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAA,CAAA;AACD,CALD,EAAKA,SAAS,KAATA,SAAS,GAKb,EALa,CAAd,CAAA,CAAA;;AAOM,MAAOC,mBAAP,SAAmCC,QAAnC,CAA2C;AAQ/CC,EAAAA,WACE,CAAAC,KAAA,EACAC,QADA,EAEAC,MAFA,EAGQC,KAAQ,GAAA,KAHhB,EAIQC,UAAA,GAAa,GAJrB,EAIwB;IAExB,KAAM,CAAA;AAAEC,MAAAA,QAAQ,EAAE,MAAA;KAAlB,CAAA,CAAA;;AAFwB,IAAA,eAAA,CAAA,IAAA,EAAA,OAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,YAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,MAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,QAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,MAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,UAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,YAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,QAAA,EAkSjB,EAlSiB,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,cAAA,EAmSX,CAnSW,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,YAAA,EAoSb,KApSa,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,UAAA,EAsSf,CAtSe,CAAA,CAAA;;AAAA,IAAA,eAAA,CAAA,IAAA,EAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA;;IAAA,eAyTHT,CAAAA,IAAAA,EAAAA,WAAAA,EAAAA,SAAS,CAACU,QAzTP,CAAA,CAAA;;IADhB,IAAKH,CAAAA,KAAL,GAAAA,KAAA,CAAA;IACA,IAAUC,CAAAA,UAAV,GAAAA,UAAA,CAAA;IAIR,MAAMG,SAAS,GAAG,OAAOL,MAAzB,CAAA;;IACA,IAAIK,SAAS,KAAK,QAAlB,EAA4B;AAC1B,MAAA,IAAA,CAAKC,MAAL,GAAc,GAAA,CAAIC,MAAJ,CAAmBP,MAAnB,CAAd,CAAA;AACD,KAFD,MAEO,IAAIK,SAAS,KAAK,QAAlB,EAA4B;MACjC,IAAKC,CAAAA,MAAL,GAAsBN,MAAtB,CAAA;AACD,KAAA;;IAED,MAAMQ,YAAY,GAAG,OAAOT,QAA5B,CAAA;;IACA,IAAIS,YAAY,KAAK,QAArB,EAA+B;MAC7B,IAAKC,CAAAA,OAAL,GAAeV,QAAf,CAAA;AACD,KAFD,MAEO,IAAIS,YAAY,KAAK,UAArB,EAAiC;MACtC,IAAKT,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;AACD,KAAA;;IAED,IAAKW,CAAAA,OAAL,GAAeT,KAAK,GAAG,IAAIU,OAAJ,EAAH,GAAmB,EAAvC,CAAA;AAEA,IAAA,IAAA,CAAKC,IAAL,GAAiB;AACf1E,MAAAA,KAAK,EAAE;QAAE,EAAI4D,EAAAA,KAAAA;OADE;AAEfe,MAAAA,KAAK,EAAE,CAFQ;AAGfP,MAAAA,MAAM,EAAE,EAHO;AAIfQ,MAAAA,IAAI,EAAE,EAAA;KAJR,CAAA;AAMA,IAAA,IAAA,CAAKC,OAAL,CAAajB,KAAb,EAAoB,IAAKc,CAAAA,IAAzB,EAA+B,EAA/B,CAAA,CAAA;AACD,GAAA;;EAEDG,OAAO,CAAC7E,KAAD,EAAQ8E,MAAR,EAAsBC,MAAuB,EAA7C,EAA+C;AACpD;AACA,IAAA,IACE/E,KAAK,IACF,OAAOA,KAAP,KAAiB,QADpB,IAEG,OAAOA,KAAK,CAACgF,MAAb,KAAwB,UAH7B,EAIE;AACAhF,MAAAA,KAAK,GAAGA,KAAK,CAACgF,MAAN,CAAaD,GAAb,CAAR,CAAA;AACD,KARmD;;;IAWpD,IAAI,IAAA,CAAKlB,QAAT,EAAmB;AACjB7D,MAAAA,KAAK,GAAG,IAAA,CAAK6D,QAAL,CAAcoB,IAAd,CAAmBH,MAAM,CAAC9E,KAA1B,EAAiC+E,GAAjC,EAAsC/E,KAAtC,CAAR,CAAA;AACD,KAbmD;;;IAgBpD,IAAIA,KAAK,YAAY+B,QAAjB,IAA6B,OAAO/B,KAAP,KAAiB,QAAlD,EAA4D;AAC1DA,MAAAA,KAAK,GAAGkF,SAAR,CAAA;AACD,KAAA;;AAED,IAAA,MAAMC,IAAI,GAAG3E,OAAO,CAACR,KAAD,CAApB,CAAA;IACA,IAAI4E,IAAJ,CArBoD;;IAwBpD,IAAI,CAAC,KAAKb,KAAN,IAAeoB,IAAI,KAAK5E,KAAK,CAACE,SAAlC,EAA6C;MAC3C,IAAK,IAAA,CAAK+D,OAAL,CAAuBY,IAAvB,CAA6BC,CAAD,IAAOA,CAAC,KAAKrF,KAAzC,CAAL,EAAsD;QACpD,IAAKsF,CAAAA,OAAL,CAAapE,MAAM,CAACoB,MAAP,CAAc,IAAIL,KAAJ,CAAU,uCAAV,CAAd,EAAkE;UAC7EjC,KAD6E;AAE7E+E,UAAAA,GAAAA;AAF6E,SAAlE,CAAb,CAAA,CAAA;AAIA,QAAA,OAAA;AACD,OAAA;;AACA,MAAA,IAAA,CAAKP,OAAL,CAAuBe,IAAvB,CAA4BvF,KAA5B,CAAA,CAAA;KARH,MASO,IAAI,IAAA,CAAK+D,KAAL,IAAcoB,IAAI,KAAK5E,KAAK,CAACE,SAAjC,EAA4C;AACjDmE,MAAAA,IAAI,GAAI,IAAKJ,CAAAA,OAAL,CAAwC3B,GAAxC,CAA4C7C,KAA5C,CAAR,CAAA;;AACA,MAAA,IAAI4E,IAAJ,EAAU;AACR,QAAA,IAAA,CAAKY,KAAL,CAAW,CAAaZ,UAAAA,EAAAA,IAAI,CAACa,GAAL,CAAUJ,CAAD,QAAYjD,MAAM,CAACsD,SAAP,CAAiBL,CAAjB,CAAgCA,GAAAA,CAAhC,GAAoClE,YAAY,CAACsB,WAAW,CAAC4C,CAAD,CAAZ,CAA2B,CAAA,CAAA,CAAhG,CAAqGM,CAAAA,IAArG,CAA0G,EAA1G,CAAiH,CAAzI,EAAA,CAAA,CAAA,CAAA;;QACA,IAAKC,CAAAA,IAAL,GAAYd,MAAZ,CAAA;AACA,QAAA,OAAA;AACD,OAAA;;AACDF,MAAAA,IAAI,GAAGE,MAAM,KAAK,IAAA,CAAKJ,IAAhB,GAAuB,EAAvB,GAA4BI,MAAM,CAACF,IAAP,CAAYiB,MAAZ,CAAmBd,GAAnB,CAAnC,CAAA;AACC,MAAA,IAAA,CAAKP,OAAL,CAAwCzB,GAAxC,CAA4C/C,KAA5C,EAAmD4E,IAAnD,CAAA,CAAA;AACF,KAAA;;AAED,IAAA,IAAIO,IAAI,KAAK5E,KAAK,CAACW,MAAnB,EAA2B;AACzB,MAAA,IAAA,CAAK4E,aAAL,CAAmB9F,KAAnB,EAA0B8E,MAA1B,CAAA,CAAA;AACD,KAFD,MAEO,IAAIK,IAAI,KAAK5E,KAAK,CAACS,KAAnB,EAA0B;AAC/B,MAAA,IAAA,CAAK+E,YAAL,CAAkB/F,KAAlB,EAAyB8E,MAAzB,CAAA,CAAA;AACD,KAFM,MAEA,IAAIK,IAAI,KAAK5E,KAAK,CAACE,SAAnB,EAA8B;MACnC,IAAIqE,MAAM,KAAK,IAAKJ,CAAAA,IAAhB,IAAwB,OAAOK,GAAP,KAAe,QAA3C,EAAqD;AACnD;AACA,QAAA,IAAI/E,KAAK,KAAKkF,SAAd,EAAyB,CAAzB,MAGO;AACL,UAAA,IAAA,CAAKM,KAAL,CAAW7D,eAAe,CAAC3B,KAAD,CAA1B,CAAA,CAAA;AACD,SAPkD;;OAArD,MASO,IAAIA,KAAK,KAAKkF,SAAV,IAAuB,OAAOH,GAAP,KAAe,QAA1C,EAAoD;AACzD;QACA,IAAKS,CAAAA,KAAL,CAAW,MAAX,CAAA,CAAA;AACD,OAHM,MAGA,IAAIxF,KAAK,KAAKkF,SAAd,EAAyB,CAAzB,MAEA;AACL,QAAA,IAAA,CAAKM,KAAL,CAAW7D,eAAe,CAAC3B,KAAD,CAA1B,CAAA,CAAA;AACD,OAAA;;MACD,IAAK4F,CAAAA,IAAL,GAAYd,MAAZ,CAAA;AACA,MAAA,OAAA;AACD,KApBM,MAoBA,IAAIK,IAAI,KAAK5E,KAAK,CAACI,OAAnB,EAA4B;AACjC,MAAA,IAAA,CAAKqF,cAAL,CAAoBhG,KAApB,EAA2B8E,MAA3B,EAAmCC,GAAnC,CAAA,CAAA;AACD,KAFM,MAEA,IAAII,IAAI,KAAK5E,KAAK,CAACQ,cAAnB,EAAmC;AACxC,MAAA,IAAA,CAAKkF,qBAAL,CAA2BjG,KAA3B,EAAkC8E,MAAlC,CAAA,CAAA;AACD,KAFM,MAEA,IAAIK,IAAI,KAAK5E,KAAK,CAACO,cAAnB,EAAmC;AACxC,MAAA,IAAA,CAAKoF,qBAAL,CAA2BlG,KAA3B,EAAkC8E,MAAlC,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKc,IAAL,CAAU5F,KAAV,GAAkBA,KAAlB,CAAA;IACA,IAAK4F,CAAAA,IAAL,CAAUjB,KAAV,GAAkBG,MAAM,CAACH,KAAP,GAAe,CAAjC,CAAA;AACA,IAAA,IAAI,KAAKP,MAAT,EAAiB,IAAKwB,CAAAA,IAAL,CAAUxB,MAAV,GAAmB,IAAKA,CAAAA,MAAL,CAAYC,MAAZ,CAAmB,KAAKuB,IAAL,CAAUjB,KAA7B,CAAnB,CAAA;AACjB,IAAA,IAAA,CAAKiB,IAAL,CAAUhB,IAAV,GAAiBA,IAAjB,CAAA;AACD,GAAA;;AAEDqB,EAAAA,qBAAqB,CAACrC,KAAD,EAAkBkB,MAAlB,EAA8B;AAAA,IAAA,IAAA,qBAAA,EAAA,sBAAA,CAAA;;IACjD,IAAIlB,KAAK,CAACT,aAAN,IAAwBS,CAAAA,qBAAAA,GAAAA,KAAa,CAAChD,cAAtC,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAwB,qBAA8BuF,CAAAA,UAA1D,EAAsE;AACpE,MAAA,IAAA,CAAKC,IAAL,CAAU,OAAV,EAAmB,IAAInE,KAAJ,CAAU,oFAAV,CAAnB,EAAoH2B,KAApH,EAA2HkB,MAAM,CAACF,IAAlI,CAAA,CAAA;AACD,KAFD,MAEO,IAAIhB,KAAK,CAACyC,eAAN,IAAA,CAAA,sBAAA,GAA0BzC,KAAa,CAAChD,cAAxC,MAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,IAA0B,sBAA8B0F,CAAAA,OAA5D,EAAqE;AAC1E1C,MAAAA,KAAK,CAAC1D,KAAN,EAAA,CAAA;AACA,MAAA,IAAA,CAAKkG,IAAL,CAAU,OAAV,EAAmB,IAAInE,KAAJ,CAAU,sFAAV,CAAnB,EAAsH2B,KAAtH,EAA6HkB,MAAM,CAACF,IAApI,CAAA,CAAA;AACD,KAAA;;IACD,MAAM2B,IAAI,GAAG,IAAb,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAe,GAAf,CAAA;AACA,IAAA,IAAA,CAAKZ,IAAL,GAAiB;AACfT,MAAAA,IAAI,EAAE,iBADS;;MAEf,MAAMlF,IAAN,CAAWiD,IAAX,EAAuB;QACrB,IAAI;UACF,MAAMuD,IAAI,GAAG,MAAMzD,cAAc,CAACY,KAAD,EAAQV,IAAR,CAAjC,CAAA;;UACA,IAAIuD,IAAI,KAAK,IAAb,EAAmB;YACjBF,IAAI,CAACf,KAAL,CAAW,GAAX,CAAA,CAAA;;YACAe,IAAI,CAACX,IAAL,GAAYd,MAAZ,CAAA;YACAyB,IAAI,CAACG,OAAL,CAAa9C,KAAb,CAAA,CAAA;AACA,YAAA,OAAA;AACD,WAAA;;AACD,UAAA,IAAI6C,IAAJ,EAAUF,IAAI,CAACf,KAAL,CAAWrE,YAAY,CAACsF,IAAI,CAAChF,QAAL,EAAD,CAAvB,CAAA,CAAA;SARZ,CASE,OAAOS,GAAP,EAAY;AACZqE,UAAAA,IAAI,CAACH,IAAL,CAAU,OAAV,EAAmBlE,GAAnB,CAAA,CAAA;AACAqE,UAAAA,IAAI,CAACjB,OAAL,EAAA,CAAA;AACD,SAAA;AACF,OAAA;;KAhBH,CAAA;AAkBD,GAAA;;AAEDY,EAAAA,qBAAqB,CAACtC,KAAD,EAAkBkB,MAAlB,EAA8B;AAAA,IAAA,IAAA,sBAAA,EAAA,sBAAA,CAAA;;IACjD,IAAIlB,KAAK,CAACT,aAAN,IAAwBS,CAAAA,sBAAAA,GAAAA,KAAa,CAAChD,cAAtC,MAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,IAAwB,sBAA8BuF,CAAAA,UAA1D,EAAsE;AACpE,MAAA,IAAA,CAAKC,IAAL,CAAU,OAAV,EAAmB,IAAInE,KAAJ,CAAU,oFAAV,CAAnB,EAAoH2B,KAApH,EAA2HkB,MAAM,CAACF,IAAlI,CAAA,CAAA;AACD,KAFD,MAEO,IAAIhB,KAAK,CAACyC,eAAN,IAAA,CAAA,sBAAA,GAA0BzC,KAAa,CAAChD,cAAxC,MAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,IAA0B,sBAA8B0F,CAAAA,OAA5D,EAAqE;AAC1E1C,MAAAA,KAAK,CAAC1D,KAAN,EAAA,CAAA;AACA,MAAA,IAAA,CAAKkG,IAAL,CAAU,OAAV,EAAmB,IAAInE,KAAJ,CAAU,sFAAV,CAAnB,EAAsH2B,KAAtH,EAA6HkB,MAAM,CAACF,IAApI,CAAA,CAAA;AACD,KAAA;;IACD,MAAM2B,IAAI,GAAG,IAAb,CAAA;;IACA,IAAKf,CAAAA,KAAL,CAAW,GAAX,CAAA,CAAA;;IACA,IAAImB,KAAK,GAAG,IAAZ,CAAA;IACA,IAAIC,CAAC,GAAG,CAAR,CAAA;AACA,IAAA,MAAMhB,IAAI,GAAQ;AAChBT,MAAAA,IAAI,EAAE,iBADU;;AAEhB,MAAA,MAAMlF,IAAN,GAAU;QACR,IAAI;UACF,IAAI4G,GAAG,GAAG,EAAV,CAAA;AACA,UAAA,MAAMJ,IAAI,GAAG,MAAMzD,cAAc,CAACY,KAAD,CAAjC,CAAA;;UACA,IAAI6C,IAAI,KAAK,IAAb,EAAmB;AACjB,YAAA,IAAIG,CAAC,IAAIL,IAAI,CAACnC,MAAd,EAAsB;AACpByC,cAAAA,GAAG,IAAS,CAAA,EAAA,EAAA/B,MAAM,CAACV,OAAnB,CAAA,CAAA;AACD,aAAA;;AACDyC,YAAAA,GAAG,IAAI,GAAP,CAAA;;YACAN,IAAI,CAACf,KAAL,CAAWqB,GAAX,CAAA,CAAA;;YACAN,IAAI,CAACX,IAAL,GAAYd,MAAZ,CAAA;YACAyB,IAAI,CAACG,OAAL,CAAa9C,KAAb,CAAA,CAAA;AACA,YAAA,OAAA;AACD,WAAA;;UACD,IAAI+C,KAAJ,EAAWA,KAAK,GAAG,KAAR,CAAX,KACKE,GAAG,IAAI,GAAP,CAAA;UACL,IAAIN,IAAI,CAACnC,MAAT,EAAiByC,GAAG,IAAS,CAAAjB,EAAAA,EAAAA,IAAI,CAACxB,OAAjB,CAAA,CAAA;UACjBmC,IAAI,CAACC,OAAL,GAAeK,GAAf,CAAA;AACAN,UAAAA,IAAI,CAAC1B,OAAL,CAAa4B,IAAb,EAAmBb,IAAnB,EAAyBgB,CAAzB,CAAA,CAAA;AACAA,UAAAA,CAAC,IAAI,CAAL,CAAA;SAlBF,CAmBE,OAAO1E,GAAP,EAAY;AACZqE,UAAAA,IAAI,CAACH,IAAL,CAAU,OAAV,EAAmBlE,GAAnB,CAAA,CAAA;AACAqE,UAAAA,IAAI,CAACjB,OAAL,EAAA,CAAA;AACD,SAAA;AACF,OAAA;;KA1BH,CAAA;IA4BA,IAAKM,CAAAA,IAAL,GAAYA,IAAZ,CAAA;AACD,GAAA;;AAEDI,EAAAA,cAAc,CAACpC,KAAD,EAAsBkB,MAAtB,EAAoCC,GAApC,EAAuC;IACnD,MAAMwB,IAAI,GAAG,IAAb,CAAA;IACA,IAAItG,IAAI,GAAG,KAAX,CAAA;AACA,IAAA,IAAA,CAAK2F,IAAL,GAAY;AACV,MAAA,MAAM3F,IAAN,GAAU;AACR,QAAA,IAAIA,IAAJ,EAAU,OAAA;;QACV,IAAI;AACFA,UAAAA,IAAI,GAAG,IAAP,CAAA;UACAsG,IAAI,CAAC1B,OAAL,CAAa,MAAMjB,KAAnB,EAA0BkB,MAA1B,EAAkCC,GAAlC,CAAA,CAAA;SAFF,CAGE,OAAO7C,GAAP,EAAY;AACZqE,UAAAA,IAAI,CAACH,IAAL,CAAU,OAAV,EAAmBlE,GAAnB,CAAA,CAAA;AACAqE,UAAAA,IAAI,CAACjB,OAAL,EAAA,CAAA;AACD,SAAA;AACF,OAAA;;KAVH,CAAA;AAYD,GAAA;;AAEDS,EAAAA,YAAY,CAACnC,KAAD,EAAekB,MAAf,EAA0B;AACpC;IACA,IAAI8B,CAAC,GAAG,CAAR,CAAA;AACA,IAAA,MAAME,GAAG,GAAGlD,KAAK,CAACjB,MAAlB,CAAA;IACA,IAAIgE,KAAK,GAAG,IAAZ,CAAA;IACA,MAAMJ,IAAI,GAAG,IAAb,CAAA;AACA,IAAA,MAAMX,IAAI,GAAS;AACjB3F,MAAAA,IAAI,GAAA;QACF,IAAI4G,GAAG,GAAG,EAAV,CAAA;QACA,IAAIE,QAAQ,GAAG,KAAf,CAAA;;AACA,QAAA,IAAIJ,KAAJ,EAAW;AACTA,UAAAA,KAAK,GAAG,KAAR,CAAA;AACAI,UAAAA,QAAQ,GAAG,IAAX,CAAA;;UACA,IAAI,CAACD,GAAL,EAAU;YACRP,IAAI,CAACf,KAAL,CAAW,IAAX,CAAA,CAAA;;YACAe,IAAI,CAACG,OAAL,CAAa9C,KAAb,CAAA,CAAA;YACA2C,IAAI,CAACX,IAAL,GAAYd,MAAZ,CAAA;AACA,YAAA,OAAA;AACD,WAAA;;AACD+B,UAAAA,GAAG,IAAI,GAAP,CAAA;AACD,SAAA;;AACD,QAAA,MAAMG,KAAK,GAAGpD,KAAK,CAACgD,CAAD,CAAnB,CAAA;;QACA,IAAIA,CAAC,KAAKE,GAAV,EAAe;UACb,IAAIP,IAAI,CAACnC,MAAT,EAAiByC,GAAG,IAAS,CAAA/B,EAAAA,EAAAA,MAAM,CAACV,OAAnB,CAAA,CAAA;AACjByC,UAAAA,GAAG,IAAI,GAAP,CAAA;;UACAN,IAAI,CAACf,KAAL,CAAWqB,GAAX,CAAA,CAAA;;UACAN,IAAI,CAACX,IAAL,GAAYd,MAAZ,CAAA;UACAyB,IAAI,CAACG,OAAL,CAAa9C,KAAb,CAAA,CAAA;AACA,UAAA,OAAA;AACD,SAAA;;AACD,QAAA,IAAI,CAACmD,QAAL,EAAeF,GAAG,IAAI,GAAP,CAAA;QACf,IAAIN,IAAI,CAACnC,MAAT,EAAiByC,GAAG,IAAS,CAAAjB,EAAAA,EAAAA,IAAI,CAACxB,OAAjB,CAAA,CAAA;;QACjBmC,IAAI,CAACf,KAAL,CAAWqB,GAAX,CAAA,CAAA;;AACAN,QAAAA,IAAI,CAAC1B,OAAL,CAAamC,KAAb,EAAoBpB,IAApB,EAA0BgB,CAA1B,CAAA,CAAA;AACAA,QAAAA,CAAC,IAAI,CAAL,CAAA;AACD,OAAA;;KA7BH,CAAA;IA+BA,IAAKhB,CAAAA,IAAL,GAAYA,IAAZ,CAAA;AACD,GAAA;;EAEDc,OAAO,CAACd,IAAD,EAAK;IACV,IAAI,IAAA,CAAK7B,KAAT,EAAgB,OAAA;;IAChB,MAAMkD,EAAE,GAAI,IAAKzC,CAAAA,OAAL,CAAuB0C,OAAvB,CAA+BtB,IAA/B,CAAZ,CAAA;;AACA,IAAA,IAAIqB,EAAE,GAAG,CAAC,CAAV,EAAc,IAAA,CAAKzC,OAAL,CAAuB2C,MAAvB,CAA8BF,EAA9B,EAAkC,CAAlC,CAAA,CAAA;AACf,GAAA;;AAGDnB,EAAAA,aAAa,CAAClC,KAAD,EAA0BkB,MAAM,GAAGI,SAAnC,EAA4C;AACvD,IAAA,MAAMkC,IAAI,GAAGlG,MAAM,CAACkG,IAAP,CAAYxD,KAAZ,CAAb,CAAA;IACA,IAAIgD,CAAC,GAAG,CAAR,CAAA;AACA,IAAA,MAAME,GAAG,GAAGM,IAAI,CAACzE,MAAjB,CAAA;IACA,IAAIgE,KAAK,GAAG,IAAZ,CAAA;IACA,MAAMJ,IAAI,GAAG,IAAb,CAAA;IACA,MAAM;AAAEhC,MAAAA,OAAAA;AAAF,KAAA,GAAc,IAApB,CAAA;IACA,IAAI8C,QAAQ,GAAG,KAAf,CAAA;AACA,IAAA,IAAItC,GAAJ,CAAA;AACA,IAAA,MAAMa,IAAI,GAAc;AACtB3F,MAAAA,IAAI,GAAA;AAAA,QAAA,IAAA,gBAAA,CAAA;;QACF,IAAI2G,CAAC,KAAK,CAAV,EAAaL,IAAI,CAACf,KAAL,CAAW,GAAX,CAAA,CAAA;;QACb,IAAIoB,CAAC,KAAKE,GAAV,EAAe;UACbP,IAAI,CAACe,UAAL,GAAkBpC,SAAlB,CAAA;;UACA,IAAI,CAACmC,QAAL,EAAe;YACbd,IAAI,CAACf,KAAL,CAAW,GAAX,CAAA,CAAA;AACD,WAFD,MAEO;AACLe,YAAAA,IAAI,CAACf,KAAL,CAAW,CAAA,EAAGe,IAAI,CAACnC,MAAL,QAAmBU,MAAM,CAACV,MAAQ,CAAlC,CAAA,GAAqC,EAAK,CAAxD,CAAA,CAAA,CAAA,CAAA;AACD,WAAA;;UACDmC,IAAI,CAACX,IAAL,GAAYd,MAAZ,CAAA;UACAyB,IAAI,CAACG,OAAL,CAAa9C,KAAb,CAAA,CAAA;AACA,UAAA,OAAA;AACD,SAAA;;AACDmB,QAAAA,GAAG,GAAGqC,IAAI,CAACR,CAAD,CAAV,CAAA;;AACA,QAAA,IAAI,CAAArC,OAAO,KAAA,IAAP,IAAAA,OAAO,KAAA,KAAA,CAAP,gCAAAA,OAAO,CAAE2C,OAAT,MAAA,IAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gBAAA,CAAA,IAAA,CAAA3C,OAAO,EAAYQ,GAAZ,CAAP,MAA4B,CAAC,CAAjC,EAAoC;AAClC;AACA6B,UAAAA,CAAC,IAAI,CAAL,CAAA;AACA,UAAA,OAAA;AACD,SAAA;;QACDL,IAAI,CAACe,UAAL,GAAkB1B,IAAlB,CAAA;AACAgB,QAAAA,CAAC,IAAI,CAAL,CAAA;QACAL,IAAI,CAAC1B,OAAL,CAAajB,KAAK,CAACmB,GAAD,CAAlB,EAAyBa,IAAzB,EAA+Bb,GAA/B,CAAA,CAAA;OAtBoB;;AAwBtBwC,MAAAA,KAAK,GAAA;AACH,QAAA,MAAMV,GAAG,GAAM,CAAA,EAAAQ,QAAQ,IAAI,CAACV,KAAb,GAAqB,GAArB,GAA2B,EAAK,CAAAf,EAAAA,IAAI,CAACxB,MAAL,GAAmB,CAAAwB,EAAAA,EAAAA,IAAI,CAACxB,MAAM,EAA9B,GAAmC,EAAK,CAAA3B,EAAAA,WAAW,CAACsC,GAAD,CAAK,CAAIwB,CAAAA,EAAAA,IAAI,CAACnC,MAAL,GAAc,GAAd,GAAoB,EAAE,CAAjI,CAAA,CAAA;AACAuC,QAAAA,KAAK,GAAG,KAAR,CAAA;AACAU,QAAAA,QAAQ,GAAG,IAAX,CAAA;QACAd,IAAI,CAACe,UAAL,GAAkBpC,SAAlB,CAAA;AACA,QAAA,OAAO2B,GAAP,CAAA;AACD,OAAA;;KA9BH,CAAA;IAgCA,IAAKjB,CAAAA,IAAL,GAAYA,IAAZ,CAAA;AACD,GAAA;;EASOJ,KAAK,CAACiB,IAAD,EAAK;AAChB,IAAA,MAAMI,GAAG,GAAG,CAAC,IAAA,CAAKS,UAAL,GAAkB,IAAA,CAAKA,UAAL,CAAgBC,KAAhB,EAAlB,GAA4C,EAA7C,IAAmDd,IAA/D,CAAA;;AACA,IAAA,IAAI,KAAKD,OAAL,IAAgBK,GAAG,CAAClE,MAAxB,EAAgC;MAC9B,IAAK6E,CAAAA,MAAL,IAAe,IAAA,CAAKhB,OAApB,CAAA;MACA,IAAKA,CAAAA,OAAL,GAAetB,SAAf,CAAA;AACD,KAAA;;IACD,IAAKsC,CAAAA,MAAL,IAAeX,GAAf,CAAA;;AACA,IAAA,IAAI,KAAKW,MAAL,CAAY7E,MAAZ,IAAsB,IAAA,CAAKqB,UAA/B,EAA2C;MACzC,IAAKyD,CAAAA,UAAL,GAAkB,CAAC,IAAA,CAAKlC,IAAL,CAAU,IAAA,CAAKiC,MAAf,CAAnB,CAAA;MACA,IAAKA,CAAAA,MAAL,GAAc,EAAd,CAAA;MACA,IAAKE,CAAAA,YAAL,GAAoB,CAApB,CAAA;AACA,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;;AACD,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;EAGU,MAALC,KAAK,CAACzE,IAAD,EAAc;AACvB,IAAA,IAAI,KAAK0E,SAAL,KAAmBpE,SAAS,CAACqE,QAAjC,EAA2C,OAAA;;AAC3C,IAAA,IAAI,KAAKD,SAAL,KAAmBpE,SAAS,CAACU,QAAjC,EAA2C;AACzC,MAAA,IAAA,CAAK0D,SAAL,GAAiBpE,SAAS,CAACsE,QAA3B,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AACD,IAAA,IAAA,CAAKF,SAAL,GAAiBpE,SAAS,CAACuE,OAA3B,CAAA;IACA,IAAKN,CAAAA,UAAL,GAAkB,KAAlB,CAAA;AACA,IAAA,IAAIO,CAAJ,CAAA;;AACA,IAAA,OAAO,CAAC,IAAA,CAAKP,UAAN,IAAoB,KAAK7B,IAAL,KAAc,IAAKlB,CAAAA,IAAvC,IAA+C,IAAA,CAAK8C,MAAL,KAAgBtC,SAAtE,EAAiF;MAC/E8C,CAAC,GAAG,KAAKpC,IAAL,CAAU3F,IAAV,CAAeiD,IAAf,CAAJ,CAD+E;;MAG/E,IAAI8E,CAAJ,EAAO,MAAMA,CAAN,CAAA;AACR,KAAA;;AACD,IAAA,IAAI,IAAKR,CAAAA,MAAL,KAAgBtC,SAApB,EAA+B,OAAA;;AAC/B,IAAA,IAAI,IAAKU,CAAAA,IAAL,KAAc,IAAA,CAAKlB,IAAvB,EAA6B;MAC3B,IAAI,IAAA,CAAK8C,MAAL,CAAY7E,MAAhB,EAAwB,IAAK4C,CAAAA,IAAL,CAAU,IAAA,CAAKiC,MAAf,CAAA,CAAA;MACxB,IAAKjC,CAAAA,IAAL,CAAU,IAAV,CAAA,CAAA;AACA,MAAA,IAAA,CAAKqC,SAAL,GAAiBpE,SAAS,CAACqE,QAA3B,CAAA;AACA,MAAA,IAAA,CAAKI,OAAL,EAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AACD,IAAA,IAAI,KAAKL,SAAL,KAAwBpE,SAAS,CAACsE,QAAtC,EAAgD;AAC9C,MAAA,IAAA,CAAKF,SAAL,GAAiBpE,SAAS,CAACU,QAA3B,CAAA;AACA,MAAA,MAAM,IAAKyD,CAAAA,KAAL,CAAWzE,IAAX,CAAN,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AACD,IAAA,IAAA,CAAK0E,SAAL,GAAiBpE,SAAS,CAACU,QAA3B,CAAA;AACD,GAAA;;AAEO+D,EAAAA,OAAO,GAAA;AACb,IAAA,IAAA,CAAKL,SAAL,GAAiBpE,SAAS,CAACqE,QAA3B,CAAA;IACA,IAAKL,CAAAA,MAAL,GAActC,SAAd,CAAA;IACA,IAAKV,CAAAA,OAAL,GAAeU,SAAf,CAAA;IACA,IAAKU,CAAAA,IAAL,GAAYV,SAAZ,CAAA;IACA,IAAKR,CAAAA,IAAL,GAAYQ,SAAZ,CAAA;IACA,IAAKsB,CAAAA,OAAL,GAAetB,SAAf,CAAA;AACD,GAAA;;EAEDI,OAAO,CAAC4C,KAAD,EAAc;AAAA,IAAA,IAAA,cAAA,CAAA;;AACnB,IAAA,IAAIA,KAAJ,EAAW,IAAA,CAAK9B,IAAL,CAAU,OAAV,EAAmB8B,KAAnB,CAAA,CAAA;AACX,IAAA,CAAA,cAAA,GAAA,KAAA,CAAM5C,OAAN,MAAA,IAAA,IAAA,cAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAK2C,OAAL,EAAA,CAAA;AACA,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AAnX8C;;;;"}