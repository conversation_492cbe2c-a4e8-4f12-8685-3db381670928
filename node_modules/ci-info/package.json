{"name": "ci-info", "version": "2.0.0", "description": "Get details about the current Continuous Integration environment", "main": "index.js", "dependencies": {}, "devDependencies": {"clear-require": "^1.0.1", "standard": "^12.0.1", "tape": "^4.9.1"}, "scripts": {"test": "standard && node test.js"}, "repository": {"type": "git", "url": "https://github.com/watson/ci-info.git"}, "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": "<PERSON> <<EMAIL>> (https://twitter.com/wa7son)", "license": "MIT", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "homepage": "https://github.com/watson/ci-info", "coordinates": [55.778231, 12.593179]}