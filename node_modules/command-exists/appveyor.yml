init:
  # Get the latest stable version of Node.js
  - ps: Install-Product node $env:nodejs_version

image:
  - Visual Studio 2017

matrix:
  fast_finish: true

environment:
  matrix:
    - nodejs_version: "4"
    - nodejs_version: "6"
    - nodejs_version: "7"
    - nodejs_version: "8"
    - nodejs_version: "9"

install:
  # install modules
  - npm install

# Post-install test scripts.
test_script:
  # Output useful info for debugging.
  - node --version
  - npm --version
  - npm run test

# Don't actually build.
build: off