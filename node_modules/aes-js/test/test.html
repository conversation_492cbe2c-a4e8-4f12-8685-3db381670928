<html>
  <body>
    <div style="font-family: monospace" id="console"></div>
    <script type="text/javascript" src="../index.js"></script>
    <script type="text/javascript">
      var Console = document.getElementById('console');

      function bufferEquals(a, b) {
          if (a.length != b.length) { return false; }
          for (var i = 0; i < a.length; i++) {
              if (a[i] != b[i]) { return false; }
          }
          return true;
      }

      // http://stackoverflow.com/questions/9838812/how-can-i-open-a-json-file-in-javascript-without-jquery
      function loadJSON(path, success, error) {
        var xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function()
        {
          if (xhr.readyState === XMLHttpRequest.DONE) {
            if (xhr.status === 200 || xhr.status == 0) {
              if (success) {
                success(JSON.parse(xhr.responseText));
              }
            } else {
              if (error) {
                error(xhr);
              }
            }
          }
        };
        xhr.open("GET", path, true);
        xhr.send();
      }

      function runTest(options) {

          var modeOfOperation = options.modeOfOperation;
          var mo = aesjs.ModeOfOperation[modeOfOperation];

          return function (test) {
              var func;
              switch (modeOfOperation) {
                  case 'ecb':
                      func = function() { return new mo(options.key); }
                      break;
                  case 'cfb':
                      func = function() { return new mo(options.key, options.iv, options.segmentSize); }
                      break;
                  case 'ofb':
                  case 'cbc':
                      func = function() { return new mo(options.key, options.iv); }
                      break;
                  case 'ctr':
                      func = function() { return new mo(options.key, new aesjs.Counter(0)); }
                      break;
                  default:
                      throw new Error('unknwon mode of operation')
              }

              var encrypter = func(), decrypter = func();
              var totalDiffers = 0;
              for (var i = 0; i < options.plaintext.length; i++) {
                  var ciphertext2 = encrypter.encrypt(options.plaintext[i]);
                  if (!bufferEquals(ciphertext2, options.encrypted[i])) {
                      return "encrypt failed to match test vector";
                  }

                  var plaintext2 = decrypter.decrypt(ciphertext2);
                   if (!bufferEquals(plaintext2, options.plaintext[i])) {
                      return "decrypt failed to match original text";
                  }
              }

              return "pass";
          }
      }



      loadJSON('test-vectors.json', function(data) {
          var passed = 0;

          var trials = {};
          for (var i = 0; i < data.length; i++) {
              var test = data[i];
              name = 'test-' + test.modeOfOperation + '-' + test.key.length;

              if (trials[name] == null) { trials[name] = 0; }
              var trial = trials[name]++;

              var result = runTest(test)();
              Console.innerHTML += name + ' (trial-' + (trial) + '): ' + result + '<br />';
              if (result == 'pass') { passed++; }
          }
          Console.innerHTML += 'Tests: ' + passed + '/' + data.length + ' passed' + '<br />';
      
      }, function(error) {
          console.log(error);
      });
    </script>
  </body>
</html>
