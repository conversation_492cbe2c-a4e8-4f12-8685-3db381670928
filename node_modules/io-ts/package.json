{"name": "io-ts", "version": "1.10.4", "description": "TypeScript compatible runtime type system for IO validation", "files": ["lib", "es6"], "main": "lib/index.js", "module": "es6/index.js", "typings": "lib/index.d.ts", "sideEffects": false, "scripts": {"lint": "tslint -p tsconfig.tslint.json src/**/*.ts test/**/*.ts", "jest": "jest", "prettier": "prettier --no-semi --single-quote --print-width 120 --parser typescript --list-different \"{src,test}/**/*.ts\"", "fix-prettier": "prettier --no-semi --single-quote --print-width 120 --parser typescript --write \"{src,test,examples,exercises}/**/*.ts\"", "test": "npm run prettier && npm run lint && npm run dtslint && npm run declaration && npm run jest && npm run docs", "clean": "rimraf lib/* es6/*", "build": "npm run clean && tsc && tsc -p tsconfig.es6.json", "prepublish": "npm run build", "perf": "ts-node perf/index", "dtslint": "dtslint dtslint", "declaration": "tsc -p declaration/tsconfig.json", "mocha": "TS_NODE_CACHE=false mocha -r ts-node/register test/*.ts", "doctoc": "doctoc README.md", "docs": "docs-ts"}, "repository": {"type": "git", "url": "https://github.com/gcanti/io-ts.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/gcanti/io-ts/issues"}, "homepage": "https://github.com/gcanti/io-ts", "dependencies": {"fp-ts": "^1.0.0"}, "devDependencies": {"@types/benchmark": "1.0.31", "@types/jest": "^23.3.8", "@types/node": "7.0.4", "benchmark": "2.1.4", "docs-ts": "^0.1.0", "doctoc": "^1.4.0", "dtslint": "github:gcanti/dtslint", "jest": "^24.8.0", "mocha": "^5.2.0", "prettier": "^1.15.3", "rimraf": "2.6.2", "ts-jest": "^24.0.2", "ts-node": "^7.0.1", "tslint": "^5.11.0", "tslint-config-standard": "^8.0.1", "typescript": "^3.5.2"}, "tags": ["typescript", "validation", "inference", "types", "runtime"], "keywords": ["typescript", "validation", "inference", "types", "runtime"], "jest": {"collectCoverage": true, "transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": "test", "moduleFileExtensions": ["ts", "js"], "testPathIgnorePatterns": ["helpers.ts"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}}}