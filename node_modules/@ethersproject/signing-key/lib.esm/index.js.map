{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,EAAE,EAAE,MAAM,YAAY,CAAC;AAEhC,OAAO,EAAE,QAAQ,EAAa,aAAa,EAAE,OAAO,EAAE,UAAU,EAA4B,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACzI,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,IAAI,MAAM,GAAO,IAAI,CAAA;AACrB,SAAS,QAAQ;IACb,IAAI,CAAC,MAAM,EAAE;QACT,MAAM,GAAG,IAAI,EAAE,CAAC,WAAW,CAAC,CAAC;KAChC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,OAAO,UAAU;IAYnB,YAAY,UAAqB;QAC7B,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAE3C,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACxD,IAAI,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE;YACvC,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;SACpF;QAED,MAAM,OAAO,GAAG,QAAQ,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAErE,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;QAC1E,cAAc,CAAC,IAAI,EAAE,qBAAqB,EAAE,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAEnF,cAAc,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,SAAS,CAAC,KAAgB;QACtB,MAAM,EAAE,GAAI,QAAQ,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC/D,MAAM,EAAE,GAAI,QAAQ,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QACtD,OAAO,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,UAAU,CAAC,MAAiB;QACxB,MAAM,OAAO,GAAG,QAAQ,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACrE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,WAAW,CAAC,MAAM,KAAK,EAAE,EAAE;YAC3B,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;SACpE;QACD,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACjE,OAAO,cAAc,CAAC;YAClB,aAAa,EAAE,SAAS,CAAC,aAAa;YACtC,CAAC,EAAE,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,CAAC,EAAE,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;SACrD,CAAC,CAAA;IACN,CAAC;IAED,mBAAmB,CAAC,QAAmB;QACnC,MAAM,OAAO,GAAG,QAAQ,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,QAAQ,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpF,OAAO,UAAU,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACxF,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,KAAU;QAC1B,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;IAC5C,CAAC;CACJ;AAED,MAAM,UAAU,gBAAgB,CAAC,MAAiB,EAAE,SAAwB;IACxE,MAAM,GAAG,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;IACtC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IACtD,OAAO,IAAI,GAAG,QAAQ,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACzG,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,GAAc,EAAE,UAAoB;IACjE,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAE5B,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;QACrB,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,UAAU,EAAE;YACZ,OAAO,IAAI,GAAG,QAAQ,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACzE;QACD,OAAO,UAAU,CAAC,SAAS,CAAC;KAE/B;SAAM,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,IAAI,UAAU,EAAE;YAAE,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;SAAE;QAC1C,OAAO,IAAI,GAAG,QAAQ,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KAEzE;SAAM,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;SAAE;QAC3C,OAAO,IAAI,GAAG,QAAQ,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACxE;IAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,+BAA+B,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;AAC3F,CAAC"}