{"version": 3, "file": "geturl.js", "sourceRoot": "", "sources": ["../src.ts/browser-geturl.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;AAEb,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAMhD,MAAM,UAAgB,MAAM,CAAC,IAAY,EAAE,OAAiB;;QACxD,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,GAAG,EAAG,CAAC;SAAE;QAEvC,MAAM,OAAO,GAAgB;YACzB,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC;YACjC,OAAO,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI,EAAG,CAAC;YACjC,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC;SACpC,CAAC;QAEF,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE;YACjC,OAAO,CAAC,IAAI,GAAgB,MAAM,CAAC,CAAc,8BAA8B;YAC/E,OAAO,CAAC,KAAK,GAAiB,UAAU,CAAC,CAAQ,0DAA0D;YAC3G,OAAO,CAAC,WAAW,GAAuB,aAAa,CAAC,CAAE,8BAA8B;YACxF,OAAO,CAAC,QAAQ,GAAoB,QAAQ,CAAC,CAAI,yBAAyB;YAC1E,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAqB,uBAAuB;SAC3E;QAAA,CAAC;QAEF,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE;YAC9B,MAAM,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC;YAClC,IAAI,IAAI,CAAC,IAAI,EAAE;gBAAE,OAAO,CAAC,IAAI,GAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAAE;YAC3D,IAAI,IAAI,CAAC,KAAK,EAAE;gBAAE,OAAO,CAAC,KAAK,GAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAAE;YAC/D,IAAI,IAAI,CAAC,WAAW,EAAE;gBAAE,OAAO,CAAC,WAAW,GAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAAE;YACvF,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAAE,OAAO,CAAC,QAAQ,GAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAAE;YAC3E,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAAE,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;aAAE;SAC3D;QAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC;QAE1C,MAAM,OAAO,GAAiC,EAAG,CAAC;QAClD,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE;YAC1B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBACpC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC;YACvC,CAAC,CAAC,CAAC;SACN;aAAM;YACmB,CAAO,CAAC,QAAQ,CAAC,OAAO,CAAE,CAAC,IAAI,CAAE,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBACtE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;SACN;QAED,OAAO;YACH,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,QAAQ,CAAC,MAAM;YAC3B,aAAa,EAAE,QAAQ,CAAC,UAAU;YAClC,IAAI,EAAE,QAAQ,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;SACvC,CAAA;IACL,CAAC;CAAA"}