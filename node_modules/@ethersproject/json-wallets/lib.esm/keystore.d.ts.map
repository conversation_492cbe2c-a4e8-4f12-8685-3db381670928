{"version": 3, "file": "keystore.d.ts", "sourceRoot": "", "sources": ["../src.ts/keystore.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,sBAAsB,EAAE,MAAM,gCAAgC,CAAC;AAExE,OAAO,EAAY,KAAK,EAAE,SAAS,EAAmB,MAAM,sBAAsB,CAAC;AACnF,OAAO,EAA0C,QAAQ,EAAqB,MAAM,uBAAuB,CAAC;AAI5G,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAexD,MAAM,WAAW,gBAAgB;IAC7B,OAAO,EAAE,MAAM,CAAC;IAChB,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,QAAQ,CAAC;IAEpB,kBAAkB,EAAE,OAAO,CAAC;CAC/B;AAED,qBAAa,eAAgB,SAAQ,WAAW,CAAC,gBAAgB,CAAE,YAAW,sBAAsB;IAChG,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IACzB,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC;IAE7B,QAAQ,CAAC,kBAAkB,EAAE,OAAO,CAAC;IAErC,iBAAiB,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,eAAe;CAG1D;AAED,oBAAY,gBAAgB,GAAG,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;AAEzD,oBAAY,cAAc,GAAG;IAC1B,EAAE,CAAC,EAAE,SAAS,CAAC;IACf,OAAO,CAAC,EAAE,SAAS,CAAC;IACpB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,MAAM,CAAC,EAAE;QACL,CAAC,CAAC,EAAE,MAAM,CAAC;QACX,CAAC,CAAC,EAAE,MAAM,CAAC;QACX,CAAC,CAAC,EAAE,MAAM,CAAC;KACd,CAAA;CACH,CAAA;AAuJD,wBAAgB,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,GAAG,MAAM,GAAG,eAAe,CAKnF;AAED,wBAAsB,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,GAAG,MAAM,EAAE,gBAAgB,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,eAAe,CAAC,CAKnI;AAGD,wBAAgB,OAAO,CAAC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,EAAE,KAAK,GAAG,MAAM,EAAE,OAAO,CAAC,EAAE,cAAc,EAAE,gBAAgB,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAsJjK"}