{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AAEH,OAAO,EAAE,QAAQ,EAAa,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D,MAAM,OAAO,KAAK;IAOd,YAAY,QAAgB;QACxB,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC3C,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE9C,cAAc,CAAC,IAAI,EAAE,cAAc,EAAE,EAAG,CAAC,CAAC;QAC1C,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD,2BAA2B;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SAC7C;IACL,CAAC;IAED,MAAM,CAAC,KAAgB;QACnB,IAAI,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAE7B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAAE,OAAO,EAAE,CAAC;SAAE;QAEvC,IAAI,MAAM,GAAG,CAAE,CAAC,CAAE,CAAA;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACpC,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACpC,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACxB,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC9B,KAAK,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACnC;YAED,OAAO,KAAK,GAAG,CAAC,EAAE;gBACd,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/B,KAAK,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACnC;SACJ;QAED,IAAI,MAAM,GAAG,EAAE,CAAA;QAEf,0BAA0B;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YAC3D,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;SAC1B;QAED,6BAA6B;QAC7B,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;YACzC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACtC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,KAAa;QAChB,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,SAAS,CAAC,iBAAiB,CAAC,CAAC;SAC1C;QAED,IAAI,KAAK,GAAkB,EAAE,CAAC;QAC9B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAAE,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;SAAE;QAEzD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAEvC,IAAI,IAAI,KAAK,SAAS,EAAE;gBACpB,MAAM,IAAI,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC;aAC1D;YAED,IAAI,KAAK,GAAG,IAAI,CAAC;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACnC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;gBACxB,KAAK,KAAK,CAAC,CAAC;aACf;YAED,OAAO,KAAK,GAAG,CAAC,EAAE;gBACd,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;gBACzB,KAAK,KAAK,CAAC,CAAC;aACf;SACJ;QAED,0BAA0B;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YACpE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;SAChB;QAED,OAAO,QAAQ,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;IACpD,CAAC;CACJ;AAED,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAC7D,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;AAEvF,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAE1B,8EAA8E;AAC9E,6FAA6F"}