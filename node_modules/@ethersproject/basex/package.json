{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/properties": "^5.8.0"}, "description": "Base-X without <PERSON><PERSON><PERSON>.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/basex", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/basex", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x2bd9c63ee656a1f235a8b6152728ec154fb840203cdfe1e1225630725bdee105", "types": "./lib/index.d.ts", "version": "5.8.0"}