{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/abstract-provider": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0"}, "description": "An Abstract Class for desribing an Ethereum Signer for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/abstract-signer", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/abstract-signer", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x729715181dd26051cd4a9d4e3c108226bfc5a834ae39543faa57eba3f837e2c0", "types": "./lib/index.d.ts", "version": "5.8.0"}