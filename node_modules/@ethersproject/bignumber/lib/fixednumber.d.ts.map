{"version": 3, "file": "fixednumber.d.ts", "sourceRoot": "", "sources": ["../src.ts/fixednumber.ts"], "names": [], "mappings": "AAEA,OAAO,EAAY,SAAS,EAAuB,MAAM,sBAAsB,CAAC;AAMhF,OAAO,EAAE,SAAS,EAAE,YAAY,EAAkB,MAAM,aAAa,CAAC;AAiCtE,wBAAgB,WAAW,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,YAAY,GAAG,MAAM,CA0BzF;AAED,wBAAgB,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,YAAY,GAAG,SAAS,CAmD5E;AAGD,qBAAa,WAAW;IACpB,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;IACzB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC1B,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;gBAEjB,gBAAgB,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;IAkBnF,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG,WAAW;CA8CvC;AAED,qBAAa,WAAW;IACpB,QAAQ,CAAC,MAAM,EAAE,WAAW,CAAC;IAC7B,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IAExB,QAAQ,CAAC,cAAc,EAAE,OAAO,CAAC;gBAErB,gBAAgB,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,WAAW;IAgBnF,YAAY,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI;IAMtC,SAAS,CAAC,KAAK,EAAE,WAAW,GAAG,WAAW;IAO1C,SAAS,CAAC,KAAK,EAAE,WAAW,GAAG,WAAW;IAO1C,SAAS,CAAC,KAAK,EAAE,WAAW,GAAG,WAAW;IAO1C,SAAS,CAAC,KAAK,EAAE,WAAW,GAAG,WAAW;IAO1C,KAAK,IAAI,WAAW;IAcpB,OAAO,IAAI,WAAW;IAetB,KAAK,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,WAAW;IAmBrC,MAAM,IAAI,OAAO;IAIjB,UAAU,IAAI,OAAO;IAIrB,QAAQ,IAAI,MAAM;IAElB,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM;IAOnC,aAAa,IAAI,MAAM;IAEvB,QAAQ,CAAC,MAAM,EAAE,WAAW,GAAG,MAAM,GAAG,WAAW;IAKnD,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,YAAY,EAAE,MAAM,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW;IAchH,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW;IAwBrF,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW;IAkBvF,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,MAAM;IAqB9D,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,WAAW;CAGzD"}