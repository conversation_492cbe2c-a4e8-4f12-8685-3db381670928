{"_ethers.alias": {"pbkdf2.js": "browser-pbkdf2.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/pbkdf2": "./lib/browser-pbkdf2.js"}, "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/sha2": "^5.8.0"}, "description": "The PBKDF2 password-pbased key derivation function for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers", "pbkdf2"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/pbkdf2", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/pbkdf2", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x941fdcf1732952484156dd56761eb99ad9fd9ae3a70657d425ef16425763cde3", "types": "./lib/index.d.ts", "version": "5.8.0"}