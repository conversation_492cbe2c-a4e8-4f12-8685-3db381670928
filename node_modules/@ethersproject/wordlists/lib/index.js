"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.wordlists = exports.Wordlist = exports.logger = void 0;
// Wordlists
// See: https://github.com/bitcoin/bips/blob/master/bip-0039/bip-0039-wordlists.md
var wordlist_1 = require("./wordlist");
Object.defineProperty(exports, "logger", { enumerable: true, get: function () { return wordlist_1.logger; } });
Object.defineProperty(exports, "Wordlist", { enumerable: true, get: function () { return wordlist_1.Wordlist; } });
var wordlists_1 = require("./wordlists");
Object.defineProperty(exports, "wordlists", { enumerable: true, get: function () { return wordlists_1.wordlists; } });
//# sourceMappingURL=index.js.map