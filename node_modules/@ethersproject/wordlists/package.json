{"_browser-all": {"./lib/wordlists": "./lib/wordlists.js"}, "_ethers.alias": {"wordlists.js": "browser-wordlists.js"}, "author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/hash": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/strings": "^5.8.0"}, "description": "Word lists for BIP39 wallets.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/wordlists", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/wordlists", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x3e1814d163850783bd6750a9f72bb2c608e3facb2c573eb24424b821e716ec06", "types": "./lib/index.d.ts", "version": "5.8.0"}