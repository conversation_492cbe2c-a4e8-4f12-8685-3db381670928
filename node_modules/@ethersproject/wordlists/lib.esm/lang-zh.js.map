{"version": 3, "file": "lang-zh.js", "sourceRoot": "", "sources": ["../src.ts/lang-zh.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAEtD,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAGtC,MAAM,IAAI,GAAG,kgMAAkgM,CAAC;AAChhM,MAAM,SAAS,GAAG,6lDAA6lD,CAAC;AAEhnD,qBAAqB;AAErB,MAAM,QAAQ,GAAqC;IAC/C,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;CACd,CAAA;AAED,MAAM,MAAM,GAA8B;IACtC,KAAK,EAAE,oEAAoE;IAC3E,KAAK,EAAE,oEAAoE;CAC9E,CAAA;AAED,MAAM,KAAK,GAAG,kEAAkE,CAAC;AACjF,MAAM,KAAK,GAAG,4BAA4B,CAAA;AAE1C,SAAS,SAAS,CAAC,IAAc;IAC7B,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO;KAAE;IAE/C,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IAE3B,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;QAC3B,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG;YACV,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YACd,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACpC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SACvC,CAAC;QAEF,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE;YACzB,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9E;SACJ;QAED,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;KACnD;IAED,qDAAqD;IACrD,wBAAwB;IACxB,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QAC9C,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC,MAAM,GAAG,mBAAmB,CAAC,CAAC;KAC9E;AACL,CAAC;AAED,MAAM,MAAO,SAAQ,QAAQ;IACzB,YAAY,OAAe;QACvB,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;IAC3B,CAAC;IAED,OAAO,CAAC,KAAa;QACjB,SAAS,CAAC,IAAI,CAAC,CAAC;QAChB,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,YAAY,CAAC,IAAY;QACrB,SAAS,CAAC,IAAI,CAAC,CAAC;QAChB,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,QAAgB;QAClB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;CACJ;AAED,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;AAClC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC5B,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAElC,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;AAClC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAE5B,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC"}