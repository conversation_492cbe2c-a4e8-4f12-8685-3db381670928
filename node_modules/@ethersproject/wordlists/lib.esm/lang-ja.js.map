{"version": 3, "file": "lang-ja.js", "sourceRoot": "", "sources": ["../src.ts/lang-ja.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAEnE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAG9C,MAAM,IAAI,GAAG;IAET,eAAe;IACf,orEAAorE;IAEprE,eAAe;IACf,ssGAAssG;IAEtsG,eAAe;IACf,4uDAA4uD;IAE5uD,eAAe;IACf,olBAAolB;IAEplB,eAAe;IACf,4JAA4J;IAE5J,eAAe;IACf,0GAA0G;IAE1G,gBAAgB;IAChB,WAAW;CACd,CAAC;AAEF,sDAAsD;AACtD,MAAM,OAAO,GAAG,6FAA6F,CAAA;AAE7G,IAAI,QAAQ,GAAkB,IAAI,CAAC;AAEnC,SAAS,GAAG,CAAC,IAAY;IACrB,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AACtC,CAAC;AAED,MAAM,MAAM,GAAG,sBAAsB,CAAC;AACtC,MAAM,KAAK,GAAG,sBAAsB,CAAA;AAEpC,SAAS,SAAS,CAAC,IAAc;IAC7B,IAAI,QAAQ,KAAK,IAAI,EAAE;QAAE,OAAO;KAAE;IAElC,QAAQ,GAAG,EAAE,CAAC;IAEd,yDAAyD;IACzD,MAAM,SAAS,GAAwC,EAAE,CAAC;IAE1D,6BAA6B;IAC7B,SAAS,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IACjD,SAAS,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAEjD,yDAAyD;IACzD,SAAS,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACzE,SAAS,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACzE,SAAS,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACzE,SAAS,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAGzE,sCAAsC;IACtC,SAAS,SAAS,CAAC,IAAY;QAC3B,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACnB,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,MAAM,KAAK,KAAK,EAAE;gBAAE,SAAS;aAAE;YACnC,IAAI,MAAM,EAAE;gBAAE,IAAI,GAAW,MAAM,CAAC;aAAE;YACtC,MAAM,IAAI,IAAI,CAAC;SAClB;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,uCAAuC;IACvC,SAAS,YAAY,CAAC,CAAS,EAAE,CAAS;QACtC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,GAAG,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,CAAC;SAAE;QACzB,IAAI,CAAC,GAAG,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC;SAAE;QACxB,OAAO,CAAC,CAAC;IACb,CAAC;IAED,qBAAqB;IACrB,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE;QACxC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3B,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,IAAI,MAAM,EAAE;YACtD,MAAM,IAAI,GAAG,EAAE,CAAC;YAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5B,MAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACf,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA,CAAC,CAAC,GAAG,CAAC,CAAC;gBACjC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aAChC;YACD,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;SACrC;KACJ;IACD,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAE5B,6DAA6D;IAC7D,uBAAuB;IACvB,YAAY;IACZ,aAAa;IAEb,wDAAwD;IACxD,0BAA0B;IAC1B,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,EAAE;QAC/D,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC1B,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC9B,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KACvB;IAED,qDAAqD;IACrD,wBAAwB;IACxB,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,oEAAoE,EAAE;QAC/F,QAAQ,GAAG,IAAI,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC9D;AACL,CAAC;AAED,MAAM,MAAO,SAAQ,QAAQ;IACzB;QACI,KAAK,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,OAAO,CAAC,KAAa;QACjB,SAAS,CAAC,IAAI,CAAC,CAAC;QAChB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED,YAAY,CAAC,IAAY;QACrB,SAAS,CAAC,IAAI,CAAC,CAAC;QAChB,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,QAAgB;QAClB,MAAM,CAAC,cAAc,EAAE,CAAC;QACxB,OAAO,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,CAAC,KAAoB;QACrB,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;CACJ;AAED,MAAM,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;AAC5B,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAE1B,OAAO,EAAE,MAAM,EAAE,CAAC"}