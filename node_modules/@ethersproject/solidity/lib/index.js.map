{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;AAEb,sDAAqD;AACrD,8CAA0E;AAC1E,sDAAsE;AACtE,4CAA2D;AAC3D,kDAAqD;AAErD,IAAM,UAAU,GAAG,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACjD,IAAM,WAAW,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AACpD,IAAM,UAAU,GAAG,IAAI,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAEtD,IAAM,KAAK,GAAG,kEAAkE,CAAC;AAEjF,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAGnC,SAAS,KAAK,CAAC,IAAY,EAAE,KAAU,EAAE,OAAiB;IACtD,QAAO,IAAI,EAAE;QACT,KAAK,SAAS;YACV,IAAI,OAAO,EAAE;gBAAE,OAAO,IAAA,eAAO,EAAC,KAAK,EAAE,EAAE,CAAC,CAAC;aAAE;YAC3C,OAAO,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;QAC3B,KAAK,QAAQ;YACT,OAAO,IAAA,qBAAW,EAAC,KAAK,CAAC,CAAC;QAC9B,KAAK,OAAO;YACR,OAAO,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;QAC3B,KAAK,MAAM;YACP,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,MAAM,CAAC,CAAC;YACjC,IAAI,OAAO,EAAE;gBAAE,OAAO,IAAA,eAAO,EAAC,KAAK,EAAE,EAAE,CAAC,CAAC;aAAE;YAC3C,OAAO,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;KAC9B;IAED,IAAI,KAAK,GAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACrC,IAAI,KAAK,EAAE;QACP,mCAAmC;QACnC,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAA;QAEtC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,GAAG,EAAE;YACzF,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;SACjE;QAED,IAAI,OAAO,EAAE;YAAE,IAAI,GAAG,GAAG,CAAC;SAAE;QAE5B,KAAK,GAAG,qBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE3C,OAAO,IAAA,eAAO,EAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;KACnC;IAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/B,IAAI,KAAK,EAAE;QACP,IAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,EAAE,EAAE;YACtD,MAAM,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;SAChE;QACD,IAAI,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC,UAAU,KAAK,IAAI,EAAE;YACrC,MAAM,CAAC,kBAAkB,CAAC,uBAAsB,IAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;SAC3E;QACD,IAAI,OAAO,EAAE;YAAE,OAAO,IAAA,gBAAQ,EAAC,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SAAE;QACnE,OAAO,KAAK,CAAC;KAChB;IAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/B,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC/B,IAAM,UAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACzD,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;YACvB,MAAM,CAAC,kBAAkB,CAAC,8BAA6B,IAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;SAClF;QACD,IAAM,QAAM,GAAsB,EAAE,CAAC;QACrC,KAAK,CAAC,OAAO,CAAC,UAAS,KAAK;YACxB,QAAM,CAAC,IAAI,CAAC,KAAK,CAAC,UAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QACH,OAAO,IAAA,cAAM,EAAC,QAAM,CAAC,CAAC;KACzB;IAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;AAClE,CAAC;AAED,oBAAoB;AAEpB,SAAgB,IAAI,CAAC,KAA4B,EAAE,MAA0B;IACzE,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;QAC/B,MAAM,CAAC,kBAAkB,CAAC,oDAAoD,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;KACpG;IACD,IAAM,KAAK,GAAsB,EAAE,CAAC;IACpC,KAAK,CAAC,OAAO,CAAC,UAAS,IAAI,EAAE,KAAK;QAC9B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IACH,OAAO,IAAA,eAAO,EAAC,IAAA,cAAM,EAAC,KAAK,CAAC,CAAC,CAAC;AAClC,CAAC;AATD,oBASC;AAED,SAAgB,SAAS,CAAC,KAA4B,EAAE,MAA0B;IAC9E,OAAO,IAAA,qBAAa,EAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC9C,CAAC;AAFD,8BAEC;AAED,SAAgB,MAAM,CAAC,KAA4B,EAAE,MAA0B;IAC3E,OAAO,IAAA,aAAU,EAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC3C,CAAC;AAFD,wBAEC"}