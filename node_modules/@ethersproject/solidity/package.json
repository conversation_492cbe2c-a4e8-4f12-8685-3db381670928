{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/keccak256": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/sha2": "^5.8.0", "@ethersproject/strings": "^5.8.0"}, "description": "Solidity coder for non-standard (tight) packing.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/solidity", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/solidity", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x2571c0d023ffec705de15218d33ec2cc58b02564e4ce09eaff83176af37ca5a6", "types": "./lib/index.d.ts", "version": "5.8.0"}