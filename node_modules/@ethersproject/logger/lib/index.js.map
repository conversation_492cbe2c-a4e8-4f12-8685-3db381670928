{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;AAEb,IAAI,sBAAsB,GAAG,KAAK,CAAC;AACnC,IAAI,aAAa,GAAG,KAAK,CAAC;AAE1B,IAAM,SAAS,GAAiC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAClH,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;AAErC,uCAAqC;AAErC,IAAI,aAAa,GAAW,IAAI,CAAC;AAEjC,SAAS,eAAe;IACpB,IAAI;QACA,IAAM,SAAO,GAAkB,EAAG,CAAC;QAEnC,qDAAqD;QACrD,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;YACxC,IAAI;gBACA,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;oBACnC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;iBACpC;gBAAA,CAAC;aACL;YAAC,OAAM,KAAK,EAAE;gBACX,SAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtB;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,SAAO,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,UAAU,GAAG,SAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACpD;QAED,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;YAClF,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;SAC3C;KACJ;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,KAAK,CAAC,OAAO,CAAC;KACxB;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,IAAM,eAAe,GAAG,eAAe,EAAE,CAAC;AAE1C,IAAY,QAMX;AAND,WAAY,QAAQ;IAChB,2BAAkB,CAAA;IAClB,yBAAiB,CAAA;IACjB,+BAAoB,CAAA;IACpB,2BAAkB,CAAA;IAClB,uBAAgB,CAAA;AACpB,CAAC,EANW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAMnB;AAGD,IAAY,SAwGX;AAxGD,WAAY,SAAS;IAEjB,mBAAmB;IACnB,iBAAiB;IAEjB,gBAAgB;IAChB,4CAA+B,CAAA;IAE/B,kBAAkB;IAClB,gDAAmC,CAAA;IAEnC,wBAAwB;IACxB,gBAAgB;IAChB,4DAA+C,CAAA;IAE/C,qEAAqE;IACrE,+EAA+E;IAC/E,4CAA+B,CAAA;IAE/B,4CAA4C;IAC5C,0CAA6B,CAAA;IAE7B,UAAU;IACV,gCAAmB,CAAA;IAEnB,mBAAmB;IACnB,sBAAsB;IAEtB,iBAAiB;IACjB,8CAAiC,CAAA;IAEjC,gBAAgB;IAChB,8CAA8C;IAC9C,qCAAqC;IACrC,4CAA+B,CAAA;IAG/B,mBAAmB;IACnB,kBAAkB;IAElB,oCAAoC;IACpC,iCAAiC;IACjC,wCAA2B,CAAA;IAE3B,yEAAyE;IACzE,mDAAmD;IACnD,uCAAuC;IACvC,kDAAqC,CAAA;IAErC,kCAAkC;IAClC,8CAA8C;IAC9C,sDAAsD;IACtD,kDAAqC,CAAA;IAErC,qBAAqB;IACrB,8CAA8C;IAC9C,sDAAsD;IACtD,wDAA2C,CAAA;IAG3C,mBAAmB;IACnB,oBAAoB;IAEpB,iBAAiB;IACjB,kCAAkC;IAClC,oCAAoC;IACpC,mDAAmD;IACnD,4CAA4C;IAC5C,iDAAiD;IACjD,6CAA6C;IAC7C,0DAA0D;IAC1D,8CAAiC,CAAA;IAEjC,qDAAqD;IACrD,6CAA6C;IAC7C,sDAAyC,CAAA;IAEzC,8BAA8B;IAC9B,6CAA6C;IAC7C,4CAA+B,CAAA;IAE/B,qDAAqD;IACrD,6CAA6C;IAC7C,gEAAmD,CAAA;IAEnD,uCAAuC;IACvC,yDAAyD;IACzD,gEAAmD,CAAA;IAEnD,8DAA8D;IAC9D,oDAAoD;IACpD,wEAAwE;IACxE,sCAAsC;IACtC,qEAAqE;IACrE,8CAA8C;IAC9C,0DAA6C,CAAA;IAG7C,mBAAmB;IACnB,qBAAqB;IAErB,qEAAqE;IACrE,gBAAgB;IAChB,gDAAmC,CAAA;AACvC,CAAC,EAxGW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAwGpB;AAAA,CAAC;AAEF,IAAM,GAAG,GAAG,kBAAkB,CAAC;AAE/B;IAOI,gBAAY,OAAe;QACvB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;YACnC,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,KAAK;SAClB,CAAC,CAAC;IACP,CAAC;IAED,qBAAI,GAAJ,UAAK,QAAkB,EAAE,IAAgB;QACrC,IAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QACrC,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;YAC1B,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;SAC3E;QACD,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE;YAAE,OAAO;SAAE;QAC7C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,sBAAK,GAAL;QAAM,cAAmB;aAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;YAAnB,yBAAmB;;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,qBAAI,GAAJ;QAAK,cAAmB;aAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;YAAnB,yBAAmB;;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,qBAAI,GAAJ;QAAK,cAAmB;aAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;YAAnB,yBAAmB;;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED,0BAAS,GAAT,UAAU,OAAe,EAAE,IAAgB,EAAE,MAAY;QACrD,4BAA4B;QAC5B,IAAI,aAAa,EAAE;YACf,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,EAAE,EAAG,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,IAAI,EAAE;YAAE,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;SAAE;QAClD,IAAI,CAAC,MAAM,EAAE;YAAE,MAAM,GAAG,EAAE,CAAC;SAAE;QAE7B,IAAM,cAAc,GAAkB,EAAE,CAAC;QACzC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAC5B,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI;gBACA,IAAI,KAAK,YAAY,UAAU,EAAE;oBAC7B,IAAI,GAAG,GAAG,EAAE,CAAC;oBACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACrC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC1B,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;qBAC7B;oBACD,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,gBAAgB,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;iBAC3D;qBAAM;oBACH,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC1D;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;aAC3E;QACL,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,IAAI,CAAC,UAAS,IAAO,CAAC,CAAC;QACtC,cAAc,CAAC,IAAI,CAAC,aAAY,IAAI,CAAC,OAAU,CAAC,CAAC;QAEjD,IAAM,MAAM,GAAG,OAAO,CAAC;QAEvB,IAAI,GAAG,GAAG,EAAE,CAAC;QAEb,QAAQ,IAAI,EAAE;YACV,KAAK,SAAS,CAAC,aAAa,CAAC,CAAC;gBAC1B,GAAG,GAAG,eAAe,CAAC;gBACtB,IAAM,KAAK,GAAG,OAAO,CAAC;gBAEtB,QAAQ,KAAK,EAAE;oBACX,KAAK,UAAU,CAAC;oBAAC,KAAK,WAAW,CAAC;oBAAC,KAAK,kBAAkB;wBACtD,GAAG,IAAI,GAAG,GAAG,KAAK,CAAC;wBACnB,MAAM;oBACV,KAAK,gBAAgB,CAAC;oBAAC,KAAK,gBAAgB;wBACxC,GAAG,IAAI,cAAc,CAAC;wBACtB,MAAM;oBACV,KAAK,wBAAwB;wBACzB,GAAG,IAAI,iBAAiB,CAAC;wBACzB,MAAM;iBACb;gBACD,MAAM;aACT;YACD,KAAK,SAAS,CAAC,cAAc,CAAC;YAC9B,KAAK,SAAS,CAAC,kBAAkB,CAAC;YAClC,KAAK,SAAS,CAAC,WAAW,CAAC;YAC3B,KAAK,SAAS,CAAC,aAAa,CAAC;YAC7B,KAAK,SAAS,CAAC,uBAAuB,CAAC;YACvC,KAAK,SAAS,CAAC,oBAAoB,CAAC;YACpC,KAAK,SAAS,CAAC,uBAAuB;gBAClC,GAAG,GAAG,IAAI,CAAC;gBACX,MAAM;SACb;QAED,IAAI,GAAG,EAAE;YACL,OAAO,IAAI,8CAA8C,GAAG,GAAG,GAAG,IAAI,CAAC;SAC1E;QAED,IAAI,cAAc,CAAC,MAAM,EAAE;YACvB,OAAO,IAAI,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;SACrD;QAED,eAAe;QACf,IAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QACtC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACtB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAA;QAEjB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAS,GAAG;YACpC,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,2BAAU,GAAV,UAAW,OAAe,EAAE,IAAgB,EAAE,MAAY;QACtD,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;IAED,mCAAkB,GAAlB,UAAmB,OAAe,EAAE,IAAY,EAAE,KAAU;QACxD,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;YAC5D,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,KAAK;SACf,CAAC,CAAC;IACP,CAAC;IAED,uBAAM,GAAN,UAAO,SAAc,EAAE,OAAe,EAAE,IAAgB,EAAE,MAAY;QAClE,IAAI,CAAC,CAAC,SAAS,EAAE;YAAE,OAAO;SAAE;QAC5B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,+BAAc,GAAd,UAAe,SAAc,EAAE,OAAe,EAAE,IAAY,EAAE,KAAU;QACpE,IAAI,CAAC,CAAC,SAAS,EAAE;YAAE,OAAO;SAAE;QAC5B,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAED,+BAAc,GAAd,UAAe,OAAgB;QAC3B,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,GAAG,6CAA6C,CAAC;SAAE;QACjF,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,6CAA6C,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAChG,SAAS,EAAE,4BAA4B,EAAE,IAAI,EAAE,eAAe;aACjE,CAAC,CAAC;SACN;IACL,CAAC;IAED,gCAAe,GAAf,UAAgB,KAAa,EAAE,OAAgB;QAC3C,IAAI,OAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;YAAE,OAAO;SAAE;QAE3C,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,GAAG,gBAAgB,CAAC;SAAE;QAEpD,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,gBAAgB,EAAE;YACxC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE;gBAClD,SAAS,EAAE,kBAAkB;gBAC7B,KAAK,EAAE,mBAAmB;gBAC1B,KAAK,EAAE,KAAK;aACf,CAAC,CAAC;SACN;QAED,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE;gBAClD,SAAS,EAAE,kBAAkB;gBAC7B,KAAK,EAAE,aAAa;gBACpB,KAAK,EAAE,KAAK;aACf,CAAC,CAAC;SACN;IACL,CAAC;IAED,mCAAkB,GAAlB,UAAmB,KAAa,EAAE,aAAqB,EAAE,OAAgB;QACrE,IAAI,OAAO,EAAE;YACT,OAAO,GAAG,IAAI,GAAG,OAAO,CAAC;SAC5B;aAAM;YACH,OAAO,GAAG,EAAE,CAAC;SAChB;QAED,IAAI,KAAK,GAAG,aAAa,EAAE;YACvB,IAAI,CAAC,UAAU,CAAC,kBAAkB,GAAG,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;gBAC1E,KAAK,EAAE,KAAK;gBACZ,aAAa,EAAE,aAAa;aAC/B,CAAC,CAAC;SACN;QAED,IAAI,KAAK,GAAG,aAAa,EAAE;YACvB,IAAI,CAAC,UAAU,CAAC,oBAAoB,GAAG,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE;gBAC/E,KAAK,EAAE,KAAK;gBACZ,aAAa,EAAE,aAAa;aAC/B,CAAC,CAAC;SACN;IACL,CAAC;IAED,yBAAQ,GAAR,UAAS,MAAW,EAAE,IAAS;QAC3B,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,IAAI,IAAI,EAAE;YACrC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;SAClF;IACL,CAAC;IAED,8BAAa,GAAb,UAAc,MAAW,EAAE,IAAS;QAChC,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,IAAI,CAAC,UAAU,CACX,oCAAoC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,4BAA4B,EAC/F,MAAM,CAAC,MAAM,CAAC,qBAAqB,EACnC,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,CAC1C,CAAC;SACL;aAAM,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,IAAI,IAAI,EAAE;YAC5C,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;SAClF;IACL,CAAC;IAEM,mBAAY,GAAnB;QACI,IAAI,CAAC,aAAa,EAAE;YAAE,aAAa,GAAG,IAAI,MAAM,CAAC,kBAAO,CAAC,CAAC;SAAE;QAC5D,OAAO,aAAa,CAAC;IACzB,CAAC;IAEM,oBAAa,GAApB,UAAqB,UAAmB,EAAE,SAAmB;QACzD,IAAI,CAAC,UAAU,IAAI,SAAS,EAAE;YAC1B,IAAI,CAAC,YAAY,EAAE,CAAC,UAAU,CAAC,uCAAuC,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBACzG,SAAS,EAAE,eAAe;aAC7B,CAAC,CAAC;SACN;QAED,IAAI,sBAAsB,EAAE;YACxB,IAAI,CAAC,UAAU,EAAE;gBAAE,OAAO;aAAE;YAC5B,IAAI,CAAC,YAAY,EAAE,CAAC,UAAU,CAAC,4BAA4B,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC9F,SAAS,EAAE,eAAe;aAC7B,CAAC,CAAC;SACN;QAED,aAAa,GAAG,CAAC,CAAC,UAAU,CAAC;QAC7B,sBAAsB,GAAG,CAAC,CAAC,SAAS,CAAC;IACzC,CAAC;IAEM,kBAAW,GAAlB,UAAmB,QAAkB;QACjC,IAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;QAChD,IAAI,KAAK,IAAI,IAAI,EAAE;YACf,MAAM,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC,CAAC;YAC9D,OAAO;SACV;QACD,SAAS,GAAG,KAAK,CAAC;IACtB,CAAC;IAEM,WAAI,GAAX,UAAY,OAAe;QACvB,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAlPM,aAAM,GAAG,SAAS,CAAC;IAEnB,aAAM,GAAG,QAAQ,CAAC;IAiP7B,aAAC;CAAA,AAtPD,IAsPC;AAtPY,wBAAM"}