{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bignumber": "^5.8.0", "@ethersproject/constants": "^5.8.0", "@ethersproject/logger": "^5.8.0"}, "description": "Unit conversion functions for Ethereum.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers", "units", "conversion"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/units", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/units", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x409ade405321651163d8850b1ad4f45a7c03f6c671986c9f0dc60bb7b6363644", "types": "./lib/index.d.ts", "version": "5.8.0"}