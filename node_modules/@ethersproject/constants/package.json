{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bignumber": "^5.8.0"}, "description": "Common Ethereum constants used for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/constants", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/constants", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xceb0a58df6d69671e0ade33a4dd44b6e7380f5ba95bc3093326c1a2805e82b5b", "types": "./lib/index.d.ts", "version": "5.8.0"}