import { BigNumber } from "@ethersproject/bignumber";
declare const NegativeOne: BigNumber;
declare const Zero: BigNumber;
declare const One: BigNumber;
declare const Two: BigNumber;
declare const WeiPerEther: BigNumber;
declare const MaxUint256: BigNumber;
declare const MinInt256: BigNumber;
declare const MaxInt256: BigNumber;
export { NegativeOne, Zero, One, Two, <PERSON><PERSON><PERSON><PERSON><PERSON>, MaxUint256, MinInt256, MaxInt256, };
//# sourceMappingURL=bignumbers.d.ts.map