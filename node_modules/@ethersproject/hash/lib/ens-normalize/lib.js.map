{"version": 3, "file": "lib.js", "sourceRoot": "", "sources": ["../../src.ts/ens-normalize/lib.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;;;AAEH,kDAA0D;AAE1D,2CAAuC;AACvC,IAAM,CAAC,GAAG,IAAA,oBAAO,GAAE,CAAC;AAEpB,2CAAiF;AAEjF,sCAAsC;AAEtC,IAAM,KAAK,GAAG,IAAI,GAAG,CAAC,IAAA,8BAAiB,EAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,IAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAA,8BAAiB,EAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,IAAM,MAAM,GAAG,IAAA,4BAAe,EAAC,CAAC,CAAC,CAAC;AAClC,IAAM,UAAU,GAAG,IAAA,4BAAe,EAAC,CAAC,CAAC,CAAC;AACtC,oGAAoG;AAEpG,oBAAoB;AACpB,IAAM,MAAM,GAAG,IAAI,CAAC;AACpB,IAAM,UAAU,GAAG,IAAI,CAAC;AAExB,SAAS,UAAU,CAAC,IAAY;IAC5B,OAAO,IAAA,0BAAgB,EAAC,IAAI,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,WAAW,CAAC,GAAkB;IACnC,OAAO,GAAG,CAAC,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,IAAI,MAAM,EAAZ,CAAY,CAAC,CAAC;AAC1C,CAAC;AAED,SAAgB,wBAAwB,CAAC,IAAY;IACpD,KAAkB,UAAe,EAAf,KAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAf,cAAe,EAAf,IAAe,EAAE;QAA9B,IAAI,KAAK,SAAA;QACb,IAAI,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI;YACH,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1D,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;oBAC1B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;iBACpD;aACD;YACD,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,GAAG,IAAI,EAAT,CAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;gBAC5F,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;aAC3C;SACD;QAAC,OAAO,GAAG,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,qBAAkB,KAAK,YAAM,GAAG,CAAC,OAAS,CAAC,CAAC;SAC5D;KACD;IACD,OAAO,IAAI,CAAC;AACb,CAAC;AAjBD,4DAiBC;AAED,SAAgB,aAAa,CAAC,IAAY;IACzC,OAAO,wBAAwB,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;AAC/D,CAAC;AAFD,sCAEC;AAED,SAAS,SAAS,CAAC,IAAY,EAAE,YAAiD;IACjF,IAAI,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe;IACvD,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,OAAO,KAAK,CAAC,MAAM,EAAE;QACpB,IAAI,KAAK,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACV,MAAM,CAAC,IAAI,OAAX,MAAM,EAAS,YAAY,CAAC,KAAK,CAAC,EAAE;YACpC,SAAS;SACT;QACD,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACrB,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAClB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChB,SAAS;SACT;QACD,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACpB,SAAS;SACT;QACD,IAAI,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QACrB,IAAI,GAAG,EAAE;YACR,MAAM,CAAC,IAAI,OAAX,MAAM,EAAS,GAAG,EAAE;YACpB,SAAS;SACT;QACD,MAAM,IAAI,KAAK,CAAC,6BAA2B,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAI,CAAC,CAAC;KAC5E;IACD,OAAO,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,OAApB,MAAM,EAAkB,MAAM,EAAE,CAAC,CAAC;AACvE,CAAC;AAED,SAAS,GAAG,CAAC,CAAS;IAClB,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAkB,EAAE,KAAqB;;IACxE,IAAI,IAAI,GAAG,UAAU,CAAC;IACtB,IAAI,KAAK,CAAC;IACV,IAAI,KAAK,CAAC;IACV,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACrB,IAAI,KAAK;QAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,iCAAiC;;QAE7D,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,GAAG,MAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAb,CAAa,CAAC,0CAAE,IAAI,CAAC;QACpD,IAAI,CAAC,IAAI;2BAAQ;QACjB,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW;YAC3B,KAAK,GAAG,EAAE,CAAC;SACX;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,kBAAkB;YAC1C,IAAI,EAAE,KAAK,KAAK;+BAAQ;SACxB;QACD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,IAAI,IAAI,CAAC,IAAI,EAAE;YACd,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnB,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,MAAM;gBAAE,GAAG,EAAE,CAAC,CAAC,wBAAwB;SACtE;QACD,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,iCAAiC;YAClD,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,aAAa;YACpC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;gBAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,yDAAyD;YAClG,IAAI,KAAK;gBAAE,KAAK,CAAC,IAAI,OAAV,KAAK,EAAS,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,yBAAyB;YAC7E,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,WAAW;SAC7B;;IAnBF,OAAO,GAAG;;;;KAoBT;IACD,OAAO,KAAK,CAAC;AACd,CAAC"}