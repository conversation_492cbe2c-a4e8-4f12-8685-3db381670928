{"version": 3, "file": "ankr-provider.js", "sourceRoot": "", "sources": ["../src.ts/ankr-provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,yCAAkD;AAClD,iEAA6D;AAI7D,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAGnC,IAAM,aAAa,GAAG,kEAAkE,CAAC;AAEzF,SAAS,OAAO,CAAC,IAAY;IACzB,QAAQ,IAAI,EAAE;QACV,KAAK,WAAW;YACZ,OAAO,mBAAmB,CAAC;QAC/B,KAAK,SAAS;YACV,OAAO,2BAA2B,CAAC;QACvC,KAAK,SAAS;YACV,OAAO,2BAA2B,CAAC;QACvC,KAAK,QAAQ;YACT,OAAO,0BAA0B,CAAC;QACtC,KAAK,SAAS;YACV,OAAO,2BAA2B,CAAC;QAEvC,KAAK,OAAO;YACR,OAAO,uBAAuB,CAAC;QACnC,KAAK,UAAU;YACX,OAAO,8BAA8B,CAAC;QAE1C,KAAK,UAAU;YACX,OAAO,wBAAwB,CAAC;QACpC,KAAK,iBAAiB;YAClB,OAAO,gCAAgC,CAAC;QAC5C,KAAK,kBAAkB;YACnB,OAAO,gCAAgC,CAAC;QAE5C,KAAK,UAAU;YACX,OAAO,wBAAwB,CAAC;KACvC;IACD,OAAO,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC1E,CAAC;AAED;IAAkC,gCAAkB;IAApD;;IAgCA,CAAC;IA7BG,0CAAmB,GAAnB;QACI,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;IAC3C,CAAC;IAEM,sBAAS,GAAhB,UAAiB,MAAW;QACxB,IAAI,MAAM,IAAI,IAAI,EAAE;YAAE,OAAO,aAAa,CAAC;SAAE;QAC7C,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,mBAAM,GAAb,UAAc,OAAgB,EAAE,MAAW;QACvC,IAAI,MAAM,IAAI,IAAI,EAAE;YAAE,MAAM,GAAG,aAAa,CAAC;SAAE;QAC/C,IAAM,UAAU,GAAmB;YAC/B,SAAS,EAAE,IAAI;YACf,GAAG,EAAE,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YACnD,gBAAgB,EAAE,UAAC,OAAe,EAAE,GAAW;gBAC3C,IAAI,MAAM,CAAC,MAAM,KAAK,aAAa,EAAE;oBACjC,IAAA,+BAAmB,GAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC;SACJ,CAAC;QAEF,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;YAC9B,UAAU,CAAC,IAAI,GAAG,EAAE,CAAC;YACrB,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAA;SAC7C;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IACL,mBAAC;AAAD,CAAC,AAhCD,CAAkC,0CAAkB,GAgCnD;AAhCY,oCAAY"}