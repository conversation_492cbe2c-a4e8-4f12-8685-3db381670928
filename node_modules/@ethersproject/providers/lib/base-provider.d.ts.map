{"version": 3, "file": "base-provider.d.ts", "sourceRoot": "", "sources": ["../src.ts/base-provider.ts"], "names": [], "mappings": ";AAEA,OAAO,EACH,KAAK,EAAE,QAAQ,EAAE,qBAAqB,EAAE,SAAS,EAAE,MAAM,EAAE,iBAAiB,EAC5E,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,EACvF,MAAM,kCAAkC,CAAC;AAG1C,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAInE,OAAO,EAAc,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAC1E,OAAO,EAAE,UAAU,EAAgD,MAAM,2BAA2B,CAAC;AACrG,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAW1D,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAiHxC,qBAAa,KAAK;IACd,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC5B,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;IACvB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC;IAErB,gBAAgB,EAAE,MAAM,CAAA;IACxB,SAAS,EAAE,OAAO,CAAC;gBAEP,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO;IAS1D,IAAI,KAAK,IAAI,SAAS,CAQrB;IAED,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,IAAI,IAAI,IAAI,MAAM,CAIjB;IAED,IAAI,MAAM,IAAI,MAAM,CAYnB;IAED,QAAQ,IAAI,OAAO;CAGtB;AAED,MAAM,WAAW,WAAW;IAGxB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IAGtB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IAIzB,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAA;IAIjD,cAAc,IAAI,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;IAIzC,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;CAChD;AAED,MAAM,WAAW,WAAW;IACxB,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;IAClD,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;IACvD,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;CAC1D;AA6BD,MAAM,WAAW,MAAM;IACnB,GAAG,EAAE,MAAM,CAAC;IACZ,OAAO,EAAE,KAAK,CAAC;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;CACrD;AAmFD,qBAAa,QAAS,YAAW,WAAW;IACxC,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC;IAEhC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IAEzB,QAAQ,CAAC,gBAAgB,EAAE,IAAI,GAAG,MAAM,CAAC;IAGzC,gBAAgB,EAAE,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;gBAG9B,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,CAAC,EAAE,MAAM;IAO3F,gBAAgB,IAAI,OAAO,CAAC,OAAO,CAAC;IAmB9B,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAiCrE,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAMhF,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM;IA8DjD,UAAU,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAuC9C,SAAS,IAAI,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAkHnC,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC;IAkDjC,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;CAmB9C;AAMD,qBAAa,YAAa,SAAQ,QAAS,YAAW,WAAW;IAC7D,eAAe,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAClC,QAAQ,EAAE,OAAO,CAAC;IAElB,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IAEtB,SAAS,EAAE,SAAS,CAAC;IAYrB,QAAQ,EAAE;QAAE,CAAE,SAAS,EAAE,MAAM,GAAI,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IAExD,gBAAgB,EAAE,MAAM,CAAC;IACzB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC;IACtB,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC;IAE7B,gBAAgB,EAAE,MAAM,CAAC;IACzB,oBAAoB,EAAE,MAAM,CAAC;IAE7B,gBAAgB,EAAE,MAAM,CAAC;IACzB,uBAAuB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACzC,cAAc,EAAE,MAAM,CAAC;IAEvB,uBAAuB,EAAE,MAAM,CAAC;IAChC,oBAAoB,EAAE,OAAO,CAAC;QAAE,WAAW,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IAE1F,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC;IAE7B,eAAe,EAAE,OAAO,CAAC;IAGzB;;;;;;;;OAQG;gBAES,OAAO,EAAE,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC;IAgD5C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC;IAqChC,IAAI,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,CAY5B;IAGD,MAAM,CAAC,YAAY,IAAI,SAAS;IAQhC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,GAAG,OAAO;IAIzC,aAAa,CAAC,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IA0C7F,uBAAuB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAwExD,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAgK3B,gBAAgB,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAK3C,IAAI,OAAO,IAAI,OAAO,CAErB;IAIK,aAAa,IAAI,OAAO,CAAC,OAAO,CAAC;IAMjC,UAAU,IAAI,OAAO,CAAC,OAAO,CAAC;IA6CpC,IAAI,WAAW,IAAI,MAAM,CAMxB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,EAyBzB;IAED,IAAI,eAAe,IAAI,MAAM,CAE5B;IAED,IAAI,eAAe,CAAC,KAAK,EAAE,MAAM,EAWhC;IAED,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC;IAiBtC,mBAAmB,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAcxC,kBAAkB,CAAC,eAAe,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAIlH,mBAAmB,CAAC,eAAe,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,EAAE,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,SAAS,CAAC;QAAC,UAAU,EAAE,MAAM,CAAA;KAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAsI/N,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC;IAIjC,WAAW,IAAI,OAAO,CAAC,SAAS,CAAC;IAcjC,UAAU,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC;IAkBhH,mBAAmB,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAkBtH,OAAO,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAkB1G,YAAY,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAmBrK,gBAAgB,CAAC,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,mBAAmB;IA8CpF,eAAe,CAAC,iBAAiB,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAgB1F,sBAAsB,CAAC,WAAW,EAAE,UAAU,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC;IAgCzF,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,iBAAiB,GAAG,OAAO,CAAC,MAAM,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,iBAAiB,CAAC;IAsBzH,KAAK,CAAC,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IA2F5F,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,kBAAkB,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAU3G,WAAW,CAAC,WAAW,EAAE,UAAU,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC;IAiB5E,WAAW,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAerE,SAAS,CAAC,mBAAmB,EAAE,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,qBAAqB,CAAC;IA6E3J,QAAQ,CAAC,mBAAmB,EAAE,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;IAI7F,wBAAwB,CAAC,mBAAmB,EAAE,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC;IAIvH,cAAc,CAAC,eAAe,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAkCvF,qBAAqB,CAAC,eAAe,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAsC7F,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,iBAAiB,GAAG,OAAO,CAAC,MAAM,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAUtG,aAAa,IAAI,OAAO,CAAC,MAAM,CAAC;IAKhC,YAAY,CAAC,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC;IAkBvE,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC;IA4BnD,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IA4B/D,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAsBnE,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IAqBxE,SAAS,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;IA6C9D,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAIlD,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAI/B,UAAU,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAI9B,iBAAiB,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI;IAQhF,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAIlD,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAKpD,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO;IA4BxD,aAAa,CAAC,SAAS,CAAC,EAAE,SAAS,GAAG,MAAM;IAS5C,SAAS,CAAC,SAAS,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;IAWjD,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,IAAI;IAuBpD,kBAAkB,CAAC,SAAS,CAAC,EAAE,SAAS,GAAG,IAAI;CAmBlD"}