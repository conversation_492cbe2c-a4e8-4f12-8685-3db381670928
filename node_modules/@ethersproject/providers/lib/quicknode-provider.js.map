{"version": 3, "file": "quicknode-provider.js", "sourceRoot": "", "sources": ["../src.ts/quicknode-provider.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;AAGb,iEAA6D;AAE7D,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,sDAAsD;AACtD,IAAM,aAAa,GAAG,0CAA0C,CAAC;AAEjE;IAAuC,qCAAkB;IAAzD;;IAwEA,CAAC;IAtEU,2BAAS,GAAhB,UAAiB,MAAW;QACxB,IAAI,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;YACvC,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;SACjE;QACD,OAAO,MAAM,IAAI,aAAa,CAAC;IACnC,CAAC;IAEM,wBAAM,GAAb,UAAc,OAAgB,EAAE,MAAY;QACxC,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,QAAQ,OAAO,CAAC,IAAI,EAAE;YAClB,KAAK,WAAW;gBACZ,IAAI,GAAG,qBAAqB,CAAC;gBAC7B,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,GAAG,qCAAqC,CAAC;gBAC7C,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,GAAG,sCAAsC,CAAC;gBAC9C,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,GAAG,sCAAsC,CAAC;gBAC9C,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,sCAAsC,CAAC;gBAC9C,MAAM;YACV,KAAK,iBAAiB;gBAClB,IAAI,GAAG,qCAAqC,CAAC;gBAC7C,MAAM;YACV,KAAK,kBAAkB;gBACnB,IAAI,GAAG,sCAAsC,CAAC;gBAC9C,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,GAAG,kCAAkC,CAAC;gBAC1C,MAAM;YACV,KAAK,aAAa;gBACd,IAAI,GAAG,iCAAiC,CAAC;gBACzC,MAAM;YACV,KAAK,aAAa;gBACd,IAAI,GAAG,kCAAkC,CAAC;gBAC1C,MAAM;YACV,KAAK,KAAK;gBACN,IAAI,GAAG,yBAAyB,CAAC;gBACjC,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,GAAG,iCAAiC,CAAC;gBACzC,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,GAAG,2BAA2B,CAAC;gBACnC,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,mCAAmC,CAAC;gBAC3C,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,8BAA8B,CAAC;gBACtC,MAAM;YACV,KAAK,iBAAiB;gBAClB,IAAI,GAAG,qCAAqC,CAAC;gBAC7C,MAAM;YACV,KAAK,kBAAkB;gBACnB,IAAI,GAAG,sCAAsC,CAAC;gBAC9C,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,GAAG,0BAA0B,CAAC;gBAClC,MAAM;YACV;gBACG,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SAChF;QAED,OAAO,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC,CAAC;IACnD,CAAC;IACL,wBAAC;AAAD,CAAC,AAxED,CAAuC,0CAAkB,GAwExD;AAxEY,8CAAiB"}