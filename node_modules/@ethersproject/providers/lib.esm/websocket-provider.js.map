{"version": 3, "file": "websocket-provider.js", "sourceRoot": "", "sources": ["../src.ts/websocket-provider.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;AAEb,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AAErD,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAG3D,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AAEjC,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC;;;;;;;;;;;;;GAaG;AAEH,IAAI,MAAM,GAAG,CAAC,CAAC;AAuBf,mDAAmD;AACnD,8CAA8C;AAE9C,MAAM,OAAO,iBAAkB,SAAQ,eAAe;IAalD,YAAY,GAA2B,EAAE,OAAoB;QAEzD,qEAAqE;QACrE,IAAI,OAAO,KAAK,KAAK,EAAE;YACnB,MAAM,CAAC,UAAU,CAAC,sDAAsD,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC3G,SAAS,EAAE,aAAa;aAC3B,CAAC,CAAC;SACN;QAED,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;YAC1B,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;SACvB;aAAM;YACH,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAE3B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;YAC1B,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1E;aAAM;YACH,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;SAC3C;QAED,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,EAAG,CAAC,CAAC;QACvC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,EAAG,CAAC,CAAC;QACnC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,EAAG,CAAC,CAAC;QACrC,cAAc,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;QAE9D,qDAAqD;QACrD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE;YACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;gBACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,YAA8B,EAAE,EAAE;YAC1D,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,MAAM,CAAC,EAAE,IAAI,IAAI,EAAE;gBACnB,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBACnC,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAE1B,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;oBAC7B,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBAEtC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACf,MAAM,EAAE,UAAU;wBAClB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;wBACpC,QAAQ,EAAE,MAAM,CAAC,MAAM;wBACvB,QAAQ,EAAE,IAAI;qBACjB,CAAC,CAAC;iBAEN;qBAAM;oBACH,IAAI,KAAK,GAAU,IAAI,CAAC;oBACxB,IAAI,MAAM,CAAC,KAAK,EAAE;wBACd,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,eAAe,CAAC,CAAC;wBAC3D,cAAc,CAAM,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;wBAC9D,cAAc,CAAM,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;qBAChD;yBAAM;wBACH,KAAK,GAAG,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;qBACtC;oBAED,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBAEnC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACf,MAAM,EAAE,UAAU;wBAClB,KAAK,EAAE,KAAK;wBACZ,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;wBACpC,QAAQ,EAAE,IAAI;qBACjB,CAAC,CAAC;iBAEN;aAEJ;iBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,kBAAkB,EAAE;gBAC7C,kBAAkB;gBAClB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,GAAG,EAAE;oBACL,2CAA2C;oBAC3C,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;iBACxC;aAEJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;aAC1C;QACL,CAAC,CAAC;QAEF,+DAA+D;QAC/D,gEAAgE;QAChE,iCAAiC;QACjC,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,IAAI,QAAQ,CAAC,KAAK,EAAE;YAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;SAAE;IAC7C,CAAC;IAED,4EAA4E;IAC5E,4DAA4D;IAC5D,IAAI,SAAS,KAAoB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAE1D,aAAa;QACT,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAI,eAAe;QACf,OAAO,CAAC,CAAC;IACb,CAAC;IAED,gBAAgB,CAAC,WAAmB;QAChC,MAAM,CAAC,UAAU,CAAC,gDAAgD,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACrG,SAAS,EAAE,iBAAiB;SAC/B,CAAC,CAAC;IACP,CAAC;IAED,IAAI,eAAe,CAAC,KAAa;QAC7B,MAAM,CAAC,UAAU,CAAC,kDAAkD,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACvG,SAAS,EAAE,oBAAoB;SAClC,CAAC,CAAC;IACP,CAAC;IAEK,IAAI;;YACN,OAAO,IAAI,CAAC;QAChB,CAAC;KAAA;IAED,IAAI,OAAO,CAAC,KAAc;QACtB,IAAI,CAAC,KAAK,EAAE;YAAE,OAAO;SAAE;QAEvB,MAAM,CAAC,UAAU,CAAC,yCAAyC,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;YAC9F,SAAS,EAAE,YAAY;SAC1B,CAAC,CAAC;IACP,CAAC;IAED,IAAI,CAAC,MAAc,EAAE,MAAmB;QACpC,MAAM,GAAG,GAAG,MAAM,EAAE,CAAC;QAErB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,SAAS,QAAQ,CAAC,KAAY,EAAE,MAAW;gBACvC,IAAI,KAAK,EAAE;oBAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;iBAAE;gBACpC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3B,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC3B,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;gBACd,EAAE,EAAE,GAAG;gBACP,OAAO,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC5B,QAAQ,EAAE,IAAI;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;YAEpD,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAAE;QACxD,CAAC,CAAC,CAAC;IACP,CAAC;IAED,MAAM,CAAC,UAAU;QACb,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAEK,UAAU,CAAC,GAAW,EAAE,KAAiB,EAAE,WAAkC;;YAC/E,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACrC,IAAI,YAAY,IAAI,IAAI,EAAE;gBACtB,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC7C,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;gBAC7C,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;aACpC;YACD,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC;QAC7C,CAAC;KAAA;IAED,WAAW,CAAC,KAAY;QACpB,QAAQ,KAAK,CAAC,IAAI,EAAE;YAChB,KAAK,OAAO;gBACR,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAE,UAAU,CAAE,EAAE,CAAC,MAAW,EAAE,EAAE;oBACrD,MAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC7D,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC;oBAClC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV,KAAK,SAAS;gBACV,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAE,wBAAwB,CAAE,EAAE,CAAC,MAAW,EAAE,EAAE;oBACrE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV,KAAK,QAAQ;gBACT,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,CAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAE,EAAE,CAAC,MAAW,EAAE,EAAE;oBAClF,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;wBAAE,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;qBAAE;oBACvD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV,KAAK,IAAI,CAAC,CAAC;gBACP,MAAM,WAAW,GAAG,CAAC,KAAY,EAAE,EAAE;oBACjC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;oBACxB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;wBAC9C,IAAI,CAAC,OAAO,EAAE;4BAAE,OAAO;yBAAE;wBACzB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC;gBAEF,8BAA8B;gBAC9B,WAAW,CAAC,KAAK,CAAC,CAAC;gBAEnB,oEAAoE;gBACpE,2DAA2D;gBAC3D,mEAAmE;gBACnE,gCAAgC;gBAChC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAE,UAAU,CAAE,EAAE,CAAC,MAAW,EAAE,EAAE;oBAClD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC;gBACH,MAAM;aACT;YAED,oBAAoB;YACpB,KAAK,OAAO,CAAC;YACb,KAAK,MAAM,CAAC;YACZ,KAAK,UAAU,CAAC;YAChB,KAAK,SAAS,CAAC;YACf,KAAK,OAAO;gBACR,MAAM;YAEV;gBACI,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBACjC,MAAM;SACb;IACL,CAAC;IAED,UAAU,CAAC,KAAY;QACnB,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;QAEpB,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE;YACrB,kDAAkD;YAClD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;gBACtD,OAAO;aACV;YACD,GAAG,GAAG,IAAI,CAAC;SACd;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACxC,sCAAsC;YACtC,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,KAAK,EAAE;YAAE,OAAO;SAAE;QAExB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACzB,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAAE,OAAO;aAAE;YACnC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAE,KAAK,CAAE,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;IAEK,OAAO;;YACT,2DAA2D;YAC3D,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU,EAAE;gBACpD,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;wBACpB,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC,CAAC;oBAEF,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG;wBACrB,OAAO,CAAC,KAAK,CAAC,CAAC;oBACnB,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC;aACP;YAED,SAAS;YACT,gFAAgF;YAChF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;KAAA;CACJ"}