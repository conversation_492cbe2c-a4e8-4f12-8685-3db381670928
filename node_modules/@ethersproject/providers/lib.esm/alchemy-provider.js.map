{"version": 3, "file": "alchemy-provider.js", "sourceRoot": "", "sources": ["../src.ts/alchemy-provider.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAGb,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAG3D,OAAO,EAAwB,mBAAmB,EAAE,MAAM,aAAa,CAAC;AACxE,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAEzD,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAE7D,kEAAkE;AAClE,4DAA4D;AAC5D,iEAAiE;AACjE,oCAAoC;AAEpC,MAAM,aAAa,GAAG,kCAAkC,CAAA;AAExD,MAAM,OAAO,wBAAyB,SAAQ,iBAAiB;IAG3D,YAAY,OAAoB,EAAE,MAAY;QAC1C,MAAM,QAAQ,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAEtD,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;aACvB,OAAO,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAE/E,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC7B,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IACpD,CAAC;IAED,mBAAmB;QACf,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;IAC3C,CAAC;CACJ;AAED,MAAM,OAAO,eAAgB,SAAQ,kBAAkB;IAEnD,MAAM,CAAC,oBAAoB,CAAC,OAAoB,EAAE,MAAY;QAC1D,OAAO,IAAI,wBAAwB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,MAAW;QACxB,IAAI,MAAM,IAAI,IAAI,EAAE;YAAE,OAAO,aAAa,CAAC;SAAE;QAC7C,IAAI,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;YACvC,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;SACjE;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,OAAgB,EAAE,MAAc;QAC1C,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,QAAQ,OAAO,CAAC,IAAI,EAAE;YAClB,KAAK,WAAW;gBACZ,IAAI,GAAG,+BAA+B,CAAC;gBACvC,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,GAAG,8BAA8B,CAAC;gBACtC,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,GAAG,+BAA+B,CAAC;gBACvC,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,GAAG,mCAAmC,CAAC;gBAC3C,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,kCAAkC,CAAC;gBAC1C,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,+BAA+B,CAAC;gBACvC,MAAM;YACV,KAAK,iBAAiB;gBAClB,IAAI,GAAG,8BAA8B,CAAC;gBACtC,MAAM;YACV,KAAK,kBAAkB;gBACnB,IAAI,GAAG,+BAA+B,CAAC;gBACvC,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,+BAA+B,CAAC;gBACvC,MAAM;YACV,KAAK,iBAAiB;gBAClB,IAAI,GAAG,8BAA8B,CAAA;gBACrC,MAAM;YACV,KAAK,kBAAkB;gBACnB,IAAI,GAAG,+BAA+B,CAAA;gBACtC,MAAM;YACV;gBACG,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SAChF;QAED,OAAO;YACH,SAAS,EAAE,IAAI;YACf,GAAG,EAAE,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC;YACtC,gBAAgB,EAAE,CAAC,OAAe,EAAE,GAAW,EAAE,EAAE;gBAC/C,IAAI,MAAM,KAAK,aAAa,EAAE;oBAC1B,mBAAmB,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC;SACJ,CAAC;IACN,CAAC;IAED,mBAAmB;QACf,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;IAC3C,CAAC;CACJ"}