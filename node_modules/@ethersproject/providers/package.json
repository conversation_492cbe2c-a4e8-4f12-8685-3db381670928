{"_ethers.alias": {"browser-net.d.ts": "", "browser-net.d.ts.map": "", "browser-net.js": "", "browser-net.js.map": "", "ipc-provider.js": "browser-ipc-provider.js", "ws.js": "browser-ws.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/ipc-provider": "./lib/browser-ipc-provider.js", "./lib/ws": "./lib/browser-ws.js", "net": "./lib/browser-net.js"}, "dependencies": {"@ethersproject/abstract-provider": "^5.8.0", "@ethersproject/abstract-signer": "^5.8.0", "@ethersproject/address": "^5.8.0", "@ethersproject/base64": "^5.8.0", "@ethersproject/basex": "^5.8.0", "@ethersproject/bignumber": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/constants": "^5.8.0", "@ethersproject/hash": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/networks": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/random": "^5.8.0", "@ethersproject/rlp": "^5.8.0", "@ethersproject/sha2": "^5.8.0", "@ethersproject/strings": "^5.8.0", "@ethersproject/transactions": "^5.8.0", "@ethersproject/web": "^5.8.0", "bech32": "1.1.4", "ws": "8.18.0"}, "description": "Ethereum Providers for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "f345816cf202fbaffa046bff7083c2c25d21f6cd", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/providers", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/providers", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x7f521bacebda7906da3c4bdb6102ae85d7fb94fe63b42972606d6201dbd92f96", "types": "./lib/index.d.ts", "version": "5.8.0"}