{"_ethers.alias": {"random.js": "browser-random.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/random": "./lib/browser-random.js"}, "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0"}, "description": "Random utility functions for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers", "random"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/random", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/random", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x88094cb66d862effe00b05222b1bf01944c95f89bbc5db88ad11e72ada20df9c", "types": "./lib/index.d.ts", "version": "5.8.0"}