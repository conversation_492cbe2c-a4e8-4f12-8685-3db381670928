{"version": 3, "file": "random.js", "sourceRoot": "", "sources": ["../src.ts/browser-random.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAEhD,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,iDAAiD;AACjD,4DAA4D;AAE5D,8FAA8F;AAC9F,SAAS,SAAS;IAChB,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAAE,OAAO,IAAI,CAAC;KAAE;IACjD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IACrD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IACrD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACpD,CAAC;AAAA,CAAC;AAEF,MAAM,SAAS,GAAG,SAAS,EAAE,CAAC;AAE9B,IAAI,MAAM,GAAQ,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC;AACzD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;IAEpC,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;IAE5D,MAAM,GAAG;QACL,eAAe,EAAE,UAAS,MAAkB;YACxC,OAAO,MAAM,CAAC,UAAU,CAAC,mCAAmC,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC/F,SAAS,EAAE,wBAAwB;aACtC,CAAC,CAAC;QACP,CAAC;KACJ,CAAC;CACL;AAED,MAAM,UAAU,WAAW,CAAC,MAAc;IACtC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,MAAM,EAAE;QAClE,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;KACjE;IAED,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IACtC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC/B,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC5B,CAAC;AAAA,CAAC"}