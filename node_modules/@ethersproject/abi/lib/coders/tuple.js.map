{"version": 3, "file": "tuple.js", "sourceRoot": "", "sources": ["../../src.ts/coders/tuple.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;AAEb,mDAAyD;AACzD,iCAAuC;AAEvC;IAAgC,8BAAK;IAGjC,oBAAY,MAAoB,EAAE,SAAiB;QAAnD,iBAWC;QAVG,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAM,KAAK,GAAkB,EAAE,CAAC;QAChC,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;YACjB,IAAI,KAAK,CAAC,OAAO,EAAE;gBAAE,OAAO,GAAG,IAAI,CAAC;aAAE;YACtC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QACH,IAAM,IAAI,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QAEhD,QAAA,kBAAM,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,SAAC;QACzC,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;IACzB,CAAC;IAED,iCAAY,GAAZ;QACI,IAAM,MAAM,GAAQ,EAAG,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;YACtB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,4DAA4D;QAC5D,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,KAAK,EAAE,KAAK;YAChD,IAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;YAC7B,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAAE;gBACtC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;aACjB;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,EAAgC,EAAG,CAAC,CAAC;QAEtC,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAC,KAAY,EAAE,KAAa;YAC5C,IAAI,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;YAC3B,IAAI,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAAE,OAAO;aAAE;YAEjD,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAAE,IAAI,GAAG,SAAS,CAAC;aAAE;YAE5C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;gBAAE,OAAO;aAAE;YAErC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,2BAAM,GAAN,UAAO,MAAc,EAAE,KAA6C;QAChE,OAAO,IAAA,YAAI,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,2BAAM,GAAN,UAAO,MAAc;QACjB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAA,cAAM,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACjE,CAAC;IACL,iBAAC;AAAD,CAAC,AAtDD,CAAgC,sBAAK,GAsDpC;AAtDY,gCAAU"}