{"version": 3, "file": "abstract-coder.js", "sourceRoot": "", "sources": ["../../src.ts/coders/abstract-coder.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;AAEb,8CAAuF;AACvF,sDAAmE;AACnE,wDAA2D;AAE3D,gDAA+C;AAC/C,wCAAsC;AACtC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAMnC,SAAgB,iBAAiB,CAAC,MAAc;IAC5C,gCAAgC;IAChC,IAAM,MAAM,GAA0D,EAAG,CAAC;IAE1E,IAAM,WAAW,GAAG,UAAS,IAA4B,EAAE,MAAW;QAClE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAAE,OAAO;SAAE;QACvC,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;YACpB,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEpB,IAAI;gBACC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aACxC;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;aAClD;SACJ;IACL,CAAC,CAAA;IACD,WAAW,CAAC,EAAG,EAAE,MAAM,CAAC,CAAC;IAEzB,OAAO,MAAM,CAAC;AAElB,CAAC;AArBD,8CAqBC;AAID;IAmBI,eAAY,IAAY,EAAE,IAAY,EAAE,SAAiB,EAAE,OAAgB;QACvE,8BAA8B;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAED,2BAAW,GAAX,UAAY,OAAe,EAAE,KAAU;QACnC,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IAML,YAAC;AAAD,CAAC,AAnCD,IAmCC;AAnCqB,sBAAK;AAqC3B;IAOI,gBAAY,QAAiB;QACzB,IAAA,2BAAc,EAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK,GAAG,EAAG,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED,sBAAI,wBAAI;aAAR;YACI,OAAO,IAAA,iBAAS,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;;;OAAA;IACD,sBAAI,0BAAM;aAAV,cAAuB,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;;OAAA;IAEjD,2BAAU,GAAV,UAAW,IAAgB;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,6BAAY,GAAZ,UAAa,MAAc;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAA,cAAM,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,kDAAkD;IAClD,2BAAU,GAAV,UAAW,KAAgB;QACvB,IAAI,KAAK,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;QAC5B,IAAM,aAAa,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;QACnD,IAAI,aAAa,EAAE;YACf,KAAK,GAAG,IAAA,cAAM,EAAC,CAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAE,CAAC,CAAA;SAChE;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED,0BAAS,GAAT,UAAU,KAAmB;QACzB,IAAI,KAAK,GAAG,IAAA,gBAAQ,EAAC,qBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5C,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE;gBACnE,MAAM,EAAE,IAAI,CAAC,QAAQ;gBACrB,MAAM,EAAE,KAAK,CAAC,MAAM;aACvB,CAAC,CAAC;SACN;QACD,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC9B,KAAK,GAAG,IAAA,cAAM,EAAC,CAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAE,CAAC,CAAC;SAChF;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,qDAAqD;IACrD,2BAAU,GAAV,UAAW,KAAmB;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,oCAAmB,GAAnB;QAAA,iBAOC;QANG,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC;QAClC,OAAO,UAAC,KAAmB;YACvB,KAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC;IACN,CAAC;IACL,aAAC;AAAD,CAAC,AAlED,IAkEC;AAlEY,wBAAM;AAoEnB;IASI,gBAAY,IAAe,EAAE,QAAiB,EAAE,UAAuB,EAAE,UAAoB;QACzF,IAAA,2BAAc,EAAC,IAAI,EAAE,OAAO,EAAE,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC,CAAC;QAC9C,IAAA,2BAAc,EAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;QACjD,IAAA,2BAAc,EAAC,IAAI,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QAChD,IAAA,2BAAc,EAAC,IAAI,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;QAE/C,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,sBAAI,wBAAI;aAAR,cAAqB,OAAO,IAAA,eAAO,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;;OAAA;IAClD,sBAAI,4BAAQ;aAAZ,cAAyB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;;OAAA;IAE/C,8BAA8B;IACvB,aAAM,GAAb,UAAc,IAAY,EAAE,KAAU;QAClC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC1C,IAAI,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE;YAAE,KAAK,GAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;SAAE;QACrE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,uBAAM,GAAN,UAAO,IAAY,EAAE,KAAU;QAC3B,IAAI,IAAI,CAAC,WAAW,EAAE;YAAE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAAE;QAC/D,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED,2BAAU,GAAV,UAAW,MAAc,EAAE,MAAc,EAAE,KAAe;QACtD,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;QACtE,IAAI,IAAI,CAAC,OAAO,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAClD,IAAI,IAAI,CAAC,UAAU,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACxE,aAAa,GAAG,MAAM,CAAC;aAC1B;iBAAM;gBACH,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,eAAM,CAAC,MAAM,CAAC,cAAc,EAAE;oBAClE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;oBACzB,MAAM,EAAE,IAAI,CAAC,OAAO,GAAG,aAAa;iBACvC,CAAC,CAAC;aACN;SACJ;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,CAAA;IACvE,CAAC;IAED,0BAAS,GAAT,UAAU,MAAc;QACpB,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACjH,CAAC;IAED,0BAAS,GAAT,UAAU,MAAc,EAAE,KAAe;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC;QAC7B,oDAAoD;QACpD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC;IAED,0BAAS,GAAT;QACI,OAAO,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACzD,CAAC;IACL,aAAC;AAAD,CAAC,AA9DD,IA8DC;AA9DY,wBAAM"}