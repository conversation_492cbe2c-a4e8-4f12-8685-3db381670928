"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Multi Token Standard https://eips.ethereum.org/EIPS/eip-1155
// prettier-ignore
const ABI = [
    { name: 'ApprovalForAll', type: 'event', inputs: [{ indexed: true, name: 'account', type: 'address' }, { indexed: true, name: 'operator', type: 'address' }, { indexed: false, name: 'approved', type: 'bool' },], }, { name: 'TransferBatch', type: 'event', inputs: [{ indexed: true, name: 'operator', type: 'address' }, { indexed: true, name: 'from', type: 'address' }, { indexed: true, name: 'to', type: 'address' }, { indexed: false, name: 'ids', type: 'uint256[]' }, { indexed: false, name: 'values', type: 'uint256[]' },], }, { name: 'TransferSingle', type: 'event', inputs: [{ indexed: true, name: 'operator', type: 'address' }, { indexed: true, name: 'from', type: 'address' }, { indexed: true, name: 'to', type: 'address' }, { indexed: false, name: 'id', type: 'uint256' }, { indexed: false, name: 'value', type: 'uint256' },], }, { name: 'URI', type: 'event', inputs: [{ indexed: false, name: 'value', type: 'string' }, { indexed: true, name: 'id', type: 'uint256' },], }, { name: 'balanceOf', type: 'function', inputs: [{ name: 'account', type: 'address' }, { name: 'id', type: 'uint256' },], outputs: [{ type: 'uint256' }], }, { name: 'balanceOfBatch', type: 'function', inputs: [{ name: 'accounts', type: 'address[]' }, { name: 'ids', type: 'uint256[]' },], outputs: [{ type: 'uint256[]' }], }, { name: 'isApprovedForAll', type: 'function', inputs: [{ name: 'account', type: 'address' }, { name: 'operator', type: 'address' },], outputs: [{ type: 'bool' }], }, { name: 'safeBatchTransferFrom', type: 'function', inputs: [{ name: 'from', type: 'address' }, { name: 'to', type: 'address' }, { name: 'ids', type: 'uint256[]' }, { name: 'amounts', type: 'uint256[]' }, { name: 'data', type: 'bytes' },], outputs: [], }, { name: 'safeTransferFrom', type: 'function', inputs: [{ name: 'from', type: 'address' }, { name: 'to', type: 'address' }, { name: 'id', type: 'uint256' }, { name: 'amount', type: 'uint256' }, { name: 'data', type: 'bytes' },], outputs: [], }, { name: 'setApprovalForAll', type: 'function', inputs: [{ name: 'operator', type: 'address' }, { name: 'approved', type: 'bool' },], outputs: [], }, { name: 'supportsInterface', type: 'function', inputs: [{ name: 'interfaceId', type: 'bytes4' }], outputs: [{ type: 'bool' }], }, { name: 'uri', type: 'function', inputs: [{ name: 'id', type: 'uint256' }], outputs: [{ type: 'string' }] }
];
exports.default = ABI;
//# sourceMappingURL=erc1155.js.map