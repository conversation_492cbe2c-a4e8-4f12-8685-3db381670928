{"version": 3, "file": "uniswap-v3.js", "sourceRoot": "", "sources": ["../src/net/uniswap-v3.ts"], "names": [], "mappings": ";;;AAuHA,wBAsFC;AA7MD,+CAAkD;AAClD,kDAAsE;AACtE,wDAAgG;AAChG,0CAAyD;AACzD,2CAA2C;AAE3C,MAAM,YAAY,GAAG,4CAA4C,CAAC;AAClE,MAAM,cAAc,GAAG,4CAA4C,CAAC;AACpE,MAAM,UAAU,GAAG;IACjB;QACE,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,iBAAiB;QACvB,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;YAC/B,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;SACtC;QACD,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;KAClD;IACD;QACE,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,uBAAuB;QAC7B,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;YACpC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;YACrC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;YAC/B,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;YACrC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;SAC/C;QACD,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;KAClD;IACD;QACE,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,kBAAkB;QACxB,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;YAC/B,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;SACvC;QACD,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;KACjD;IACD;QACE,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,wBAAwB;QAC9B,MAAM,EAAE;YACN,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;YACpC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;YACrC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;YAC/B,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;YACtC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;SAC/C;QACD,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;KACjD;CACO,CAAC;AAEE,QAAA,GAAG,GAA2B;IACzC,GAAG,EAAE,GAAG;IACR,MAAM,EAAE,IAAI;IACZ,IAAI,EAAE,KAAK;CACZ,CAAC;AAIF,SAAS,SAAS,CAAC,CAAS,EAAE,CAAS,EAAE,cAAuB,KAAK;IACnE,IAAI,GAAG,GAAY,EAAE,CAAC;IACtB,KAAK,IAAI,GAAG,IAAI,WAAG;QAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,WAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5D,MAAM,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/B,MAAM,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/B,MAAM,KAAK,GAA4C,GAAG,CAAC,YAAY,CAAC,MAAM,CAC5E,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,QAAQ,KAAK,EAAE,CACtB,CAAC;IAC7C,MAAM,OAAO,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,WAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACpE,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC;QACpB,KAAK,IAAI,IAAI,IAAI,WAAG,EAAE,CAAC;YACrB,KAAK,IAAI,IAAI,IAAI,WAAG,EAAE,CAAC;gBACrB,IAAI,IAAI,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iBAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3F,IAAI,WAAW;oBAAE,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBACvC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAA,mBAAW,EAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,KAAK,UAAU,QAAQ,CACrB,GAAkB,EAClB,CAAS,EACT,CAAS,EACT,QAAiB,EACjB,SAAkB;IAElB,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC;QACtD,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;IACjE,MAAM,MAAM,GAAG,IAAA,2BAAc,EAAC,UAAU,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IAC/D,IAAI,KAAK,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC;IACzC,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC;QACpB,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG;YAAE,SAAS;QAChC,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,CAAC,EAAE,CAAC;QACzF,MAAM,MAAM,GAAG,YAAY,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACzF,mBAAmB;QACnB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,GAAI,MAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7E,CAAC;IACD,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAQ,CAAC;IAClD,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC;IACvD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE,CAC5B,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CACvE,CAAC;IACF,IAAI,CAAC,KAAK,CAAC,MAAM;QAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAChE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,MAAM,eAAe,GAAG,IAAA,2BAAc,EAAC,uBAAiB,EAAE,SAAS,EAAE,0CAA0B,CAAC,CAAC;AAUjG,SAAgB,MAAM,CACpB,EAAU,EACV,KAAa,EACb,MAAc,EACd,KAAY,EACZ,QAAiB,EACjB,SAAkB,EAClB,MAAa,GAAG,CAAC,gBAAgB;IAYjC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,gBAAgB,EAAE,GAAG,GAAG,EAAE,CAAC;IAC1C,MAAM,GAAG,GAAG,cAAc,CAAC;IAC3B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,uBAAuB,CAAC,CAAC;IAC/E,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,wBAAwB,CAAC,CAAC;IACjF,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,oBAAoB,CAAC,CAAC;IACzE,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;QAC5C,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,gCAAgC,CAAC,CAAC;IAC1D,IAAI,KAAK,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK;QACrC,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,qCAAqC,CAAC,CAAC;IAC/D,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC;QACtD,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,oDAAoD,CAAC,CAAC;IAC9E,IACE,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;QAC9B,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QAC9B,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAE3B,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,eAAe,CAAC,CAAC;IACzC,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,iBAAiB;QACrC,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,sCAAsC,CAAC,CAAC;IAChE,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC/D,6DAA6D;IAC7D,MAAM,iBAAiB,GAAG,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACxD,2BAA2B;IAC3B,IAAI,IAAI,GAAG;QACT,GAAG,KAAK;QACR,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC;QAChC,QAAQ,EAAE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC;QAClC,SAAS,EAAE,iBAAiB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QAChD,QAAQ;QACR,QAAQ,EAAE,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAW;QAChD,SAAS,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAW;QACnD,iBAAiB,EAAE,GAAG,CAAC,iBAAiB,IAAI,MAAM,CAAC,CAAC,CAAC;QACrD,eAAe,EAAE,SAA+B;QAChD,gBAAgB,EAAE,SAA+B;KAClD,CAAC;IACF,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC;IAC1E,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC7E,MAAM,MAAM,GAAG,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAIjE,CAAC;IACxB,uBAAuB;IACvB,MAAM,SAAS,GAAG,CAAE,eAAe,CAAC,MAAM,CAAC,CAAC,WAA0C,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9F,IAAI,KAAK,KAAK,KAAK,IAAI,SAAS;QAAE,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;IAC7F,SAAS;IACT,IAAI,iBAAiB,EAAE,CAAC;QACtB,SAAS,CAAC,IAAI,CACX,eAAuB,CACtB,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAC/E,CAAC,WAAW,CAAC;YACZ,KAAK,EAAE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,aAAa,EAAE,IAAI,CAAC,gBAAgB;YACpC,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK;YACvC,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE;SACpC,CAAC,CACH,CAAC;IACJ,CAAC;IACD,MAAM,IAAI,GACR,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IAC9F,MAAM,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACzF,MAAM,SAAS,GACb,KAAK,KAAK,KAAK;QACb,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE;QACtE,CAAC,CAAC,SAAS,CAAC;IAChB,OAAO,EAAE,EAAE,EAAE,0CAA0B,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AACpE,CAAC;AAED,mDAAmD;AACnD,MAAqB,SAAU,SAAQ,GAAG,CAAC,eAAe;IAA1D;;QACE,SAAI,GAAG,YAAY,CAAC;QACpB,aAAQ,GAAW,0CAA0B,CAAC;IAkBhD,CAAC;IAjBC,QAAQ,CAAC,QAAgB,EAAE,MAAc,EAAE,WAAmB;QAC5D,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;IAC3D,CAAC;IACD,MAAM,CACJ,SAAiB,EACjB,QAAgB,EAChB,MAAc,EACd,IAAS,EACT,WAAoB,EACpB,YAAqB,EACrB,MAAmB,GAAG,CAAC,gBAAgB;QAEvC,OAAO,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE;YAC1E,GAAG,GAAG,CAAC,gBAAgB;YACvB,GAAG,GAAG;SACP,CAAC,CAAC;IACL,CAAC;CACF;AApBD,4BAoBC"}