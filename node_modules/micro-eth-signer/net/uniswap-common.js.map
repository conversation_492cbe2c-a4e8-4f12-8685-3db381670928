{"version": 3, "file": "uniswap-common.js", "sourceRoot": "", "sources": ["../src/net/uniswap-common.ts"], "names": [], "mappings": ";;;AAwBA,gCAIC;AAED,8BAGC;AAQD,8BAoCC;AAoBD,oCAGC;AAED,gCAKC;AAED,wCAEC;AAED,wCAEC;AAnHD,8CAAkD;AAClD,0CAAmC;AACnC,0CAAyF;AAG5E,QAAA,gBAAgB,GAAY,EAAE,eAAe,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAmBhF,SAAgB,UAAU,CAAC,CAAS,EAAE,KAAa;IACjD,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACzC,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACzC,OAAO,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AACpC,CAAC;AAED,SAAgB,SAAS,CAAC,CAAU;IAClC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAAE,OAAO,KAAK,CAAC;IACnE,OAAO,OAAQ,CAAS,CAAC,IAAI,KAAK,UAAU,CAAC;AAC/C,CAAC;AAQM,KAAK,UAAU,SAAS,CAC7B,CAAI,EACJ,aAAgB;IAEhB,IAAI,QAAQ,GAAmB,EAAE,CAAC;IAClC,MAAM,QAAQ,GAAG,CAAC,CAAM,EAAO,EAAE;QAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,IAAA,kBAAO,EAAC,CAAC,CAAC;YAAE,OAAO,CAAC,CAAC;QACzB,IAAI,SAAS,CAAC,CAAC,CAAC;YAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACzD,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC1B,IAAI,GAAG,GAAwB,EAAE,CAAC;YAClC,KAAK,IAAI,CAAC,IAAI,CAAC;gBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,OAAO,GAAG,CAAC;QACb,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;IACF,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtB,IAAI,MAAa,CAAC;IAClB,IAAI,CAAC,aAAa;QAAE,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACpD,CAAC;QACJ,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACtD,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAC/C,CAAC;IACJ,CAAC;IACD,MAAM,MAAM,GAAG,CAAC,CAAM,EAAO,EAAE;QAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,IAAI,IAAA,kBAAO,EAAC,CAAC,CAAC;YAAE,OAAO,CAAC,CAAC;QACzB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC1B,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,SAAS;gBAAE,OAAO,MAAM,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YACzE,IAAI,GAAG,GAAwB,EAAE,CAAC;YAClC,KAAK,IAAI,CAAC,IAAI,CAAC;gBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,OAAO,GAAG,CAAC;QACb,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;IACF,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC;AAKY,QAAA,YAAY,GAAiB;IACxC,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;CACP;KACE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,0BAAe,EAAC,CAAC,CAAC,CAAC;KAC9B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACT,QAAA,IAAI,GAAW,IAAA,0BAAe,EAAC,MAAM,CAAE,CAAC,QAAQ,CAAC;AAC9D,IAAI,CAAC,YAAI;IAAE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAEjD,SAAgB,YAAY,CAAC,QAAgB;IAC3C,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IAClC,OAAO,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,YAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;AAC9C,CAAC;AAED,SAAgB,UAAU,CAAC,CAAS,EAAE,CAAS;IAC7C,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IACpB,IAAI,CAAC,KAAK,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,CAAC;AAED,SAAgB,cAAc,CAAC,OAAe;IAC5C,OAAO,eAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC/B,CAAC;AAED,SAAgB,cAAc,CAAC,OAAe;IAC5C,OAAO,OAAO,KAAK,KAAK,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC;AACtD,CAAC;AAID,SAAS,QAAQ,CAAC,KAAoB;IACpC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,KAAK;QAC5D,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC1D,OAAO,KAAc,CAAC;AACxB,CAAC;AAED,MAAsB,eAAe;IAcnC,YAAY,GAAkB;QAC5B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IACD,4CAA4C;IAC5C,wCAAwC;IACxC,mEAAmE;IACnE,uDAAuD;IACvD,IAAI;IACJ,KAAK,CAAC,IAAI,CACR,QAAuB,EACvB,MAAqB,EACrB,MAAc,EACd,MAAe,wBAAgB;QAkB/B,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACpC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM;YAAE,OAAO;QACjC,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACrD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU;YAAE,OAAO;QACzC,MAAM,WAAW,GAAG,IAAA,wBAAa,EAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACrD,MAAM,SAAS,GAAG,IAAA,wBAAa,EAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;YACxE,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAmB,CAAC,CAAC;YAClE,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,cAAc;gBACd,EAAE,EAAE,KAAK,EAAE,YAAoB,EAAE,SAAiB,EAAE,EAAE;oBACpD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CACvB,SAAS,EACT,YAAY,EACZ,UAAU,EACV,IAAI,EACJ,WAAW,EACX,SAAS,EACT,GAAG,CACJ,CAAC;oBACF,OAAO;wBACL,MAAM,EAAE,iBAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;wBAClC,OAAO,EAAE,KAAK,CAAC,EAAE;wBACjB,cAAc;wBACd,IAAI,EAAE,iBAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;wBAC/B,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI;4BAC5B,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK;4BAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ;4BACvB,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC;yBACnD;qBACF,CAAC;gBACJ,CAAC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,aAAa;YACb,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACpB,OAAO;QACT,CAAC;IACH,CAAC;CACF;AAxFD,0CAwFC"}