{"version": 3, "file": "uniswap-v2.js", "sourceRoot": "", "sources": ["../../src/net/uniswap-v2.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAC9D,OAAO,EAAqB,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACtE,OAAO,EAAE,OAAO,IAAI,iBAAiB,EAAE,0BAA0B,EAAE,MAAM,sBAAsB,CAAC;AAChG,OAAO,EAAsB,MAAM,EAAE,MAAM,aAAa,CAAC;AACzD,OAAO,KAAK,GAAG,MAAM,qBAAqB,CAAC;AAE3C,MAAM,eAAe,GAAG,4CAA4C,CAAC;AACrE,MAAM,cAAc,GAAG,UAAU,CAC/B,kEAAkE,CACnE,CAAC;AACF,MAAM,aAAa,GAAG;IACpB;QACE,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE;YACP,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;YACrC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;YACrC,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAAE;SAC/C;KACF;CACO,CAAC;AAEX,MAAM,UAAU,OAAO,CAAC,IAAgB,EAAE,IAAgB,EAAE,YAAwB;IAClF,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;IACzE,OAAO,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AAClD,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,CAAS,EAAE,CAAS,EAAE,UAAkB,eAAe;IACjF,kGAAkG;IAClG,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/E,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,cAAc,CAAC,CAAC;AAC3E,CAAC;AAED,KAAK,UAAU,QAAQ,CAAC,GAAkB,EAAE,CAAS,EAAE,CAAS;IAC9D,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACxB,MAAM,QAAQ,GAAG,cAAc,CAAC,aAAa,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvE,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC7E,CAAC;AAED,oFAAoF;AACpF,iFAAiF;AACjF,sBAAsB;AACtB,MAAM,UAAU,MAAM,CACpB,SAAiB,EACjB,UAAkB,EAClB,QAAiB,EACjB,SAAkB;IAElB,IAAI,QAAQ,IAAI,SAAS;QAAE,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;IACtF,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,UAAU,CAAC;QACrE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACpD,IAAI,QAAQ,EAAE,CAAC;QACb,MAAM,eAAe,GAAG,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,SAAS,GAAG,CAAC,eAAe,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;QAChG,IAAI,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,IAAI,UAAU;YACpD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,OAAO,SAAS,CAAC;IACnB,CAAC;SAAM,IAAI,SAAS;QAClB,OAAO,CACL,CAAC,SAAS,GAAG,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAC9F,CAAC;;QACC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAClE,CAAC;AAID,KAAK,UAAU,QAAQ,CACrB,GAAkB,EAClB,MAAc,EACd,MAAc,EACd,QAAiB,EACjB,SAAkB;IAElB,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC;QACtD,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;IAC/D,MAAM,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACpC,MAAM,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACpC,IAAI,IAAI,GAAoB,EAAE,CAAC;IAC/B,cAAc;IACd,IAAI,CAAC,IAAI,CACP,CAAC,KAAK,IAAI,EAAE;QACV,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QACzF,OAAO;YACL,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YACd,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU;YAC1C,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;SAC9C,CAAC;IACJ,CAAC,CAAC,EAAE,CACL,CAAC;IACF,MAAM,KAAK,GAA4C,GAAG,CAAC,YAAY,CAAC,MAAM,CAC5E,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,QAAQ,KAAK,EAAE,CACtB,CAAC;IAC7C,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CACP,CAAC,KAAK,IAAI,EAAE;YACV,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACnC,QAAQ,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC;gBAC7B,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;aAC9B,CAAC,CAAC;YACH,MAAM,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAClC,IAAI,QAAQ;gBACV,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC;iBAC5E,IAAI,SAAS,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI;oBACJ,SAAS;oBACT,QAAQ,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;iBAC1E,CAAC;YACJ,CAAC;;gBAAM,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACjD,CAAC,CAAC,EAAE,CACL,CAAC;IACJ,CAAC;IACD,IAAI,GAAG,GAAY,CAAC,MAAM,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAS,CAAC,MAAM,CAAC,CAAC,CAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,mCAAmC;IACnC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3F,IAAI,CAAC,GAAG,CAAC,MAAM;QAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC9D,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC;AAED,MAAM,eAAe,GAAG,cAAc,CAAC,iBAAiB,EAAE,SAAS,EAAE,0BAA0B,CAAC,CAAC;AAEjG,MAAM,cAAc,GAAG;IACrB,GAAG,GAAG,CAAC,gBAAgB;IACvB,aAAa,EAAE,KAAK,EAAE,0BAA0B;CACjD,CAAC;AAEF,MAAM,UAAU,MAAM,CACpB,EAAU,EACV,KAAa,EACb,MAAc,EACd,IAAU,EACV,QAAiB,EACjB,SAAkB,EAClB,MAKI,cAAc;IAYlB,GAAG,GAAG,EAAE,GAAG,cAAc,EAAE,GAAG,GAAG,EAAE,CAAC;IACpC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;QACtF,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,IAAI,KAAK,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK;QAAE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;IAC1F,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI;QAC9C,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC1E,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI;QAClE,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IACzE,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC;QACtD,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;IAC7D,IAAI,SAAS,IAAI,GAAG,CAAC,aAAa;QAAE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAClG,MAAM,MAAM,GAAG,CAAC,MAAM;QACpB,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QACzB,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC;QACpC,KAAK;QACL,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1B,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC;QACrC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAiC,CAAC;IAC9F,IAAI,CAAC,CAAC,MAAM,IAAI,eAAe,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACpE,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC;IACvF,MAAM,WAAW,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC;IACvE,MAAM,YAAY,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC1E,mBAAmB;IACnB,MAAM,IAAI,GAAI,eAAuB,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC;QACxD,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,SAAS;QACT,EAAE;QACF,QAAQ;QACR,IAAI,EAAE,IAAI,CAAC,IAAI;KAChB,CAAC,CAAC;IACH,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC;IACjD,MAAM,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACnD,MAAM,SAAS,GAAG,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;IACzE,OAAO,EAAE,EAAE,EAAE,0BAA0B,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AACpE,CAAC;AAED,wHAAwH;AACxH,MAAM,CAAC,OAAO,OAAO,SAAU,SAAQ,GAAG,CAAC,eAAe;IAA1D;;QACE,SAAI,GAAG,YAAY,CAAC;QACpB,aAAQ,GAAW,0BAA0B,CAAC;IAkBhD,CAAC;IAjBC,QAAQ,CAAC,QAAgB,EAAE,MAAc,EAAE,WAAmB;QAC5D,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;IAC3D,CAAC;IACD,MAAM,CACJ,SAAiB,EACjB,QAAgB,EAChB,MAAc,EACd,IAAS,EACT,WAAoB,EACpB,YAAqB,EACrB,MAAmB,GAAG,CAAC,gBAAgB;QAEvC,OAAO,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE;YAC1E,GAAG,cAAc;YACjB,GAAG,GAAG;SACP,CAAC,CAAC;IACL,CAAC;CACF"}