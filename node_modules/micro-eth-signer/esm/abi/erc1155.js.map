{"version": 3, "file": "erc1155.js", "sourceRoot": "", "sources": ["../../src/abi/erc1155.ts"], "names": [], "mappings": "AAAA,+DAA+D;AAC/D,kBAAkB;AAClB,MAAM,GAAG,GAAG;IACV,EAAC,IAAI,EAAC,gBAAgB,EAAC,IAAI,EAAC,OAAO,EAAC,MAAM,EAAC,CAAC,EAAC,OAAO,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,OAAO,EAAC,IAAI,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,OAAO,EAAC,KAAK,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,MAAM,EAAC,EAAE,GAAE,EAAC,EAAC,IAAI,EAAC,eAAe,EAAC,IAAI,EAAC,OAAO,EAAC,MAAM,EAAC,CAAC,EAAC,OAAO,EAAC,IAAI,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,OAAO,EAAC,IAAI,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,OAAO,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,OAAO,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,WAAW,EAAC,EAAC,EAAC,OAAO,EAAC,KAAK,EAAC,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,WAAW,EAAC,EAAE,GAAE,EAAC,EAAC,IAAI,EAAC,gBAAgB,EAAC,IAAI,EAAC,OAAO,EAAC,MAAM,EAAC,CAAC,EAAC,OAAO,EAAC,IAAI,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,OAAO,EAAC,IAAI,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,OAAO,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,OAAO,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,OAAO,EAAC,KAAK,EAAC,IAAI,EAAC,OAAO,EAAC,IAAI,EAAC,SAAS,EAAC,EAAE,GAAE,EAAC,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,OAAO,EAAC,MAAM,EAAC,CAAC,EAAC,OAAO,EAAC,KAAK,EAAC,IAAI,EAAC,OAAO,EAAC,IAAI,EAAC,QAAQ,EAAC,EAAC,EAAC,OAAO,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,EAAE,GAAE,EAAC,EAAC,IAAI,EAAC,WAAW,EAAC,IAAI,EAAC,UAAU,EAAC,MAAM,EAAC,CAAC,EAAC,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,EAAE,EAAC,OAAO,EAAC,CAAC,EAAC,IAAI,EAAC,SAAS,EAAC,CAAC,GAAE,EAAC,EAAC,IAAI,EAAC,gBAAgB,EAAC,IAAI,EAAC,UAAU,EAAC,MAAM,EAAC,CAAC,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,WAAW,EAAC,EAAC,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,WAAW,EAAC,EAAE,EAAC,OAAO,EAAC,CAAC,EAAC,IAAI,EAAC,WAAW,EAAC,CAAC,GAAE,EAAC,EAAC,IAAI,EAAC,kBAAkB,EAAC,IAAI,EAAC,UAAU,EAAC,MAAM,EAAC,CAAC,EAAC,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,SAAS,EAAC,EAAE,EAAC,OAAO,EAAC,CAAC,EAAC,IAAI,EAAC,MAAM,EAAC,CAAC,GAAE,EAAC,EAAC,IAAI,EAAC,uBAAuB,EAAC,IAAI,EAAC,UAAU,EAAC,MAAM,EAAC,CAAC,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,WAAW,EAAC,EAAC,EAAC,IAAI,EAAC,SAAS,EAAC,IAAI,EAAC,WAAW,EAAC,EAAC,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,OAAO,EAAC,EAAE,EAAC,OAAO,EAAC,EAAE,GAAE,EAAC,EAAC,IAAI,EAAC,kBAAkB,EAAC,IAAI,EAAC,UAAU,EAAC,MAAM,EAAC,CAAC,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,IAAI,EAAC,QAAQ,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,OAAO,EAAC,EAAE,EAAC,OAAO,EAAC,EAAE,GAAE,EAAC,EAAC,IAAI,EAAC,mBAAmB,EAAC,IAAI,EAAC,UAAU,EAAC,MAAM,EAAC,CAAC,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,SAAS,EAAC,EAAC,EAAC,IAAI,EAAC,UAAU,EAAC,IAAI,EAAC,MAAM,EAAC,EAAE,EAAC,OAAO,EAAC,EAAE,GAAE,EAAC,EAAC,IAAI,EAAC,mBAAmB,EAAC,IAAI,EAAC,UAAU,EAAC,MAAM,EAAC,CAAC,EAAC,IAAI,EAAC,aAAa,EAAC,IAAI,EAAC,QAAQ,EAAC,CAAC,EAAC,OAAO,EAAC,CAAC,EAAC,IAAI,EAAC,MAAM,EAAC,CAAC,GAAE,EAAC,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,UAAU,EAAC,MAAM,EAAC,CAAC,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,SAAS,EAAC,CAAC,EAAC,OAAO,EAAC,CAAC,EAAC,IAAI,EAAC,QAAQ,EAAC,CAAC,EAAC;CACx8D,CAAC;AAEX,eAAe,GAAG,CAAC"}