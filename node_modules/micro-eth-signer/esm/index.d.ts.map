{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAGpC,OAAO,EACL,KAAK,iBAAiB,EACtB,KAAK,oBAAoB,EACzB,KAAK,OAAO,EACZ,KAAK,MAAM,EAQZ,MAAM,SAAS,CAAC;AAEjB,OAAO,EAMkB,MAAM,EAAE,OAAO,EACvC,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;AAIjC;;GAEG;AACH,eAAO,MAAM,aAAa;kBACV,oBAAoB,GAAG,UAAU;cAIrC,oBAAoB,cAAc,MAAM,GAAG,iBAAiB;uBAKnD,iBAAiB,GAAG,MAAM;CAO9C,CAAC;AAIF,QAAA,MAAM,WAAW;;;sBAGwB,MAAM;;uBAED,MAAM;mCACmB,MAAM;;CAEnE,CAAC;AACX,KAAK,YAAY,GAAG,MAAM,OAAO,WAAW,CAAC;AAC7C,KAAK,WAAW,GAAG,CAAC,OAAO,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AAChD,KAAK,gBAAgB,CAAC,CAAC,IAAI;KACxB,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CAC5D,GAAG;KACD,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,YAAY,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC3D,CAAC;AACF,KAAK,eAAe,CAAC,CAAC,SAAS,MAAM,IAAI,gBAAgB,CAAC;IAAE,IAAI,EAAE,CAAC,CAAA;CAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,KAAK,sBAAsB,GAAG,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;AACrE,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;AACnD,KAAK,UAAU,CAAC,CAAC,SAAS,MAAM,GAAG,SAAS,IAAI,CAAC,SAAS,SAAS,GAC/D,sBAAsB,GACtB,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AA0BjC,qBAAa,WAAW,CAAC,CAAC,SAAS,MAAM;IACvC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACjB,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IACzB,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC;gBAGf,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,UAAO,EAAE,oBAAoB,UAAO;IAOhF,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS;QAAE,IAAI,EAAE,SAAS,CAAA;KAAE,EAC1C,IAAI,EAAE,CAAC,GAAG,sBAAsB,EAChC,MAAM,CAAC,EAAE,OAAO,GACf,WAAW,CAAC,CAAC,OAAO,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;IAC5C,MAAM,CAAC,OAAO,CAAC,EAAE,SAAS,MAAM,EAAE,CAAC,SAAS;QAAE,IAAI,EAAE,EAAE,CAAA;KAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EACvE,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,EACpB,MAAM,CAAC,EAAE,OAAO,GACf,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAiBzB;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,cAAc,CAAC,cAAc,EAAE,MAAM,EAAE,aAAa,UAAO,GAAG,WAAW,CAAC,CAAC,CAAC;IAc5E,MAAM,CAAC,YAAY,CACjB,KAAK,EAAE,UAAU,EACjB,MAAM,UAAQ,GACb,WAAW,CAAC,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;IAIxE,MAAM,CAAC,OAAO,CACZ,GAAG,EAAE,MAAM,EACX,MAAM,UAAQ,GACb,WAAW,CAAC,SAAS,GAAG,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;IAGxE,OAAO,CAAC,cAAc;IAGtB;;;OAGG;IACH,UAAU,CAAC,gBAAgB,GAAE,OAAuB,GAAG,UAAU;IAUjE;;;OAGG;IACH,KAAK,CAAC,gBAAgB,GAAE,OAAuB,GAAG,MAAM;IAGxD,iFAAiF;IACjF,IAAI,IAAI,IAAI,MAAM,CAGjB;IACD,gCAAgC;IAChC,IAAI,MAAM,IAAI,MAAM,CAEnB;IACD;;OAEG;IACH,IAAI,CAAC,IAAI,MAAM,GAAG,SAAS,CAE1B;IACD,OAAO,CAAC,QAAQ;IAGhB,yDAAyD;IACzD,IAAI,GAAG,IAAI,MAAM,CAgBhB;IACD,KAAK,IAAI,WAAW,CAAC,CAAC,CAAC;IAGvB,eAAe,IAAI,OAAO;IAS1B,eAAe,IAAI,WAAW,CAAC,CAAC,CAAC;IAGjC;;;;;OAKG;IACH,MAAM,CACJ,UAAU,EAAE,MAAM,GAAG,UAAU,EAC/B,YAAY,GAAE,OAAO,GAAG,UAAiB,GACxC,WAAW,CAAC,CAAC,CAAC;IASjB,6EAA6E;IAC7E,aAAa,IAAI;QAAE,SAAS,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE;CASxD"}