/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-flat-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: {
        get "es-x"() {
            return require("../../index.js")
        },
    },
    rules: {
        "es-x/no-logical-assignment-operators": "error",
        "es-x/no-numeric-separators": "error",
        "es-x/no-promise-any": "error",
        "es-x/no-regexp-unicode-property-escapes-2021": "error",
        "es-x/no-string-prototype-replaceall": "error",
        "es-x/no-weakrefs": "error",
    },
}
