/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-flat-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: {
        get "es-x"() {
            return require("../../index.js")
        },
    },
    rules: {
        "es-x/no-intl-datetimeformat-prototype-formatrange": "error",
        "es-x/no-intl-displaynames": "error",
        "es-x/no-intl-listformat": "error",
    },
}
