/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-flat-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: {
        get "es-x"() {
            return require("../../index.js")
        },
    },
    rules: {
        ...require("./no-new-in-es2023-intl-api").rules,
        ...require("./no-new-in-es2022-intl-api").rules,
        ...require("./no-new-in-es2021-intl-api").rules,
        ...require("./no-new-in-es2020-intl-api").rules,
        ...require("./no-new-in-es2019-intl-api").rules,
        ...require("./no-new-in-es2018-intl-api").rules,
    },
}
