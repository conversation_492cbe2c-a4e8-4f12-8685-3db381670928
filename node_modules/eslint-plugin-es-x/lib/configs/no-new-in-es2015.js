/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: ["es-x"],
    rules: {
        "es-x/no-array-from": "error",
        "es-x/no-array-of": "error",
        "es-x/no-array-prototype-copywithin": "error",
        "es-x/no-array-prototype-entries": "error",
        "es-x/no-array-prototype-fill": "error",
        "es-x/no-array-prototype-find": "error",
        "es-x/no-array-prototype-findindex": "error",
        "es-x/no-array-prototype-keys": "error",
        "es-x/no-array-prototype-values": "error",
        "es-x/no-arrow-functions": "error",
        "es-x/no-binary-numeric-literals": "error",
        "es-x/no-block-scoped-functions": "error",
        "es-x/no-block-scoped-variables": "error",
        "es-x/no-classes": "error",
        "es-x/no-computed-properties": "error",
        "es-x/no-default-parameters": "error",
        "es-x/no-destructuring": "error",
        "es-x/no-for-of-loops": "error",
        "es-x/no-generators": "error",
        "es-x/no-map": "error",
        "es-x/no-math-acosh": "error",
        "es-x/no-math-asinh": "error",
        "es-x/no-math-atanh": "error",
        "es-x/no-math-cbrt": "error",
        "es-x/no-math-clz32": "error",
        "es-x/no-math-cosh": "error",
        "es-x/no-math-expm1": "error",
        "es-x/no-math-fround": "error",
        "es-x/no-math-hypot": "error",
        "es-x/no-math-imul": "error",
        "es-x/no-math-log10": "error",
        "es-x/no-math-log1p": "error",
        "es-x/no-math-log2": "error",
        "es-x/no-math-sign": "error",
        "es-x/no-math-sinh": "error",
        "es-x/no-math-tanh": "error",
        "es-x/no-math-trunc": "error",
        "es-x/no-modules": "error",
        "es-x/no-new-target": "error",
        "es-x/no-number-epsilon": "error",
        "es-x/no-number-isfinite": "error",
        "es-x/no-number-isinteger": "error",
        "es-x/no-number-isnan": "error",
        "es-x/no-number-issafeinteger": "error",
        "es-x/no-number-maxsafeinteger": "error",
        "es-x/no-number-minsafeinteger": "error",
        "es-x/no-number-parsefloat": "error",
        "es-x/no-number-parseint": "error",
        "es-x/no-object-assign": "error",
        "es-x/no-object-getownpropertysymbols": "error",
        "es-x/no-object-is": "error",
        "es-x/no-object-setprototypeof": "error",
        "es-x/no-object-super-properties": "error",
        "es-x/no-octal-numeric-literals": "error",
        "es-x/no-promise": "error",
        "es-x/no-property-shorthands": "error",
        "es-x/no-proxy": "error",
        "es-x/no-reflect": "error",
        "es-x/no-regexp-prototype-flags": "error",
        "es-x/no-regexp-u-flag": "error",
        "es-x/no-regexp-y-flag": "error",
        "es-x/no-rest-parameters": "error",
        "es-x/no-set": "error",
        "es-x/no-spread-elements": "error",
        "es-x/no-string-fromcodepoint": "error",
        "es-x/no-string-prototype-codepointat": "error",
        "es-x/no-string-prototype-endswith": "error",
        "es-x/no-string-prototype-includes": "error",
        "es-x/no-string-prototype-normalize": "error",
        "es-x/no-string-prototype-repeat": "error",
        "es-x/no-string-prototype-startswith": "error",
        "es-x/no-string-raw": "error",
        "es-x/no-subclassing-builtins": "error",
        "es-x/no-symbol": "error",
        "es-x/no-template-literals": "error",
        "es-x/no-typed-arrays": "error",
        "es-x/no-unicode-codepoint-escapes": "error",
        "es-x/no-weak-map": "error",
        "es-x/no-weak-set": "error",
    },
}
