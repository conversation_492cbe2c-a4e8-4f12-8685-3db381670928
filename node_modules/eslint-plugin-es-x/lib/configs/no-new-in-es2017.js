/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: ["es-x"],
    rules: {
        "es-x/no-async-functions": "error",
        "es-x/no-atomics": "error",
        "es-x/no-object-entries": "error",
        "es-x/no-object-getownpropertydescriptors": "error",
        "es-x/no-object-values": "error",
        "es-x/no-shared-array-buffer": "error",
        "es-x/no-string-prototype-padstart-padend": "error",
        "es-x/no-trailing-function-commas": "error",
    },
}
