/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: ["es-x"],
    rules: {
        "es-x/no-array-prototype-findlast-findlastindex": "error",
        "es-x/no-array-prototype-toreversed": "error",
        "es-x/no-array-prototype-tosorted": "error",
        "es-x/no-array-prototype-tospliced": "error",
        "es-x/no-array-prototype-with": "error",
        "es-x/no-hashbang": "error",
        "es-x/no-regexp-unicode-property-escapes-2023": "error",
    },
}
