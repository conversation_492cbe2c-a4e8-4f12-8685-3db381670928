/**
 * Chain configuration for the Keeper Reward Claimer system
 * 
 * This file defines the supported chains and their specific parameters
 */

module.exports = {
  // Default supported chains
  chains: [
    {
      name: 'Redstone',
      chainId: 7575,
      rpcUrl: process.env.REDSTONE_RPC_URL,
      explorerUrl: 'https://explorer.redstone.network',
      explorerApiUrl: 'https://api.explorer.redstone.network/api',
      explorerApiKey: process.env.REDSTONE_EXPLORER_API_KEY,
      nativeToken: {
        symbol: 'REDS',
        decimals: 18
      },
      gasMultiplier: 1.1,
      maxGasPrice: '100000000000', // 100 gwei
      blockTime: 2, // seconds
      confirmations: 2,
      dexRouters: [
        {
          name: '<PERSON>toneSwap',
          address: '******************************************', // Replace with actual address
          factoryAddress: '******************************************' // Replace with actual address
        }
      ]
    },
    {
      name: '<PERSON><PERSON>',
      chainId: 7777777,
      rpcUrl: process.env.ZORA_RPC_URL,
      explorerUrl: 'https://explorer.zora.energy',
      explorerApiUrl: 'https://api.explorer.zora.energy/api',
      explorerApiKey: process.env.ZORA_EXPLORER_API_KEY,
      nativeToken: {
        symbol: 'ETH',
        decimals: 18
      },
      gasMultiplier: 1.1,
      maxGasPrice: '100000000000', // 100 gwei
      blockTime: 2, // seconds
      confirmations: 2,
      dexRouters: [
        {
          name: 'ZoraSwap',
          address: '******************************************', // Replace with actual address
          factoryAddress: '******************************************' // Replace with actual address
        }
      ]
    },
    {
      name: 'Fraxtal',
      chainId: 252,
      rpcUrl: process.env.FRAXTAL_RPC_URL,
      explorerUrl: 'https://explorer.frax.com',
      explorerApiUrl: 'https://api.explorer.frax.com/api',
      explorerApiKey: process.env.FRAXTAL_EXPLORER_API_KEY,
      nativeToken: {
        symbol: 'frxETH',
        decimals: 18
      },
      gasMultiplier: 1.1,
      maxGasPrice: '100000000000', // 100 gwei
      blockTime: 2, // seconds
      confirmations: 2,
      dexRouters: [
        {
          name: 'FraxSwap',
          address: '******************************************', // Replace with actual address
          factoryAddress: '******************************************' // Replace with actual address
        }
      ]
    },
    {
      name: 'Scroll',
      chainId: 534352,
      rpcUrl: process.env.SCROLL_RPC_URL,
      explorerUrl: 'https://scrollscan.com',
      explorerApiUrl: 'https://api.scrollscan.com/api',
      explorerApiKey: process.env.SCROLL_EXPLORER_API_KEY,
      nativeToken: {
        symbol: 'ETH',
        decimals: 18
      },
      gasMultiplier: 1.1,
      maxGasPrice: '100000000000', // 100 gwei
      blockTime: 3, // seconds
      confirmations: 2,
      dexRouters: [
        {
          name: 'ScrollSwap',
          address: '******************************************', // Replace with actual address
          factoryAddress: '******************************************' // Replace with actual address
        }
      ]
    },
    {
      name: 'Celo',
      chainId: 42220,
      rpcUrl: process.env.CELO_RPC_URL,
      explorerUrl: 'https://explorer.celo.org',
      explorerApiUrl: 'https://api.explorer.celo.org/api',
      explorerApiKey: process.env.CELO_EXPLORER_API_KEY,
      nativeToken: {
        symbol: 'CELO',
        decimals: 18
      },
      gasMultiplier: 1.1,
      maxGasPrice: '100000000000', // 100 gwei
      blockTime: 5, // seconds
      confirmations: 2,
      dexRouters: [
        {
          name: 'UbeSwap',
          address: '******************************************', // Replace with actual address
          factoryAddress: '******************************************' // Replace with actual address
        }
      ]
    }
  ],

  // Function to get chain configuration by name
  getChainByName: function(name) {
    return this.chains.find(chain => chain.name.toLowerCase() === name.toLowerCase());
  },

  // Function to get chain configuration by ID
  getChainById: function(id) {
    return this.chains.find(chain => chain.chainId === id);
  },

  // Function to add a new chain configuration
  addChain: function(chainConfig) {
    // Validate required fields
    const requiredFields = ['name', 'chainId', 'rpcUrl', 'explorerUrl', 'nativeToken'];
    for (const field of requiredFields) {
      if (!chainConfig[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Check if chain already exists
    const existingChain = this.chains.find(
      chain => chain.chainId === chainConfig.chainId || 
              chain.name.toLowerCase() === chainConfig.name.toLowerCase()
    );

    if (existingChain) {
      throw new Error(`Chain already exists: ${chainConfig.name} (${chainConfig.chainId})`);
    }

    // Add the new chain
    this.chains.push(chainConfig);
    return chainConfig;
  },

  // Function to update an existing chain configuration
  updateChain: function(chainId, updates) {
    const chainIndex = this.chains.findIndex(chain => chain.chainId === chainId);
    if (chainIndex === -1) {
      throw new Error(`Chain not found: ${chainId}`);
    }

    // Update the chain configuration
    this.chains[chainIndex] = { ...this.chains[chainIndex], ...updates };
    return this.chains[chainIndex];
  },

  // Function to remove a chain configuration
  removeChain: function(chainId) {
    const chainIndex = this.chains.findIndex(chain => chain.chainId === chainId);
    if (chainIndex === -1) {
      throw new Error(`Chain not found: ${chainId}`);
    }

    // Remove the chain
    const removedChain = this.chains.splice(chainIndex, 1)[0];
    return removedChain;
  }
};